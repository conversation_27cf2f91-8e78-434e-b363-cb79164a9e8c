import csv
import os
import time

import pytest

from api.performance import ask_bi
from data import config

q_a_file = os.path.join(
    os.path.dirname(__file__), "..", "data", "csv", "sample", "question.csv"
)


# 读取 CSV 作为参数化数据
def load_test_cases_from_csv(file):
    with open(file, encoding="utf-8") as f:
        reader = csv.reader(f)
        next(reader, None)  # 跳过首行（表头）
        test_cases = []
        for row in reader:
            if not row or len(row) < 3:
                continue  # 跳过空行或字段不足
            scene_name, question, ground_truth = row[0], row[1], row[2]
            test_cases.append(
                (scene_name.strip(), question.strip(), ground_truth.strip())
            )
        return test_cases


@pytest.mark.parametrize(
    "project_name,scene_name,model_name",
    [
        (project, scene_name, model_name)
        for project, scene_dict in config.model.items()
        for scene_name, model_name in scene_dict.items()
    ],
)
# @pytest.mark.skip(reason="需要增加码值查询case再执行，先跳过")
def test_performance(front_client, project_name, scene_name, model_name):
    time.sleep(20)
    q_a_file = os.path.join(
        os.path.dirname(__file__), "..", "data", "csv", "sample", f"{scene_name}.csv"
    )
    # 如果你的 CSV 有多行问答，则逐行执行
    for sence, question, ground_truth in load_test_cases_from_csv(q_a_file):
        ask_bi(front_client, question, project_name, scene_name, ground_truth)
