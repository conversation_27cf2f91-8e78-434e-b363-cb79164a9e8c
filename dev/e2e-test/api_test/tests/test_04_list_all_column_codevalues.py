import pytest
import time
from data import config


@pytest.mark.parametrize(
    "model_name", [model_name for model_name in config.metric_model]
)
def test_list_all_column_codevalues(backend_client, model_name):
    substr = "Prague"
    flag = 0
    for i in range(50):
        time.sleep(1)
        url = (
            "/api/engine/v1/metricmodel/listAllColumnCodeValues?modelName=" + model_name
        )
        response = backend_client.get(url)
        for i in range(len(response["data"])):
            for j in range(len(response["data"][i]["codeValues"])):
                if substr in response["data"][i]["codeValues"][j]["codeValue"]:
                    flag = 1
                    break
            if flag == 1:
                break
        if flag == 1:
            break
    print(response)
    assert response["code"] == 0, f"码值查询失败: {response['msg']}"
    assert flag == 1, f"码值不存在: {substr}"
