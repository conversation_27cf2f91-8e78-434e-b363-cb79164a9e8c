import os
from datetime import datetime
from typing import Dict, List, Optional

from pydantic import BaseModel
from sqlalchemy import (
    DateTime,
    Integer,
    Text,
    create_engine,
    func,
    select,
    update,
    delete,
    J<PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    Enum,
)
from sqlalchemy import String
from sqlalchemy.dialects.mysql import TINYINT
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import DeclarativeBase, sessionmaker
from sqlalchemy.orm import Mapped
from sqlalchemy.orm import Session
from sqlalchemy.orm import mapped_column
from tenacity import retry, wait_fixed, stop_after_attempt

from common.fs.fs import get_s3_file_system
from config import app_config
from config import doc_config
from nl2document.common.index.document_index import (
    askdoc_doc_index_dir,
    askdoc_doc_resource_s3_dir,
)
from nl2document.common.index.document_index import askdoc_meta_data_db
from nl2document.common.msg.doc import (
    DocumentFilePb,
    DocumentFolderPb,
    FileStatus,
    get_file_status_name,
)

# TODO(bhx): config this, use same method as bi
if doc_config.ask_doc_use_mysql_engine:
    connection_string = f"mysql+pymysql://{doc_config.DB_USER}:{doc_config.DB_PASSWORD}@{doc_config.DB_HOST}:{doc_config.DB_PORT}/{doc_config.DB_NAME}"
else:
    connection_string = f"sqlite+pysqlite:///{askdoc_meta_data_db}"
engine = create_engine(connection_string, echo=False, pool_recycle=300)

# Configure the connection string for async database engine
if doc_config.ask_doc_use_mysql_engine:
    async_connection_string = f"mysql+aiomysql://{doc_config.DB_USER}:{doc_config.DB_PASSWORD}@{doc_config.DB_HOST}:{doc_config.DB_PORT}/{doc_config.DB_NAME}"
else:
    async_connection_string = f"sqlite+aiosqlite:///{askdoc_meta_data_db}"

# Create async engine and sessionmaker
async_engine = create_async_engine(
    async_connection_string, echo=False, pool_recycle=300
)
async_session: AsyncSession = sessionmaker(
    async_engine, expire_on_commit=False, class_=AsyncSession
)


class Base(DeclarativeBase):
    pass


class SceneInfo(Base):
    __tablename__ = "scene_info"
    scene_id: Mapped[str] = mapped_column(String(256), primary_key=True)
    # ready or unready
    scene_status: Mapped[str] = mapped_column(String(256))


class SceneFile(Base):
    __tablename__ = "scene_file"
    id: Mapped[int] = mapped_column(primary_key=True)
    scene_id: Mapped[str] = mapped_column(String(256))
    file_id: Mapped[str] = mapped_column(Integer)


class LibraryModel(Base):
    __tablename__ = "library"
    id: Mapped[int] = mapped_column(primary_key=True)
    name: Mapped[str] = mapped_column(String(256))


class FolderModel(Base):
    __tablename__ = "folder"
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    parent_folder_id: Mapped[int] = mapped_column(Integer)
    name: Mapped[str] = mapped_column(String(256))
    library_id: Mapped[int] = mapped_column(Integer)
    folder_status: Mapped[int] = mapped_column(Integer, default=0)

    @property
    def data_key(self):
        return f"folder_{self.id}"

    @property
    def index_dir(self):
        return os.path.join(askdoc_doc_index_dir, self.data_key)

    def to_pb(self) -> DocumentFolderPb:
        return DocumentFolderPb(
            id=self.id,
            parentFolderId=self.parent_folder_id,
            name=self.name,
            folders=[],
            files=[],
            folderStatus=get_file_status_name(
                self.folder_status if self.folder_status else 0
            ),
        )


ONE_DAY_SECONDS = 3600 * 24


class SuggestionQuestionModel(Base):
    __tablename__ = "suggestion_question"
    id: Mapped[int] = mapped_column(primary_key=True)
    question: Mapped[str] = mapped_column(Text)
    library_id: Mapped[int] = mapped_column(Integer)
    folder_id: Mapped[int] = mapped_column(Integer, nullable=True)
    doc_id: Mapped[int] = mapped_column(Integer, nullable=True)
    gmt_created: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), default=func.now(), nullable=False
    )
    gmt_modified: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), default=func.now(), onupdate=func.now(), nullable=False
    )


class UploadType(Enum, str):
    meeting = "meeting"
    personal = "personal"
    temp = "temp"


class CommonDocumentModel(BaseModel):
    # id for document and file_id for cmcc_files
    id: str
    # source_url for document and file_id for cmcc_files
    file_name: Optional[str]
    file_status: Optional[int] = -1
    source_url: Optional[str] = ""
    file_type: Optional[str] = ""
    # meeting personal temp
    upload_type: Optional[str] = ""
    platform: Optional[str] = ""
    x_location: Optional[str] = ""  # eb多环境
    # for index meta info, eg: {"file_id": "xxx", "meeting_id": "xxx"}
    meta_info: Optional[Dict] = {}
    upload_status: Optional[str] = ""

    app_id: Optional[str] = ""

    # 北京电信产品额外信息键
    product_extra_info_key: Optional[str] = ""

    @property
    def data_key(self):
        return f"doc_{self.id}"

    @property
    def index_dir(self):
        return os.path.join(askdoc_doc_index_dir, self.data_key)


class ChatRecordModel(Base):
    __tablename__ = "chat_record"

    id: Mapped[int] = mapped_column(
        Integer, primary_key=True, autoincrement=True, comment="主键id"
    )
    request_id: Mapped[str] = mapped_column(
        String(255), nullable=False, default="", comment="请求ID", unique=True
    )
    session_id: Mapped[str] = mapped_column(
        String(128), nullable=False, default="", comment="会话ID", index=True
    )
    user_id: Mapped[str] = mapped_column(
        String(128), nullable=False, default="", comment="用户ID", index=True
    )
    file_metas: Mapped[list[dict]] = mapped_column(
        JSON, nullable=True, comment="文件元数据"
    )
    question: Mapped[str] = mapped_column(Text, nullable=False, comment="问题")
    content: Mapped[str] = mapped_column(Text, nullable=False, comment="回答")
    new_query: Mapped[str] = mapped_column(Text, nullable=True, comment="新问题")
    has_exception: Mapped[bool] = mapped_column(
        Boolean, nullable=False, default=False, comment="是否有异常"
    )
    useful: Mapped[bool] = mapped_column(Boolean, nullable=True, comment="是否有用")
    expected_answer: Mapped[bool] = mapped_column(
        String(2048), nullable=True, comment="预期答案"
    )
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        default=func.now(),
        nullable=True,
        index=True,
        comment="创建对话时间",
    )


class DocumentModel(Base):
    __tablename__ = "document"
    id: Mapped[int] = mapped_column(primary_key=True)
    name: Mapped[str] = mapped_column(String(256))
    size: Mapped[int] = mapped_column(Integer, nullable=True, default=0)
    mime_type: Mapped[str] = mapped_column(String(64), nullable=True, default="")
    thumbnail_path: Mapped[str] = mapped_column(String(512), nullable=True, default="")
    source_path: Mapped[str] = mapped_column(String(512), nullable=True, default="")
    gmt_created: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), default=func.now(), nullable=False
    )
    gmt_modified: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), default=func.now(), onupdate=func.now(), nullable=False
    )
    file_type: Mapped[str] = mapped_column(String(64), nullable=True, default="")
    language: Mapped[str] = mapped_column(String(64))
    overview: Mapped[str] = mapped_column(Text, nullable=True, default="")
    file_status: Mapped[int] = mapped_column(Integer, default=FileStatus.Pending)
    md5: Mapped[str] = mapped_column(String(32), nullable=True, default="")
    creator: Mapped[str] = mapped_column(String(32), nullable=True, default="")

    # New columns from ALTER TABLE statements
    is_dir: Mapped[int] = mapped_column(
        TINYINT(1), nullable=False, default=0
    )  # 是否为目录（0否/1是）
    parse_result: Mapped[str] = mapped_column(
        String(256), nullable=False, default=""
    )  # 解析错误结果
    parse_error_msg: Mapped[str] = mapped_column(
        String(2048), nullable=False, default=""
    )  # 解析错误详细信息
    parent_id: Mapped[int] = mapped_column(
        Integer, nullable=False, default=-1
    )  # 父级目录id, 默认根目录

    @property
    def data_key(self):
        return f"doc_{self.id}"

    @property
    def index_dir(self):
        return os.path.join(askdoc_doc_index_dir, self.data_key)

    @property
    def resource_dir(self):
        return os.path.join(askdoc_doc_resource_s3_dir, self.data_key)

    @property
    def thumbnail_url(self):
        if not self.thumbnail_path:
            return ""
        return get_s3_file_system().url(
            self.thumbnail_path, expires=ONE_DAY_SECONDS * 2
        )

    @property
    def source_url(self):
        if not self.source_path:
            return ""
        return get_s3_file_system().url(self.source_path, expires=ONE_DAY_SECONDS * 2)

    def to_pb(self) -> DocumentFilePb:
        return DocumentFilePb(
            id=self.id,
            folderId=self.folder_id,
            name=self.name,
            mimeType=self.mime_type,
            size=self.size,
            thumbnailUrl=self.thumbnail_url,
            sourceUrl=self.source_url,
            createdTime=int(datetime.timestamp(self.gmt_created)),
            updatedTime=int(datetime.timestamp(self.gmt_modified)),
            overview=self.overview,
            fileStatus=get_file_status_name(
                self.file_status if self.file_status else 0
            ),
        )


class RequestStageInfo(Base):
    __tablename__ = "request_stage_info"
    request_id: Mapped[str] = mapped_column(
        String(255), primary_key=True, comment="请求ID"
    )
    request_stage: Mapped[str] = mapped_column(
        String(32),
        nullable=False,
        default="",
        comment="请求阶段 create: 任务已创建... "
        "nl2meeting_params: 开始获取资料... "
        "nl2document_retrieve_nodes: 已获取资料，加紧阅读中..."
        "nl2document_synthesize_answer: 正在合成结果... "
        "complete: 已完成",
    )
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), default=func.now(), nullable=False
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), default=func.now(), onupdate=func.now(), nullable=False
    )


# 插入记录
def insert_request_stage_info(request_id, request_stage):
    new_record = RequestStageInfo(request_id=request_id, request_stage=request_stage)
    with Session(engine) as session:
        session.add(new_record)
        session.commit()


# 查询记录
def get_request_stage_info(request_id):
    with Session(engine) as session:
        return session.query(RequestStageInfo).filter_by(request_id=request_id).first()


# 更新记录
@retry(stop=stop_after_attempt(3), wait=wait_fixed(1), reraise=True)
def insert_or_update_request_stage_info(request_id, new_stage):
    with Session(engine) as session:
        # 创建一个 RequestStageInfo 对象，准备插入或更新
        request_info = RequestStageInfo(request_id=request_id, request_stage=new_stage)

        # 使用 merge 方法来插入或更新
        session.merge(request_info)

        # 提交事务
        session.commit()


# 删除记录
def delete_request_stage_info(request_id):
    with Session(engine) as session:
        record = (
            session.query(RequestStageInfo).filter_by(request_id=request_id).first()
        )
        if record:
            session.delete(record)
            session.commit()


def nullable_folder_id(folder_id):
    if not folder_id:
        return -1
    return folder_id


def get_library(name: str) -> LibraryModel:
    with Session(engine) as session:
        stmt = select(LibraryModel).where(LibraryModel.name == name)
        return session.scalar(stmt)


def create_library(name: str) -> int:
    with Session(engine) as session:
        library = LibraryModel(name=name)
        session.add(library)
        session.commit()
        return library.id


def get_or_create_library(name: str) -> LibraryModel:
    library = get_library(name)
    if library:
        return library
    create_library(name)
    return get_library(name)


def list_libraries() -> List[LibraryModel]:
    with Session(engine) as session:
        stmt = select(LibraryModel)
        return list(session.scalars(stmt))


def get_folder(
    library_id: int, parent_folder_id: Optional[int], name: str
) -> FolderModel:
    with Session(engine) as session:
        stmt = (
            select(FolderModel)
            .where(FolderModel.library_id == library_id)
            .where(FolderModel.parent_folder_id == nullable_folder_id(parent_folder_id))
            .where(FolderModel.name == name)
        )
        return session.scalar(stmt)


def create_folder(library_id: int, parent_folder_id: Optional[int], name: str):
    with Session(engine) as session:
        folder = FolderModel(
            name=name,
            library_id=library_id,
            parent_folder_id=nullable_folder_id(parent_folder_id),
        )
        session.add(folder)
        session.commit()


def get_or_create_folder(library_id: int, parent_folder_id: Optional[int], name: str):
    folder = get_folder(library_id, parent_folder_id, name)
    if folder:
        return folder
    create_folder(library_id, parent_folder_id, name)
    return get_folder(library_id, parent_folder_id, name)


def get_folder_tree(library_id: int, path: str) -> FolderModel:
    names = filter(None, os.path.split(path))
    parent_folder = None
    for name in names:
        folder = get_folder(
            library_id=library_id,
            parent_folder_id=parent_folder.id if parent_folder else None,
            name=name,
        )
        parent_folder = folder
    return parent_folder


def get_or_create_folder_tree(library_id: int, path: str) -> FolderModel:
    names = filter(None, os.path.split(path))
    parent_folder = None
    for name in names:
        folder = get_or_create_folder(
            library_id=library_id,
            parent_folder_id=parent_folder.id if parent_folder else None,
            name=name,
        )
        parent_folder = folder
    return parent_folder


def get_folder_by_id(id: str) -> FolderModel:
    return FolderModel(
        id=id,
    )


def get_document(library_id: int, folder_id: Optional[str], name: str):
    with Session(engine) as session:
        stmt = (
            select(DocumentModel)
            .where(DocumentModel.library_id == library_id)
            .where(DocumentModel.folder_id == nullable_folder_id(folder_id))
            .where(DocumentModel.name == name)
        )
        return session.scalar(stmt)


def create_document(
    library_id: int, folder_id: Optional[str], name: str, file_type: str, language: str
):
    with Session(engine) as session:
        doc = DocumentModel(
            name=name,
            library_id=library_id,
            folder_id=nullable_folder_id(folder_id),
            file_type=file_type,
            language=language,
        )
        session.add(doc)
        session.commit()


def get_or_create_document(
    library_id: int, folder_id: Optional[str], name: str, file_type: str, language: str
):
    doc = get_document(library_id, folder_id, name)
    if doc:
        return doc
    create_document(library_id, folder_id, name, file_type, language)
    return get_document(library_id, folder_id, name)


def list_folders_by_library_id(library_id: int) -> List[FolderModel]:
    with Session(engine) as session:
        stmt = select(FolderModel).where(FolderModel.library_id == library_id)
        return list(session.scalars(stmt))


def search_folders(library_id: int, name: str) -> List[FolderModel]:
    with Session(engine) as session:
        stmt = (
            select(FolderModel)
            .where(FolderModel.library_id == library_id)
            .filter(FolderModel.name.like(f"%{name}%"))
        )
        return list(session.scalars(stmt))


def update_folder(folder_id: str, **values):
    with Session(engine) as session:
        stmt = update(FolderModel).where(FolderModel.id == folder_id).values(**values)
        session.execute(stmt)
        session.commit()


def list_files_by_library_id(library_id: int) -> List[DocumentModel]:
    with Session(engine) as session:
        stmt = select(DocumentModel).where(DocumentModel.library_id == library_id)
        return list(session.scalars(stmt))


def search_files(library_id: int, name: str) -> List[DocumentModel]:
    with Session(engine) as session:
        stmt = (
            select(DocumentModel)
            .where(DocumentModel.library_id == library_id)
            .filter(DocumentModel.name.like(f"%{name}%"))
        )
        return list(session.scalars(stmt))


def set_folder_suggestion_questions(
    library_id: int, folder_id: int, questions: List[str]
):
    with Session(engine) as session:
        deleteStmt = (
            delete(SuggestionQuestionModel)
            .where(SuggestionQuestionModel.library_id == library_id)
            .where(SuggestionQuestionModel.folder_id == folder_id)
        )
        session.execute(deleteStmt)
        for question in questions:
            suggestion_question = SuggestionQuestionModel(
                library_id=library_id, question=question, folder_id=folder_id
            )
            session.add(suggestion_question)
        session.commit()


def list_doc_suggestion_questions(
    library_id: str, doc_id: str
) -> List[SuggestionQuestionModel]:
    with Session(engine) as session:
        stmt = (
            select(SuggestionQuestionModel)
            .where(SuggestionQuestionModel.library_id == library_id)
            .where(SuggestionQuestionModel.doc_id == doc_id)
        )
        return list(session.scalars(stmt))


def list_folder_suggestion_questions(
    library_id: str, folder_id: str
) -> List[SuggestionQuestionModel]:
    with Session(engine) as session:
        stmt = (
            select(SuggestionQuestionModel)
            .where(SuggestionQuestionModel.library_id == library_id)
            .where(SuggestionQuestionModel.folder_id == folder_id)
        )
        return list(session.scalars(stmt))


def list_documents_by_folder(
    library_id: int, folder_id: Optional[int]
) -> List[DocumentModel]:
    with Session(engine) as session:
        stmt = (
            select(DocumentModel)
            .where(DocumentModel.library_id == library_id)
            .where(DocumentModel.folder_id == nullable_folder_id(folder_id))
        )
        return list(session.scalars(stmt))


def list_folders_by_status(status: int) -> List[FolderModel]:
    with Session(engine) as session:
        stmt = select(FolderModel).where(FolderModel.folder_status == status)
        return list(session.scalars(stmt))


def count_ready_files_in_folders(folder_id: int) -> int:
    with Session(engine) as session:
        stmt = (
            select(func.count())
            .select_from(DocumentModel)
            .where(DocumentModel.folder_id == folder_id)
            .where(DocumentModel.file_status == FileStatus.Ready)
        )
        return session.scalar(stmt)


def list_documents_by_ids(document_ids: List[int]) -> List[DocumentModel]:
    with Session(engine) as session:
        stmt = select(DocumentModel).where(DocumentModel.id.in_(document_ids))
        return list(session.scalars(stmt))


def get_unready_scene() -> List[SceneInfo]:
    with Session(engine) as session:
        stmt = select(SceneInfo).where(SceneInfo.scene_status == "unready")
        return list(session.scalars(stmt))


def get_scene_file_list(scene_id: str) -> List[SceneFile]:
    with Session(engine) as session:
        stmt = select(SceneFile).where(SceneFile.scene_id == scene_id)
        return list(session.scalars(stmt))


def update_scene_status(scene_id: str):
    with Session(engine) as session:
        stmt = (
            update(SceneInfo)
            .where(SceneInfo.scene_id == scene_id)
            .values(scene_status="ready")
        )
        session.execute(stmt)
        session.commit()


def count_scene_unready_files(scene_id: str) -> int:
    with Session(engine) as session:
        stmt = select(SceneFile.file_id).where(SceneFile.scene_id == scene_id)
        document_ids = [row[0] for row in session.execute(stmt)]
        stmt_document = (
            select(func.count())
            .select_from(DocumentModel)
            .where(DocumentModel.id.in_(document_ids))
            .where(DocumentModel.file_status == FileStatus.Ready)
        )
        return session.scalar(stmt_document)


def get_file_ids_by_scene_id(scene_id: str) -> List[int]:
    with Session(engine) as session:
        stmt = select(SceneFile.file_id).where(SceneFile.scene_id == scene_id)
        file_ids = session.scalars(stmt).all()
        return file_ids


if app_config.ENABLE_NL2DOCUMENT:
    tables = Base.metadata.tables
    for k, v in tables.items():
        # <class 'str'> < class 'sqlalchemy.sql.schema.Table' >
        print(__file__, " : ", k, type(v))
    #Base.metadata.create_all(engine, list(tables.values()))
