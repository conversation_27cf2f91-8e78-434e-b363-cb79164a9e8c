# 加载log3_07bxx.json
# 调用extract.format_extract_result_v2 接口，打印输出

import json
import biz.dianxin.utils.extract as extract

with open("/Users/<USER>/github_project/ask-bi/python/nl2metric/biz/dianxin/utils/log3_07b622d0162ae2e7.json", "r") as f:
    data = json.load(f)
print("start parsing task...")     
result = extract.format_extract_result_v2(data)
print(result)
with open("result.json", "w") as f:
    json.dump(result, f, ensure_ascii=False, indent=4)
