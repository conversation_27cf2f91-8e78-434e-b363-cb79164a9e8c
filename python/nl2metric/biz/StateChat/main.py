"""
状态机引擎主应用入口
"""

import uvicorn
from pathlib import Path
from contextlib import asynccontextmanager

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from utils.logger import setup_logger

# .env
from dotenv import load_dotenv

load_dotenv()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """FastAPI应用生命周期事件"""
    from storage.database import init_database, cleanup_database

    # 应用启动时执行
    await init_database()
    yield
    # 应用关闭时执行
    await cleanup_database()


app = FastAPI(
    title="状态机引擎API",
    description="基于配置驱动的状态机引擎 - 咖啡店营业执照申请流程",
    version="1.0.0",
    lifespan=lifespan,
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],  # 允许前端域名
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有HTTP方法
    allow_headers=["*"],  # 允许所有请求头
)


def main():
    """主函数"""
    # 设置日志
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    setup_logger(log_level="INFO", log_file="logs/state_chat.log")
    port = 5432
    print("🚀 启动状态机引擎服务...")
    print("📋 咖啡店营业执照申请流程")
    print(f"🌐 API文档: http://localhost:{port}/docs")
    print(f"📊 健康检查: http://localhost:{port}/health")
    from api.endpoints import router

    app.include_router(router)
    # 启动FastAPI应用
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=port,
        # reload=True,  # 开发模式下启用热重载
        log_level="info",
    )


if __name__ == "__main__":
    main()
