"""
数据库模型定义
"""

import json
from datetime import datetime
from typing import Any

from sqlalchemy import Column, String, Text, DateTime, Enum as SQLE<PERSON>, Integer
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.types import TypeDecorator, TEXT

from models.session import SessionStatus

Base = declarative_base()


class JSONEncodedDict(TypeDecorator):
    """将Python字典编码为JSON字符串存储在数据库中"""

    impl = TEXT
    cache_ok = True

    def process_bind_param(self, value, dialect):
        if value is not None:
            return json.dumps(value, ensure_ascii=False)
        return value

    def process_result_value(self, value, dialect):
        if value is not None:
            return json.loads(value)
        return value


def serialize_json_field(data: Any) -> str:
    """将Python对象序列化为JSON字符串"""
    return json.dumps(data, ensure_ascii=False)


def deserialize_json_field(data: str) -> Any:
    """将JSON字符串反序列化为Python对象"""
    return json.loads(data)


class SessionTable(Base):
    """用户会话表"""

    __tablename__ = "user_sessions"

    session_id = Column(String(255), primary_key=True, index=True)
    user_id = Column(String(255), index=True, nullable=True)
    flow_name = Column(String(255), nullable=False)
    current_state = Column(String(255), nullable=False)
    status = Column(
        SQLEnum(SessionStatus), default=SessionStatus.ACTIVE, nullable=False
    )
    created_at = Column(DateTime, default=datetime.now, nullable=False)
    updated_at = Column(
        DateTime, default=datetime.now, onupdate=datetime.now, nullable=False
    )


class ContextTable(Base):
    """流程上下文表"""

    __tablename__ = "flow_contexts"

    session_id = Column(String(255), primary_key=True, index=True)
    flow_name = Column(String(255), nullable=False)
    form_data = Column(JSONEncodedDict, default={})
    uploaded_files = Column(JSONEncodedDict, default={})
    business_info = Column(JSONEncodedDict, default={})
    personal_info = Column(JSONEncodedDict, default={})
    license_info = Column(JSONEncodedDict, default={})
    completed_steps = Column(JSONEncodedDict, default=[])
    pending_approvals = Column(JSONEncodedDict, default=[])
    generated_documents = Column(JSONEncodedDict, default=[])
    created_at = Column(DateTime, default=datetime.now, nullable=False)
    updated_at = Column(
        DateTime, default=datetime.now, onupdate=datetime.now, nullable=False
    )


class TransitionTable(Base):
    """状态转移历史表"""

    __tablename__ = "state_transitions"

    id = Column(String(255), primary_key=True, index=True)
    session_id = Column(String(255), index=True, nullable=False)
    from_state = Column(String(255), nullable=False)
    to_state = Column(String(255), nullable=False)
    condition = Column(Text, nullable=True)
    user_input = Column(JSONEncodedDict, default={})
    created_at = Column(DateTime, default=datetime.now, nullable=False)
