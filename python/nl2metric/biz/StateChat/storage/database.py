"""
数据库管理模块
"""

import asyncio
import os
from pathlib import Path
from typing import Optional, Dict, Any

import yaml
from loguru import logger
from sqlalchemy import create_engine
from sqlalchemy.ext.asyncio import (
    create_async_engine,
    AsyncEngine,
    AsyncSession,
    async_sessionmaker,
)
from storage.models import Base


# config_path 当前路径下的../config/database.yaml
class DatabaseConfig:
    """数据库配置"""

    def __init__(self):
        # 始终从项目根目录加载配置
        project_root = Path(__file__).parent.parent
        config_path = project_root / "config" / "database.yaml"
        self.config = self._load_config(str(config_path))
        self.env = os.getenv("APP_ENV", "development")

    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        if not Path(config_path).exists():
            raise FileNotFoundError(f"数据库配置文件未找到: {config_path}")
        with open(config_path, "r", encoding="utf-8") as f:
            return yaml.safe_load(f)

    def _get_env_value(self, value: str) -> str:
        """处理环境变量占位符"""
        if not isinstance(value, str):
            return value

        if value.startswith("${") and value.endswith("}"):
            # 解析默认值
            env_key = value[2:-1]
            if ":-" in env_key:
                env_key, default = env_key.split(":-", 1)
                return os.getenv(env_key, default)
            return os.getenv(env_key, "")
        return value

    def get_database_url(self) -> str:
        """获取数据库URL"""
        db_config = self.config[self.env]
        db_type = db_config["type"]

        if db_type == "sqlite":
            database = self._get_env_value(db_config["database"])
            if database != ":memory:":
                # 确保数据目录存在
                db_path = Path(database)
                db_path.parent.mkdir(parents=True, exist_ok=True)
            return f"sqlite+aiosqlite:///{database}"

        elif db_type == "mysql":
            host = self._get_env_value(db_config["host"])
            port = self._get_env_value(db_config["port"])
            database = self._get_env_value(db_config["database"])
            user = self._get_env_value(db_config["user"])
            password = self._get_env_value(db_config["password"])
            charset = db_config.get("charset", "utf8mb4")

            return f"mysql+aiomysql://{user}:{password}@{host}:{port}/{database}?charset={charset}"

        else:
            raise ValueError(f"不支持的数据库类型: {db_type}")

    def get_pool_settings(self) -> Dict[str, Any]:
        """获取连接池设置"""
        pool_config = self.config["pool"].copy()

        # 如果是生产环境，使用配置中的特定设置
        if self.env == "production":
            prod_config = self.config["production"]
            pool_config["pool_size"] = prod_config.get(
                "pool_size", pool_config["pool_size"]
            )
            pool_config["max_overflow"] = prod_config.get(
                "max_overflow", pool_config["max_overflow"]
            )

        return pool_config

    def get_migration_settings(self) -> Dict[str, Any]:
        """获取迁移设置"""
        return self.config["migrations"]


class DatabaseManager:
    """数据库管理器"""

    def __init__(self):
        """
        初始化数据库管理器
        """
        self.config = DatabaseConfig()  # DatabaseConfig现在自己处理路径
        self.engine: Optional[AsyncEngine] = None
        self.sessionmaker: Optional[async_sessionmaker[AsyncSession]] = None

    async def init(self):
        """初始化数据库连接"""
        if self.engine is not None:
            return

        database_url = self.config.get_database_url()
        pool_settings = self.config.get_pool_settings()

        self.engine = create_async_engine(
            database_url,
            echo=pool_settings["echo"],
            pool_size=pool_settings["pool_size"],
            max_overflow=pool_settings["max_overflow"],
            pool_timeout=pool_settings["timeout"],
            pool_recycle=pool_settings["recycle"],
            pool_pre_ping=pool_settings["pre_ping"],
        )

        self.sessionmaker = async_sessionmaker(
            self.engine, expire_on_commit=False, class_=AsyncSession
        )

        # 新增：使用线程池执行同步DDL操作
        await asyncio.to_thread(self._sync_init)

        logger.info(f"数据库管理器初始化完成: {database_url}")

    def _sync_init(self):
        """同步初始化方法"""
        database_url = self.config.get_database_url()
        # 替换数据库URL为同步驱动
        if database_url.startswith("sqlite+aiosqlite"):
            database_url = database_url.replace("sqlite+aiosqlite", "sqlite")
        sync_engine = create_engine(database_url)
        Base.metadata.create_all(bind=sync_engine)

    async def get_session(self) -> AsyncSession:
        """获取数据库会话"""
        if self.sessionmaker is None:
            await self.init()
        return self.sessionmaker()

    async def close(self):
        """关闭数据库连接"""
        if self.engine is not None:
            await self.engine.dispose()
            self.engine = None
            self.sessionmaker = None
            logger.info("数据库连接已关闭")


# 全局数据库管理器实例
db_manager = DatabaseManager()


async def get_db_session():
    """FastAPI依赖函数：获取数据库会话"""
    async with await db_manager.get_session() as session:
        try:
            yield session
        finally:
            await session.close()


# 应用启动时初始化数据库
async def init_database():
    """初始化数据库连接"""
    await db_manager.init()


# 应用关闭时清理数据库连接
async def cleanup_database():
    """清理数据库连接"""
    await db_manager.close()
