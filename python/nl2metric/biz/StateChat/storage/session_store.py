"""
会话存储实现
"""

from datetime import datetime
from typing import List, Optional

from loguru import logger
from sqlalchemy import select, update, delete
from sqlalchemy.ext.asyncio import AsyncSession

from models.context import FlowContext
from models.session import UserSession, SessionStatus, SessionSummary
from models.state import StateTransition
from storage.models import (
    SessionTable,
    ContextTable,
    TransitionTable,
    deserialize_json_field,
    serialize_json_field,
)


class SessionStore:
    """会话存储管理器"""

    def __init__(self, db_session: AsyncSession):
        self.db_session = db_session

    async def create_session(self, session: UserSession) -> UserSession:
        """创建新会话"""
        try:
            # 创建会话记录
            session_record = SessionTable(
                session_id=session.session_id,
                user_id=session.user_id,
                flow_name=session.flow_name,
                current_state=session.current_state,
                status=session.status,
                created_at=session.created_at,
                updated_at=session.updated_at,
            )

            self.db_session.add(session_record)
            await self.db_session.commit()

            logger.info(f"会话已创建: {session.session_id}")
            return session

        except Exception as e:
            await self.db_session.rollback()
            logger.error(f"创建会话失败: {e}")
            raise

    async def get_session(self, session_id: str) -> Optional[UserSession]:
        """获取会话"""
        try:
            stmt = select(SessionTable).where(SessionTable.session_id == session_id)
            result = await self.db_session.execute(stmt)
            session_record = result.scalar_one_or_none()

            if not session_record:
                return None

            # 获取状态转移历史
            history_stmt = (
                select(TransitionTable)
                .where(TransitionTable.session_id == session_id)
                .order_by(TransitionTable.created_at)
            )
            history_result = await self.db_session.execute(history_stmt)
            history_records = history_result.scalars().all()

            # 构建状态转移历史
            history = []
            for record in history_records:
                transition = StateTransition(
                    from_state=record.from_state,
                    to_state=record.to_state,
                    condition=record.condition,
                    user_input=deserialize_json_field(record.user_input),
                )
                history.append(transition)

            # 构建会话对象
            session = UserSession(
                session_id=session_record.session_id,
                user_id=session_record.user_id,
                flow_name=session_record.flow_name,
                current_state=session_record.current_state,
                status=SessionStatus(session_record.status),
                context={},  # 上下文单独获取
                history=history,
                created_at=session_record.created_at,
                updated_at=session_record.updated_at,
            )

            return session

        except Exception as e:
            logger.error(f"获取会话失败: {e}")
            raise

    async def update_session(self, session: UserSession) -> UserSession:
        """更新会话"""
        try:
            stmt = (
                update(SessionTable)
                .where(SessionTable.session_id == session.session_id)
                .values(
                    current_state=session.current_state,
                    status=session.status,
                    updated_at=datetime.now(),
                )
            )

            await self.db_session.execute(stmt)
            await self.db_session.commit()

            logger.debug(f"会话已更新: {session.session_id}")
            return session

        except Exception as e:
            await self.db_session.rollback()
            logger.error(f"更新会话失败: {e}")
            raise

    async def delete_session(self, session_id: str) -> bool:
        """删除会话"""
        try:
            # 删除状态转移历史
            await self.db_session.execute(
                delete(TransitionTable).where(TransitionTable.session_id == session_id)
            )

            # 删除上下文
            await self.db_session.execute(
                delete(ContextTable).where(ContextTable.session_id == session_id)
            )

            # 删除会话
            result = await self.db_session.execute(
                delete(SessionTable).where(SessionTable.session_id == session_id)
            )

            await self.db_session.commit()

            deleted = result.rowcount > 0
            if deleted:
                logger.info(f"会话已删除: {session_id}")

            return deleted

        except Exception as e:
            await self.db_session.rollback()
            logger.error(f"删除会话失败: {e}")
            raise

    async def list_sessions(
        self, user_id: Optional[str] = None
    ) -> List[SessionSummary]:
        """列出会话摘要"""
        try:
            stmt = select(SessionTable)
            if user_id:
                stmt = stmt.where(SessionTable.user_id == user_id)
            stmt = stmt.order_by(SessionTable.updated_at.desc())

            result = await self.db_session.execute(stmt)
            session_records = result.scalars().all()

            summaries = []
            for record in session_records:
                summary = SessionSummary(
                    session_id=record.session_id,
                    user_id=record.user_id,
                    flow_name=record.flow_name,
                    current_state=record.current_state,
                    status=SessionStatus(record.status),
                    progress_percentage=0.0,  # 需要从上下文计算
                    created_at=record.created_at,
                    updated_at=record.updated_at,
                )
                summaries.append(summary)

            return summaries

        except Exception as e:
            logger.error(f"列出会话失败: {e}")
            raise

    async def add_transition(self, session_id: str, transition: StateTransition):
        """添加状态转移记录"""
        try:
            transition_record = TransitionTable(
                id=transition.id,
                session_id=session_id,
                from_state=transition.from_state,
                to_state=transition.to_state,
                condition=transition.condition,
                user_input=serialize_json_field(transition.user_input),
                created_at=datetime.now(),
            )

            self.db_session.add(transition_record)
            await self.db_session.commit()

            logger.debug(
                f"状态转移记录已添加: {session_id} {transition.from_state} -> {transition.to_state}"
            )

        except Exception as e:
            await self.db_session.rollback()
            logger.error(f"添加状态转移记录失败: {e}")
            raise


class ContextStore:
    """上下文存储管理器"""

    def __init__(self, db_session: AsyncSession):
        self.db_session = db_session

    async def create_context(self, context: FlowContext) -> FlowContext:
        """创建上下文"""
        try:
            context_record = ContextTable(
                session_id=context.session_id,
                flow_name=context.flow_name,
                form_data=serialize_json_field(
                    {k: v.dict() for k, v in context.form_data.items()}
                ),
                uploaded_files=serialize_json_field(
                    {
                        k: [f.dict() for f in v]
                        for k, v in context.uploaded_files.items()
                    }
                ),
                business_info=serialize_json_field(context.business_info),
                personal_info=serialize_json_field(context.personal_info),
                license_info=serialize_json_field(context.license_info),
                completed_steps=serialize_json_field(context.completed_steps),
                pending_approvals=serialize_json_field(context.pending_approvals),
                generated_documents=serialize_json_field(context.generated_documents),
                created_at=context.created_at,
                updated_at=context.updated_at,
            )

            self.db_session.add(context_record)
            await self.db_session.commit()

            logger.info(f"上下文已创建: {context.session_id}")
            return context

        except Exception as e:
            await self.db_session.rollback()
            logger.error(f"创建上下文失败: {e}")
            raise

    async def get_context(self, session_id: str) -> Optional[FlowContext]:
        """获取上下文"""
        try:
            stmt = select(ContextTable).where(ContextTable.session_id == session_id)
            result = await self.db_session.execute(stmt)
            context_record = result.scalar_one_or_none()

            if not context_record:
                return None

            # 重建上下文对象
            from models.context import FormData, FileUpload

            # 重建表单数据
            form_data = {}
            form_data_raw = deserialize_json_field(context_record.form_data)
            for key, value in form_data_raw.items():
                form_data[key] = FormData(**value)

            # 重建上传文件
            uploaded_files = {}
            uploaded_files_raw = deserialize_json_field(context_record.uploaded_files)
            for key, files_list in uploaded_files_raw.items():
                uploaded_files[key] = [
                    FileUpload(**file_data) for file_data in files_list
                ]

            context = FlowContext(
                session_id=context_record.session_id,
                flow_name=context_record.flow_name,
                form_data=form_data,
                uploaded_files=uploaded_files,
                business_info=deserialize_json_field(context_record.business_info),
                personal_info=deserialize_json_field(context_record.personal_info),
                license_info=deserialize_json_field(context_record.license_info),
                completed_steps=deserialize_json_field(context_record.completed_steps),
                pending_approvals=deserialize_json_field(
                    context_record.pending_approvals
                ),
                generated_documents=deserialize_json_field(
                    context_record.generated_documents
                ),
                created_at=context_record.created_at,
                updated_at=context_record.updated_at,
            )

            return context

        except Exception as e:
            logger.error(f"获取上下文失败: {e}")
            raise

    async def update_context(self, context: FlowContext) -> FlowContext:
        """更新上下文"""
        try:
            stmt = (
                update(ContextTable)
                .where(ContextTable.session_id == context.session_id)
                .values(
                    form_data=serialize_json_field(
                        {k: v.dict() for k, v in context.form_data.items()}
                    ),
                    uploaded_files=serialize_json_field(
                        {
                            k: [f.model_dump() for f in v]
                            for k, v in context.uploaded_files.items()
                        }
                    ),
                    business_info=serialize_json_field(context.business_info),
                    personal_info=serialize_json_field(context.personal_info),
                    license_info=serialize_json_field(context.license_info),
                    completed_steps=serialize_json_field(context.completed_steps),
                    pending_approvals=serialize_json_field(context.pending_approvals),
                    generated_documents=serialize_json_field(
                        context.generated_documents
                    ),
                    updated_at=datetime.now(),
                )
            )

            await self.db_session.execute(stmt)
            await self.db_session.commit()

            logger.debug(f"上下文已更新: {context.session_id}")
            return context

        except Exception as e:
            await self.db_session.rollback()
            logger.error(f"更新上下文失败: {e}")
            raise
