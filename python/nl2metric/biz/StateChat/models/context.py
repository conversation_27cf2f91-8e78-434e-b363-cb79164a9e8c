"""
流程上下文模型定义
"""

from typing import Dict, List, Optional, Any, Union
from pydantic import BaseModel, Field
from datetime import datetime


class FileUpload(BaseModel):
    """文件上传模型"""

    filename: str = Field(..., description="文件名")
    file_path: str = Field(..., description="文件存储路径")
    file_size: int = Field(..., description="文件大小(字节)")
    content_type: str = Field(..., description="文件类型")
    upload_time: datetime = Field(default_factory=datetime.now, description="上传时间")

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}


class FormData(BaseModel):
    """表单数据模型"""

    field_name: str = Field(..., description="字段名称")
    field_value: Union[str, List[str], Dict[str, Any]] = Field(
        ..., description="字段值"
    )
    field_type: str = Field(..., description="字段类型")
    is_required: bool = Field(False, description="是否必填")
    validation_rules: Optional[Dict[str, Any]] = Field(None, description="验证规则")


class FlowContext(BaseModel):
    """流程上下文模型"""

    session_id: str = Field(..., description="会话ID")
    flow_name: str = Field(..., description="流程名称")

    # 用户输入数据
    form_data: Dict[str, FormData] = Field(default_factory=dict, description="表单数据")
    uploaded_files: Dict[str, List[FileUpload]] = Field(
        default_factory=dict, description="上传文件"
    )

    # 业务数据
    business_info: Dict[str, Any] = Field(default_factory=dict, description="企业信息")
    personal_info: Dict[str, Any] = Field(default_factory=dict, description="个人信息")
    license_info: Dict[str, Any] = Field(default_factory=dict, description="许可证信息")

    # 流程控制
    completed_steps: List[str] = Field(default_factory=list, description="已完成步骤")
    pending_approvals: List[str] = Field(default_factory=list, description="待审批项目")
    generated_documents: List[str] = Field(
        default_factory=list, description="生成的文档"
    )

    # 元数据
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}

    def add_form_data(
        self,
        field_name: str,
        field_value: Any,
        field_type: str,
        is_required: bool = False,
    ):
        """添加表单数据"""
        self.form_data[field_name] = FormData(
            field_name=field_name,
            field_value=field_value,
            field_type=field_type,
            is_required=is_required,
        )
        self.updated_at = datetime.now()

    def add_uploaded_file(self, field_name: str, file_upload: FileUpload):
        """添加上传文件"""
        if field_name not in self.uploaded_files:
            self.uploaded_files[field_name] = []
        self.uploaded_files[field_name].append(file_upload)
        self.updated_at = datetime.now()

    def get_form_value(self, field_name: str, default: Any = None) -> Any:
        """获取表单字段值"""
        form_data = self.form_data.get(field_name)
        return form_data.field_value if form_data else default

    def get_uploaded_files(self, field_name: str) -> List[FileUpload]:
        """获取指定字段的上传文件"""
        return self.uploaded_files.get(field_name, [])

    def mark_step_completed(self, step_name: str):
        """标记步骤为已完成"""
        if step_name not in self.completed_steps:
            self.completed_steps.append(step_name)
            self.updated_at = datetime.now()

    def add_pending_approval(self, approval_item: str):
        """添加待审批项目"""
        if approval_item not in self.pending_approvals:
            self.pending_approvals.append(approval_item)
            self.updated_at = datetime.now()

    def remove_pending_approval(self, approval_item: str):
        """移除待审批项目"""
        if approval_item in self.pending_approvals:
            self.pending_approvals.remove(approval_item)
            self.updated_at = datetime.now()

    def add_generated_document(self, document_path: str):
        """添加生成的文档"""
        if document_path not in self.generated_documents:
            self.generated_documents.append(document_path)
            self.updated_at = datetime.now()

    def update_business_info(self, key: str, value: Any):
        """更新企业信息"""
        self.business_info[key] = value
        self.updated_at = datetime.now()

    def update_personal_info(self, key: str, value: Any):
        """更新个人信息"""
        self.personal_info[key] = value
        self.updated_at = datetime.now()

    def update_license_info(self, key: str, value: Any):
        """更新许可证信息"""
        self.license_info[key] = value
        self.updated_at = datetime.now()

    def get_progress_percentage(self, total_steps: int) -> float:
        """计算进度百分比"""
        if total_steps == 0:
            return 0.0
        return (len(self.completed_steps) / total_steps) * 100.0
