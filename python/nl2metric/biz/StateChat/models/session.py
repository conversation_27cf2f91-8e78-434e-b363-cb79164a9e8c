"""
用户会话模型定义
"""

from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum

from .state import StateTransition, UserInput


class SessionStatus(str, Enum):
    """会话状态枚举"""

    ACTIVE = "active"
    COMPLETED = "completed"
    PAUSED = "paused"
    ERROR = "error"


class UserSession(BaseModel):
    """用户会话模型"""

    session_id: str = Field(..., description="会话ID")
    user_id: Optional[str] = Field(None, description="用户ID")
    flow_name: str = Field(..., description="流程名称")
    current_state: str = Field(..., description="当前状态")
    status: SessionStatus = Field(SessionStatus.ACTIVE, description="会话状态")
    context: Dict[str, Any] = Field(default_factory=dict, description="会话上下文")
    history: List[StateTransition] = Field(
        default_factory=list, description="状态转移历史"
    )
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")

    class Config:
        use_enum_values = True
        json_encoders = {datetime: lambda v: v.isoformat()}

    def add_transition(self, transition: StateTransition):
        """添加状态转移记录"""
        self.history.append(transition)
        self.current_state = transition.to_state
        self.updated_at = datetime.now()

    def update_context(self, key: str, value: Any):
        """更新会话上下文"""
        self.context[key] = value
        self.updated_at = datetime.now()

    def get_context_value(self, key: str, default: Any = None) -> Any:
        """获取上下文值"""
        return self.context.get(key, default)

    def is_completed(self) -> bool:
        """检查会话是否完成"""
        return self.status == SessionStatus.COMPLETED

    def is_active(self) -> bool:
        """检查会话是否活跃"""
        return self.status == SessionStatus.ACTIVE

    def get_history(self) -> list[dict]:
        return [transition.model_dump() for transition in self.history]


class SessionSummary(BaseModel):
    """会话摘要模型"""

    session_id: str
    user_id: Optional[str]
    flow_name: str
    current_state: str
    status: SessionStatus
    progress_percentage: float = Field(0.0, description="进度百分比")
    created_at: datetime
    updated_at: datetime

    class Config:
        use_enum_values = True
        json_encoders = {datetime: lambda v: v.isoformat()}
