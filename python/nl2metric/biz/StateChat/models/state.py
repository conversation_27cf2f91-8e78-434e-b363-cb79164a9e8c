"""
状态节点模型定义
"""

import uuid
from typing import Dict, List, Optional, Union, Any
from pydantic import BaseModel, Field
from enum import Enum


class InputType(str, Enum):
    """输入类型枚举"""

    TEXT = "text"
    CHOICE = "choice"
    MULTICHOICE = "multichoice"
    FILE = "file"


class StateNode(BaseModel):
    """状态节点模型"""

    name: str = Field(..., description="状态名称")
    prompt: str = Field(..., description="状态提示信息")
    input_type: InputType = Field(..., description="输入类型")
    options: Optional[List[str]] = Field(None, description="选择项列表")
    requires: Optional[List[str]] = Field(None, description="必需的字段列表")
    next: Optional[Union[str, Dict[str, str]]] = Field(None, description="下一个状态")

    class Config:
        use_enum_values = True


class FlowConfig(BaseModel):
    """流程配置模型"""

    flow_name: str = Field(..., description="流程名称")
    description: str = Field(..., description="流程描述")
    version: str = Field(..., description="版本号")
    states: Dict[str, StateNode] = Field(..., description="状态字典")
    initial_state: str = Field(..., description="初始状态")
    final_states: List[str] = Field(..., description="终止状态列表")


class StateTransition(BaseModel):
    """状态转移模型"""

    id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="转移ID")
    from_state: str = Field(..., description="源状态")
    to_state: str = Field(..., description="目标状态")
    condition: Optional[str] = Field(None, description="转移条件")
    user_input: Optional[dict] = Field(None, description="用户输入")


class UserInput(BaseModel):
    """用户输入模型"""

    input_type: InputType
    value: Union[str, List[str], Dict[str, Any]]
    files: Optional[List[str]] = Field(None, description="上传的文件列表")
    timestamp: Optional[str] = Field(None, description="输入时间戳")
