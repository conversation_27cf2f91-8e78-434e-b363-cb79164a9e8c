# Requirements Document

## Introduction

为现有的状态机引擎项目创建一个现代化的React TypeScript前端界面。该前端将提供用户友好的界面来与后端状态机API进行交互，支持完整的咖啡店营业执照申请流程。前端将使用Ant Design (antd)作为UI组件库，提供专业、美观且响应式的用户体验。

## Requirements

### Requirement 1

**User Story:** 作为用户，我希望能够通过直观的Web界面开始新的营业执照申请流程，以便我可以轻松地进行申请操作。

#### Acceptance Criteria

1. WHEN 用户访问应用首页 THEN 系统 SHALL 显示欢迎界面和可用的流程选项
2. WHEN 用户点击"开始申请"按钮 THEN 系统 SHALL 创建新的会话并跳转到第一个状态
3. WHEN 会话创建成功 THEN 系统 SHALL 显示当前状态的提示信息和相应的输入组件
4. IF 会话创建失败 THEN 系统 SHALL 显示错误消息并提供重试选项

### Requirement 2

**User Story:** 作为用户，我希望能够根据不同的输入类型（文本、选择、多选、文件上传）进行交互，以便我可以提供所需的信息。

#### Acceptance Criteria

1. WHEN 当前状态要求文本输入 THEN 系统 SHALL 显示文本输入框
2. WHEN 当前状态要求单选 THEN 系统 SHALL 显示单选按钮组或下拉选择器
3. WHEN 当前状态要求多选 THEN 系统 SHALL 显示复选框组
4. WHEN 当前状态要求文件上传 THEN 系统 SHALL 显示文件上传组件
5. WHEN 用户提交输入 THEN 系统 SHALL 验证输入并发送到后端API
6. IF 输入验证失败 THEN 系统 SHALL 显示具体的错误信息

### Requirement 3

**User Story:** 作为用户，我希望能够看到申请流程的进度，以便我了解当前的完成情况和剩余步骤。

#### Acceptance Criteria

1. WHEN 用户在流程中 THEN 系统 SHALL 显示进度条指示当前完成百分比
2. WHEN 状态发生转移 THEN 系统 SHALL 更新进度条
3. WHEN 用户查看会话详情 THEN 系统 SHALL 显示已完成的步骤列表
4. WHEN 流程完成 THEN 系统 SHALL 显示完成状态和摘要信息

### Requirement 4

**User Story:** 作为用户，我希望能够查看和管理我的申请会话，以便我可以暂停、恢复或查看历史记录。

#### Acceptance Criteria

1. WHEN 用户访问会话管理页面 THEN 系统 SHALL 显示所有会话的列表
2. WHEN 用户点击会话项 THEN 系统 SHALL 显示会话详情包括状态历史
3. WHEN 用户选择暂停会话 THEN 系统 SHALL 调用暂停API并更新状态
4. WHEN 用户选择恢复会话 THEN 系统 SHALL 调用恢复API并跳转到当前状态
5. WHEN 用户选择删除会话 THEN 系统 SHALL 显示确认对话框并执行删除操作

### Requirement 5

**User Story:** 作为用户，我希望能够上传所需的文件，以便我可以提交申请所需的文档。

#### Acceptance Criteria

1. WHEN 状态要求文件上传 THEN 系统 SHALL 显示拖拽上传组件
2. WHEN 用户选择或拖拽文件 THEN 系统 SHALL 验证文件类型和大小
3. WHEN 文件验证通过 THEN 系统 SHALL 上传文件到服务器
4. WHEN 文件上传成功 THEN 系统 SHALL 显示上传成功状态和文件信息
5. IF 文件上传失败 THEN 系统 SHALL 显示错误信息并允许重新上传

### Requirement 6

**User Story:** 作为用户，我希望界面能够智能识别我的意图（闲聊、查询、业务办理），以便我可以获得相应的响应。

#### Acceptance Criteria

1. WHEN 用户输入闲聊内容 THEN 系统 SHALL 显示友好的回复而不影响流程
2. WHEN 用户输入查询问题 THEN 系统 SHALL 显示知识库查询结果
3. WHEN 用户输入业务相关内容 THEN 系统 SHALL 继续执行业务流程
4. WHEN 系统识别为流式响应 THEN 系统 SHALL 实时显示流式内容
5. WHEN 意图处理完成 THEN 系统 SHALL 根据should_continue_flow决定是否继续流程

### Requirement 7

**User Story:** 作为用户，我希望界面具有响应式设计和良好的用户体验，以便我可以在不同设备上舒适地使用。

#### Acceptance Criteria

1. WHEN 用户在桌面设备访问 THEN 系统 SHALL 显示完整的多列布局
2. WHEN 用户在移动设备访问 THEN 系统 SHALL 显示适配的单列布局
3. WHEN 用户执行操作 THEN 系统 SHALL 显示适当的加载状态
4. WHEN 发生错误 THEN 系统 SHALL 显示用户友好的错误消息
5. WHEN 操作成功 THEN 系统 SHALL 显示成功反馈

### Requirement 8

**User Story:** 作为用户，我希望能够查看系统状态和配置信息，以便我了解系统的运行情况。

#### Acceptance Criteria

1. WHEN 用户访问系统信息页面 THEN 系统 SHALL 显示健康检查状态
2. WHEN 用户查看流程配置 THEN 系统 SHALL 显示可用的流程列表和详情
3. WHEN 用户查看状态信息 THEN 系统 SHALL 显示特定状态的配置信息
4. WHEN 系统离线 THEN 系统 SHALL 显示离线状态和重连选项