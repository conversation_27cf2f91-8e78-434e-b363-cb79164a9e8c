# StateChat - 基于配置驱动的状态机引擎

一个用Python开发的开源状态机引擎，专门用于处理复杂的业务流程。本项目以新加坡咖啡店营业执照申请流程为例，展示了如何通过配置文件驱动的方式实现复杂的多步骤业务流程。

## 🌟 特性

- **配置驱动**: 通过YAML配置文件定义业务流程，无需修改代码
- **状态机引擎**: 基于有限状态机理论，支持复杂的状态转移逻辑
- **意图识别**: 智能识别用户输入意图，支持闲聊/知识查询/业务办理
- **多种输入类型**: 支持文本、选择、多选、文件上传等多种输入方式
- **会话管理**: 完整的用户会话生命周期管理
- **RESTful API**: 提供完整的REST API接口
- **文件上传**: 支持文件上传和管理
- **进度跟踪**: 实时跟踪流程进度
- **异常处理**: 完善的错误处理和验证机制
- **日志系统**: 详细的日志记录和监控

## 🏗️ 项目架构

```
project/
├── core/                      # 核心状态机引擎
│   ├── state_machine.py       # 状态机基类
│   ├── transition.py          # 状态转移逻辑
│   ├── intent.py             # 意图识别模块
│   └── exceptions.py          # 状态机异常类
├── models/                    # 数据模型
│   ├── state.py               # 状态节点模型
│   ├── session.py             # 用户会话模型
│   └── context.py             # 流程上下文
├── api/                       # API接口层
│   ├── endpoints.py           # FastAPI路由
│   └── schemas.py             # Pydantic模型
├── utils/                     # 工具类
│   ├── config_loader.py       # 配置加载器
│   └── logger.py              # 日志工具
├── config/                    # 配置文件
│   └── cafe_license_flow.yaml # 咖啡店许可证流程配置
├── main.py                    # 应用入口
└── test/                      # 测试模块
    ├── test_demo.py           # 演示脚本
    ├── api_demo.py            # API演示
    └── intent_demo.py         # 意图识别演示
```

## 🚀 快速开始

### 环境要求

- Python 3.13+
- uv (推荐) 或 pip

### 安装依赖

```bash
# 使用uv (推荐)
uv sync

# 或使用pip
pip install -r requirements.txt
```

### 运行演示

```bash
# 演示状态机功能
python test/test_demo.py

# 演示API功能
python test/api_demo.py

# 演示意图识别功能
python test/intent_demo.py
```

### 启动API服务

```bash
# 启动FastAPI服务
python main.py
```

服务启动后，可以访问：
- API文档: http://localhost:8000/docs
- 健康检查: http://localhost:8000/health

## 💡 意图识别功能

系统支持智能识别用户输入的意图，并根据不同意图类型执行相应的处理逻辑：

### 意图类型

1. **闲聊 (CHAT)**
   - 识别日常对话、问候、闲聊等
   - 示例："你好"、"天气真好"、"再见"
   - 不影响业务流程，返回友好回复

2. **知识查询 (RAG_QUERY)**
   - 识别咨询、查询、了解等意图
   - 示例："如何注册公司"、"申请流程是什么"
   - 返回知识库查询结果，不影响流程

3. **业务办理 (BUSINESS)**
   - 识别申请、提交、确认等业务操作
   - 示例："申请营业执照"、"提交文件"
   - 继续执行业务流程

4. **未知 (UNKNOWN)**
   - 无法明确识别的意图
   - 默认作为业务输入处理

### 使用示例

```python
from core.intent import IntentProcessor

processor = IntentProcessor()

# 识别意图
intent = processor.process("你好，我想申请营业执照")
print(f"识别意图: {intent.value}")  # chat

# 处理输入
result = processor.handle_input(
    "如何注册公司?",
    {"session_id": "123", "current_state": "welcome"}
)
print(result)
# {
#     "intent": "rag_query",
#     "success": True,
#     "response": "这是知识库查询结果",
#     "should_continue_flow": False
# }
```

### 自定义意图规则

可以通过添加关键词模式来扩展意图识别能力：

```python
processor = IntentProcessor()

# 添加新的意图模式
processor.intent_patterns[IntentType.CHAT].extend([
    "打招呼", "问候", "闲聊"
])

processor.intent_patterns[IntentType.RAG_QUERY].extend([
    "查一下", "帮我找", "需要了解"
])
```

## 📋 业务流程示例

本项目实现了新加坡咖啡店营业执照申请的完整流程，包括：

1. **ACRA注册流程**
   - 企业结构选择（独资/合伙/私人有限公司）
   - 企业信息收集
   - 身份验证
   - 费用支付

2. **商业单位流程**
   - 商业地产租赁
   - 地址和布局图提供

3. **附加服务许可**
   - 酒类许可证申请
   - 宠物友好许可证
   - 户外座位许可证

4. **装修与安全流程**
   - 消防安全证书申请
   - 临时防火计划

5. **招牌许可流程**
   - 招牌设计审批

6. **雇佣流程**
   - 公积金雇主注册

7. **食品许可流程**
   - 食品店许可证申请

8. **财务设置流程**
   - 企业银行账户开设
   - GST注册
   - 簿记设置

## 🔧 配置文件格式

状态机流程通过YAML配置文件定义：

```yaml
flow_name: "cafe_license_application"
description: "新加坡咖啡店营业执照申请完整流程"
version: "1.0.0"

states:
  welcome_user:
    name: "welcome_user"
    prompt: "您好！我是AI助手，很高兴为您服务。您想申请哪种类型的营业执照？"
    input_type: "text"
    next: "check_acra_registration"

  check_acra_registration:
    name: "check_acra_registration"
    prompt: "您的咖啡店是否已在ACRA注册？"
    input_type: "choice"
    options: ["是", "否"]
    next:
      是: "check_commercial_unit"
      否: "register_acra"

initial_state: "welcome_user"
final_states: ["done"]
```

## 📡 API接口

### 创建会话

```bash
POST /sessions
{
  "user_id": "user123"
}
```

### 处理用户输入

```bash
POST /sessions/{session_id}/input
{
  "input_type": "text",
  "value": "我要申请营业执照"
}
```

### 上传文件

```bash
POST /sessions/{session_id}/files
# multipart/form-data
```

### 获取会话详情

```bash
GET /sessions/{session_id}
```

## 🧪 测试

运行测试脚本来验证系统功能：

```bash
# 测试状态机功能
python test/test_demo.py

# 测试API功能
python test/api_demo.py

# 测试意图识别
python test/intent_demo.py
```

## 📊 状态图

```mermaid
stateDiagram-v2
    [*] --> welcome_user
    welcome_user --> check_acra_registration
    
    state ACRA注册流程 {
        check_acra_registration --> register_acra: 否
        register_acra --> collect_business_info_and_owner: 独资经营/合伙经营
        register_acra --> collect_business_info_and_director: 私人有限公司
        
        collect_business_info_and_owner --> verify_identity
        collect_business_info_and_director --> verify_identity
        verify_identity --> confirm_acra_info
        confirm_acra_info --> acra_payment: 确认
        confirm_acra_info --> register_acra: 修改
        acra_payment --> check_commercial_unit
    }
    
    check_acra_registration --> check_commercial_unit: 是
    
    state 商业单位流程 {
        check_commercial_unit --> find_commercial_unit: 否
        check_commercial_unit --> provide_commercial_unit_info: 是
        find_commercial_unit --> check_business_services
        provide_commercial_unit_info --> check_business_services
    }
    
    check_business_services --> done
    done --> [*]
```

## 🛠️ 扩展开发

### 添加新的业务流程

1. 创建新的YAML配置文件
2. 定义状态和转移规则
3. 添加自定义验证规则（可选）
4. 测试流程

### 自定义验证规则

```python
from core.transition import default_transition_engine

def custom_validation_rule(state, user_input, context):
    # 自定义验证逻辑
    return True

# 注册验证规则
default_transition_engine.register_validation_rule("custom_type", custom_validation_rule)
```

### 自定义状态转移规则

```python
def custom_transition_rule(current_state, user_input, context):
    # 自定义转移逻辑
    return "next_state_name"

# 注册转移规则
default_transition_engine.register_transition_rule("state_name", custom_transition_rule)
```

## 📝 日志

系统使用loguru进行日志管理，日志文件保存在`logs/`目录下：

- 控制台输出：彩色格式化日志
- 文件输出：结构化日志，支持轮转和压缩
- 日志级别：INFO, DEBUG, WARNING, ERROR
