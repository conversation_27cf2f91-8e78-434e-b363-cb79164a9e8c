[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "statechat"
version = "1.0.0"
description = "基于配置的状态机引擎"
authors = [
    { name = "Your Name", email = "<EMAIL>" }
]
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "fastapi>=0.104.1",
    "uvicorn[standard]>=0.24.0",
    "sqlalchemy[asyncio]>=2.0.23",
    "aiosqlite>=0.19.0", # SQLite支持
    "aiomysql>=0.2.0", # MySQL支持
    "alembic>=1.13.1", # 数据库迁移
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    "pyyaml>=6.0.1",
    "loguru>=0.7.2",
    "typing-extensions>=4.8.0",
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "httpx>=0.25.2",
    "langchain>=0.0.350",
    "langchain-community>=0.0.10",
    "langchain-openai>=0.0.2",
    "python-multipart>=0.0.20",
    "transitions>=0.9.3",
]

[tool.pytest.ini_options]
pythonpath = ["."]

[tool.hatch.build.targets.wheel]
packages = ["api", "core", "models", "storage", "utils"]

[dependency-groups]
dev = [
    "pytest-asyncio>=1.1.0",
    "pytest-tornasync>=0.6.0.post2",
    "pytest-trio>=0.8.0",
    "pytest-twisted>=1.14.3",
    "twisted>=25.5.0",
]
