"""
API演示脚本 - 展示如何通过HTTP API使用状态机引擎
"""

import asyncio
import traceback

import httpx
import json
from typing import Dict, Any


class StateMachineAPIClient:
    """状态机API客户端"""

    def __init__(self, base_url: str = "http://localhost:5432"):
        self.base_url = base_url
        self.session_id = None

    async def create_session(self, user_id: str = "demo_user") -> Dict[str, Any]:
        """创建新会话"""
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/sessions", json={"user_id": user_id}
            )
            result = response.json()
            if response.status_code == 200:
                self.session_id = result["session_id"]
                print(f"✅ 会话创建成功: {self.session_id}")
            else:
                print(f"❌ 会话创建失败: {result}")
            return result

    async def get_current_state(self) -> Dict[str, Any]:
        """获取当前状态"""
        if not self.session_id:
            raise ValueError("请先创建会话")

        async with httpx.AsyncClient() as client:
            response = await client.get(f"{self.base_url}/sessions/{self.session_id}")
            result = response.json()
            if response.status_code == 200:
                print(f"📍 当前状态: {result['current_state']}")
                print(f"💬 提示: {result['state']['prompt']}")
                if result["state"].get("options"):
                    print(f"🔘 选项: {', '.join(result['state']['options'])}")
            return result

    async def send_input(self, input_type: str, value: Any) -> Dict[str, Any]:
        """发送用户输入"""
        if not self.session_id:
            raise ValueError("请先创建会话")

        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/sessions/{self.session_id}/input",
                json={"input_type": input_type, "value": value},
            )
            result = response.json()
            if response.status_code == 200:
                if result.get("success"):
                    print(f"✅ 输入处理成功")
                    if result.get("completed"):
                        print("🎉 流程已完成!")
                    else:
                        print(f"➡️  转移到状态: {result.get('current_state')}")
                else:
                    print(f"❌ 输入处理失败: {result.get('error')}")
            return result

    async def get_session_info(self) -> Dict[str, Any]:
        """获取会话信息"""
        if not self.session_id:
            raise ValueError("请先创建会话")

        async with httpx.AsyncClient() as client:
            response = await client.get(f"{self.base_url}/sessions/{self.session_id}")
            result = response.json()
            if response.status_code == 200:
                print(f"📊 会话信息:")
                print(f"  - 会话ID: {result['session_id']}")
                print(f"  - 用户ID: {result['user_id']}")
                print(f"  - 流程名称: {result['flow_name']}")
                print(f"  - 当前状态: {result['current_state']}")
                print(f"  - 会话状态: {result['status']}")
            return result

    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{self.base_url}/health")
            result = response.json()
            if response.status_code == 200:
                print(f"💚 服务健康状态: {result['status']}")
            return result


async def demo_api_workflow():
    """演示API工作流程"""
    print("🎯 开始API演示")
    print("=" * 50)

    client = StateMachineAPIClient()

    try:
        # 1. 健康检查
        print("\n1️⃣ 健康检查...")
        await client.health_check()

        # 2. 创建会话
        print("\n2️⃣ 创建会话...")
        await client.create_session("api_demo_user")

        # 3. 获取初始状态
        print("\n3️⃣ 获取初始状态...")
        state = await client.get_current_state()

        # 4. 发送第一个输入
        print("\n4️⃣ 发送文本输入...")
        await client.send_input("text", "Hello from API!")

        # 5. 获取新状态
        print("\n5️⃣ 获取新状态...")
        state = (await client.get_current_state())["state"]

        # 6. 发送选择输入
        if state.get("input_type") == "choice" and state.get("options"):
            print("\n6️⃣ 发送选择输入...")
            await client.send_input("choice", state["options"][0])

        # 7. 继续流程
        print("\n7️⃣ 继续流程...")
        state = await client.get_current_state()

        if state.get("input_type") == "multichoice" and state.get("options"):
            print("\n8️⃣ 发送多选输入...")
            selected_options = (
                state["options"][:2]
                if len(state["options"]) >= 2
                else state["options"][:1]
            )
            await client.send_input("multichoice", selected_options)

        # 8. 获取最终会话信息
        print("\n9️⃣ 获取会话信息...")
        await client.get_session_info()

        print("\n✅ API演示完成!")

    except httpx.ConnectError:
        print("❌ 无法连接到API服务器")
        print("请确保服务器正在运行: python main.py")
    except Exception as e:
        print(f"❌ 演示过程中发生错误: {e}")
        traceback.print_exc()


async def main():
    """主函数"""
    print("🚀 状态机引擎API演示")
    print("=" * 50)

    # 演示基本工作流程
    await demo_api_workflow()


if __name__ == "__main__":
    asyncio.run(main())
