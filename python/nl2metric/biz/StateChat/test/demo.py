"""
状态机引擎演示测试脚本
"""

import asyncio
from pathlib import Path

from loguru import logger

from api.schemas import ProcessInputResponse
from core.state_machine import StateMachine
from models.state import UserInput, InputType
from storage.database import db_manager, init_database
from utils.config_loader import ConfigLoader


class DemoStateMachine(StateMachine):
    """演示状态机，继承自StateMachine以便于测试和演示"""

    def __init__(self, flow_config, db_session=None):
        super().__init__(flow_config, db_session)

    def can_check_registration(self):
        """检查注册状态"""
        logger.info("检查注册状态...")
        return True

    def can_register(self):
        """请选择注册类型"""
        logger.info("请选择注册类型...")
        return True

    def can_collect_personal_info(self):
        return True


async def demo_cafe_license_flow():
    """演示咖啡店许可证申请流程"""
    print("🎯 开始演示咖啡店营业执照申请流程")
    print("=" * 50)

    try:
        # 1. 加载配置
        print("📋 加载流程配置...")
        config_path = f"{Path(__file__).parent.parent}/config/simple_test_flow.yaml"
        flow_config = ConfigLoader.load_flow_config(config_path)
        print(f"✅ 配置加载成功: {flow_config.flow_name} v{flow_config.version}")
        print(f"📊 总状态数: {len(flow_config.states)}")

        # 2. 初始化状态机
        print("\n🔧 初始化状态机...")
        async with await db_manager.get_session() as db_session:
            state_machine = DemoStateMachine(flow_config, db_session)
            print("✅ 状态机初始化成功")

            # 3. 创建用户会话
            print("\n👤 创建用户会话...")
            session = await state_machine.create_session(user_id="demo_user_001")
            print(f"✅ 会话创建成功: {session.session_id}")

            # 4. 开始流程演示
            print("\n🚀 开始流程演示...")
            current_state = await state_machine.get_current_state(session.session_id)
            step = 1

            while current_state.name not in flow_config.final_states:
                print(f"\n--- 步骤 {step}: {current_state.name} ---")
                print(f"💬 提示: {current_state.prompt}")
                input_type_str = (
                    current_state.input_type.value
                    if hasattr(current_state.input_type, "value")
                    else current_state.input_type
                )
                print(f"📝 输入类型: {input_type_str}")

                if current_state.options:
                    print(f"🔘 选项: {', '.join(current_state.options)}")

                if current_state.requires:
                    print(f"📋 必需字段: {', '.join(current_state.requires)}")

                # 模拟用户输入
                user_input = simulate_user_input(current_state)
                print(f"👤 用户输入: {user_input.value}")

                # 处理输入
                result: ProcessInputResponse = await state_machine.process_user_input(
                    session.session_id, user_input
                )

                if not result.success:
                    print(f"❌ 处理失败: {result.error}")
                    break

                if result.completed:
                    print("🎉 流程完成!")
                    break
                print(f"当前状态: {result.current_state}")
                print(f"✅ 处理结果: {result.message}")
                print(f"下一状态: {result.next_states}")
                print(f"进度: {result.progress}")
                # 更新当前状态
                current_state = await state_machine.get_current_state(
                    session.session_id
                )
                step += 1

                # 限制演示步数，避免无限循环
                if step > 10:
                    print("⚠️  演示步数限制，停止演示")
                    break

            # 5. 显示会话摘要
            print("\n📊 会话摘要:")
            summary = await state_machine.get_session_summary(session.session_id)
            print(f"  会话ID: {summary.session_id}")
            print(f"  用户ID: {summary.user_id}")
            print(f"  流程名称: {summary.flow_name}")
            print(f"  当前状态: {summary.current_state}")
            status_str = (
                summary.status.value
                if hasattr(summary.status, "value")
                else summary.status
            )
            print(f"  会话状态: {status_str}")
            print(f"  进度: {summary.progress_percentage:.1f}%")

            # 6. 显示上下文信息
            print("\n📋 会话上下文:")
            context = await state_machine.get_context(session.session_id)
            print(f"  已完成步骤: {len(context.completed_steps)}")
            print(f"  表单数据: {len(context.form_data)} 项")
            print(
                f"  上传文件: {sum(len(files) for files in context.uploaded_files.values())} 个"
            )

            print("\n✅ 演示完成!")

    except Exception as e:
        print(f"❌ 演示过程中发生错误: {e}")
        import traceback

        traceback.print_exc()


def simulate_user_input(state) -> UserInput:
    """模拟用户输入"""
    if state.input_type == InputType.TEXT:
        return UserInput(input_type=InputType.TEXT, value="使用服务A")

    elif state.input_type == InputType.CHOICE:
        if state.options:
            # 选择第一个选项
            return UserInput(input_type=InputType.CHOICE, value=state.options[0])
        return UserInput(input_type=InputType.CHOICE, value="是")

    elif state.input_type == InputType.MULTICHOICE:
        if state.options:
            # 选择前两个选项（如果有的话）
            selected = (
                state.options[:2] if len(state.options) >= 2 else state.options[:1]
            )
            return UserInput(input_type=InputType.MULTICHOICE, value=selected)
        return UserInput(input_type=InputType.MULTICHOICE, value=[])

    elif state.input_type == InputType.FILE:
        # 模拟文件上传
        mock_data = {}
        if state.requires:
            for field in state.requires:
                mock_data[field] = f"模拟_{field}_数据"

        return UserInput(
            input_type=InputType.FILE,
            value=mock_data,
            files=["mock_file_1.pdf", "mock_file_2.jpg"],
        )

    return UserInput(input_type=InputType.TEXT, value="默认输入")


def config_validation():
    """测试配置文件验证"""
    print("\n🔍 测试配置文件验证...")

    config_path = "../config/cafe_license_flow.yaml"
    result = ConfigLoader.validate_config_file(config_path)

    print(f"验证结果: {'✅ 有效' if result['valid'] else '❌ 无效'}")

    if result["errors"]:
        print("错误:")
        for error in result["errors"]:
            print(f"  ❌ {error}")

    if result["warnings"]:
        print("警告:")
        for warning in result["warnings"]:
            print(f"  ⚠️  {warning}")


async def main():
    """主函数"""
    print("🎮 状态机引擎演示程序")
    print("=" * 50)
    await init_database()
    # 测试配置验证
    config_validation()

    # 演示流程
    await demo_cafe_license_flow()


if __name__ == "__main__":
    asyncio.run(main())
