"""
意图识别演示脚本
"""

import asyncio

from loguru import logger

from core.intent import IntentProcessor, IntentType


# 添加项目根目录到Python路径


async def test_intent_classification():
    """测试意图分类"""
    print("🎯 测试意图分类")
    print("=" * 50)

    # 测试用例
    test_cases = [
        # 闲聊意图
        "你好，最近怎么样？",
        "今天天气真不错",
        "讲个笑话吧",
        # 知识库查询意图
        "营业执照办理流程是什么？",
        "如何注册公司？",
        "申请酒牌的要求有哪些？",
        # 业务办理意图
        "我要申请营业执照",
        "继续办理注册手续",
        "确认提交这些文件",
        # 混合意图
        "你好，我想申请营业执照",
        "帮我查询一下执照办理进度",
        "谢谢，请继续下一步",
    ]

    processor = IntentProcessor()

    for text in test_cases:
        print(f"\n💬 用户输入: {text}")
        intent = await processor.process(text)
        print(f"🎯 识别意图: {intent}")


async def test_intent_processing():
    """测试意图处理"""
    print("\n🔄 测试意图处理")
    print("=" * 50)

    test_cases = [
        {
            "text": "你好，最近怎么样？",
            "context": {
                "session_id": "test_session_1",
                "current_state": "welcome_user",
                "flow_name": "cafe_license_flow",
            },
        },
        {
            "text": "营业执照怎么申请？",
            "context": {
                "session_id": "test_session_2",
                "current_state": "welcome_user",
                "flow_name": "cafe_license_flow",
            },
        },
        {
            "text": "我要申请营业执照",
            "context": {
                "session_id": "test_session_3",
                "current_state": "welcome_user",
                "flow_name": "cafe_license_flow",
            },
        },
    ]

    processor = IntentProcessor()

    for case in test_cases:
        print(f"\n💬 用户输入: {case['text']}")
        print(f"📊 上下文: {case['context']}")

        result = await processor.handle_input(case["text"], case["context"])

        print(f"🎯 意图类型: {result['intent']}")
        if result.get("response"):
            print(f"💡 处理响应: {result['response']}")
        print(f"🔄 继续流程: {result['should_continue_flow']}")


async def test_custom_intent_rules():
    """测试自定义意图规则"""
    print("\n🎮 测试自定义意图规则")
    print("=" * 50)

    # 创建自定义处理器
    processor = IntentProcessor()

    # 添加自定义意图模式
    processor.intent_patterns[IntentType.CHAT].extend(["打招呼", "问候", "闲聊"])

    processor.intent_patterns[IntentType.RAG_QUERY].extend(
        ["查一下", "帮我找", "需要了解"]
    )

    processor.intent_patterns[IntentType.BUSINESS].extend(["办理", "处理", "填写"])

    # 测试用例
    test_cases = ["我想打招呼", "帮我找一下相关政策", "我要办理业务"]

    for text in test_cases:
        print(f"\n💬 用户输入: {text}")
        intent = await processor.process(text)
        print(f"🎯 识别意图: {intent.value}")


async def main():
    """主函数"""
    print("🚀 意图识别演示程序")
    print("=" * 50)

    try:
        # 测试意图分类
        await test_intent_classification()

        # 测试意图处理
        await test_intent_processing()

        # 测试自定义规则
        await test_custom_intent_rules()

        print("\n✅ 演示完成!")

    except Exception as e:
        logger.error(f"演示过程中发生错误: {e}")


if __name__ == "__main__":
    asyncio.run(main())
