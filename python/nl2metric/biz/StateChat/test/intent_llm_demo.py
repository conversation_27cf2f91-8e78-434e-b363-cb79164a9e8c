"""
意图识别和LLM功能演示脚本
"""

import asyncio
import os

from loguru import logger

from core.intent import IntentProcessor


# 添加项目根目录到Python路径


async def test_llm_intent_classification():
    """测试LLM意图分类"""
    print("\n🎯 测试LLM意图分类")
    print("=" * 50)

    # 测试用例
    test_cases = [
        # 闲聊意图
        "今天天气不错啊，我们聊聊天吧",
        "你最近过得怎么样？",
        "我听说你很聪明，能跟我讲个笑话吗？",
        # 知识库查询意图
        "新加坡注册公司需要准备哪些材料？",
        "请问营业执照办理流程是怎样的？",
        "食品经营许可证的要求是什么？",
        # 业务办理意图
        "我现在就要开始申请营业执照",
        "帮我提交这份表格",
        "我想继续之前的申请流程",
        # 混合意图
        "你好啊，我想咨询一下营业执照的事",
        "麻烦帮我解释一下这个流程，然后我要开始申请",
        "谢谢你的帮助，我现在就开始办理",
    ]

    # 创建意图处理器
    processor = IntentProcessor(use_llm=True)

    for text in test_cases:
        print(f"\n💬 用户输入: {text}")
        intent = await processor.process(text)
        print(f"🎯 识别意图: {intent.value}")


async def test_llm_chat_responses():
    """测试LLM聊天回复"""
    print("\n🗣️ 测试LLM聊天回复")
    print("=" * 50)

    processor = IntentProcessor(use_llm=True)

    test_cases = [
        {
            "text": "你好啊，今天天气真不错",
            "context": {"session_id": "test_1", "current_state": "welcome"},
        },
        {
            "text": "你能介绍一下新加坡的餐饮行业吗？",
            "context": {"session_id": "test_2", "current_state": "welcome"},
        },
        {
            "text": "我想开一家咖啡店，你觉得怎么样？",
            "context": {"session_id": "test_3", "current_state": "welcome"},
        },
    ]

    for case in test_cases:
        print(f"\n💬 用户输入: {case['text']}")
        result = await processor.handle_input(case["text"], case["context"])
        print(f"🤖 意图类型: {result['intent']}")
        if result.get("response"):
            print(f"💡 AI回复: {result['response']}")
        print(f"🔄 继续流程: {result['should_continue_flow']}")


async def test_llm_knowledge_query():
    """测试LLM知识库查询"""
    print("\n📚 测试LLM知识库查询")
    print("=" * 50)

    processor = IntentProcessor(use_llm=True)

    test_cases = [
        "新加坡咖啡店营业执照申请需要什么条件？",
        "食品安全认证怎么办理？",
        "营业场所要求有哪些？",
        "员工培训需要注意什么？",
    ]

    for text in test_cases:
        print(f"\n❓ 用户问题: {text}")
        result = await processor.handle_input(text, {"session_id": "test"})
        print(f"🤖 意图类型: {result['intent']}")
        if result.get("response"):
            print(f"💡 知识库回复: {result['response']}")


async def test_fallback_to_rules():
    """测试LLM失败时回退到规则"""
    print("\n🔄 测试回退机制")
    print("=" * 50)

    # 创建一个模拟的处理器，故意使用错误的配置
    processor = IntentProcessor(use_llm=True)

    test_cases = ["你好，我想聊天", "帮我查询一下", "我要申请执照"]

    print("⚠️ 将使用无效的API密钥测试回退机制...")
    os.environ["OPENAI_API_KEY"] = "invalid_key"

    for text in test_cases:
        print(f"\n💬 用户输入: {text}")
        intent = await processor.process(text)
        print(f"🎯 识别意图: {intent.value} (应该使用规则匹配)")


async def compare_rule_and_llm():
    """比较规则和LLM的识别结果"""
    print("\n🔍 规则vs.LLM比较")
    print("=" * 50)

    rule_processor = IntentProcessor(use_llm=False)
    llm_processor = IntentProcessor(use_llm=True)

    test_cases = [
        "这个流程真复杂，能解释一下吗？",
        "我想了解更多关于许可证的信息",
        "好的，那我现在就开始申请",
        "这些要求太多了，有点烦",
    ]

    for text in test_cases:
        print(f"\n💬 用户输入: {text}")
        rule_intent = await rule_processor.process(text)
        llm_intent = await llm_processor.process(text)
        print(f"📏 规则识别: {rule_intent.value}")
        print(f"🤖 LLM识别: {llm_intent.value}")


async def main():
    """主函数"""
    print("🚀 意图识别与LLM功能演示")
    print("=" * 50)

    # 检查OpenAI API密钥
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("\n❌ 未设置OPENAI_API_KEY环境变量")
        print("请设置环境变量后再运行测试:")
        print("  export OPENAI_API_KEY='your-api-key'")
        return

    print(f"\n✅ 使用API密钥: {api_key[:6]}...{api_key[-4:]}")

    try:
        # 运行测试
        await test_llm_intent_classification()
        await test_llm_chat_responses()
        await test_llm_knowledge_query()
        await test_fallback_to_rules()
        await compare_rule_and_llm()

        print("\n✅ 演示完成!")

    except Exception as e:
        logger.error(f"演示过程中发生错误: {e}")


if __name__ == "__main__":
    asyncio.run(main())
