/**
 * 流程和状态信息服务
 */
import api from './api';
import { 
  FlowConfig, 
  StateInfoResponse, 
  ValidationResultResponse, 
  HealthCheckResponse 
} from '../types/responses';

// API端点
const ENDPOINTS = {
  FLOWS: '/flows',
  FLOW_DETAIL: (name: string) => `/flows/${name}`,
  STATE_INFO: (flowName: string, stateName: string) => `/flows/${flowName}/states/${stateName}`,
  VALIDATE_CONFIG: '/validate-config',
  HEALTH: '/health',
};

/**
 * 流程服务类
 */
export class FlowService {
  /**
   * 获取可用流程列表
   * @returns 流程配置列表
   */
  static async listFlows(): Promise<FlowConfig[]> {
    return api.get<FlowConfig[]>(ENDPOINTS.FLOWS);
  }

  /**
   * 获取流程详情
   * @param flowName 流程名称
   * @returns 流程配置
   */
  static async getFlowDetail(flowName: string): Promise<FlowConfig> {
    return api.get<FlowConfig>(ENDPOINTS.FLOW_DETAIL(flowName));
  }

  /**
   * 获取状态信息
   * @param flowName 流程名称
   * @param stateName 状态名称
   * @returns 状态信息
   */
  static async getStateInfo(flowName: string, stateName: string): Promise<StateInfoResponse> {
    return api.get<StateInfoResponse>(ENDPOINTS.STATE_INFO(flowName, stateName));
  }

  /**
   * 验证配置文件
   * @param configPath 配置文件路径
   * @returns 验证结果
   */
  static async validateConfig(configPath: string): Promise<ValidationResultResponse> {
    return api.post<ValidationResultResponse>(ENDPOINTS.VALIDATE_CONFIG, { config_path: configPath });
  }

  /**
   * 获取健康检查状态
   * @returns 健康检查响应
   */
  static async healthCheck(): Promise<HealthCheckResponse> {
    return api.get<HealthCheckResponse>(ENDPOINTS.HEALTH);
  }

  /**
   * 计算流程进度
   * @param flowConfig 流程配置
   * @param currentState 当前状态
   * @param completedStates 已完成状态列表
   * @returns 进度百分比
   */
  static calculateProgress(
    flowConfig: FlowConfig, 
    currentState: string, 
    completedStates: string[] = []
  ): number {
    // 如果是最终状态，返回100%
    if (flowConfig.final_states.includes(currentState)) {
      return 100;
    }

    // 计算总状态数（不包括最终状态）
    const totalStates = flowConfig.total_states - flowConfig.final_states.length;
    
    // 计算已完成状态数
    const completedCount = completedStates.length;
    
    // 计算进度百分比
    const progress = (completedCount / totalStates) * 100;
    
    // 确保进度在0-100之间
    return Math.min(Math.max(progress, 0), 100);
  }

  /**
   * 获取状态显示名称
   * @param stateName 状态名称
   * @returns 显示名称
   */
  static getStateDisplayName(stateName: string): string {
    // 将下划线和连字符替换为空格
    const nameWithSpaces = stateName.replace(/[_-]/g, ' ');
    
    // 将首字母大写
    return nameWithSpaces
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }
}

export default FlowService;