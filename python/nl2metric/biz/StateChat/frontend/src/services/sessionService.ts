/**
 * 会话管理服务
 */
import api from './api';
import { 
  CreateSessionRequest, 
  UserInputRequest 
} from '../types/requests';
import { 
  CreateSessionResponse, 
  ProcessInputResponse, 
  SessionDetail, 
  SuccessResponse,
  PaginatedResponse,
  SessionSummary
} from '../types/responses';
import { UserInput } from '../types/api';

// API端点
const ENDPOINTS = {
  SESSIONS: '/sessions',
  SESSION_DETAIL: (id: string) => `/sessions/${id}`,
  SESSION_INPUT: (id: string) => `/sessions/${id}/input`,
  SESSION_PAUSE: (id: string) => `/sessions/${id}/pause`,
  SESSION_RESUME: (id: string) => `/sessions/${id}/resume`,
  SESSION_FILES: (id: string) => `/sessions/${id}/files`,
};

/**
 * 会话服务类
 */
export class SessionService {
  /**
   * 创建新会话
   * @param request 创建会话请求
   * @returns 创建会话响应
   */
  static async createSession(request: CreateSessionRequest = {}): Promise<CreateSessionResponse> {
    return api.post<CreateSessionResponse>(ENDPOINTS.SESSIONS, request);
  }

  /**
   * 获取会话详情
   * @param sessionId 会话ID
   * @returns 会话详情
   */
  static async getSessionDetail(sessionId: string): Promise<SessionDetail> {
    return api.get<SessionDetail>(ENDPOINTS.SESSION_DETAIL(sessionId));
  }

  /**
   * 获取会话列表
   * @param page 页码
   * @param size 每页大小
   * @returns 分页会话列表
   */
  static async listSessions(page: number = 1, size: number = 10): Promise<PaginatedResponse<SessionSummary>> {
    return api.get<PaginatedResponse<SessionSummary>>(ENDPOINTS.SESSIONS, { page, size });
  }

  /**
   * 处理用户输入
   * @param sessionId 会话ID
   * @param input 用户输入
   * @returns 处理结果
   */
  static async processInput(sessionId: string, input: UserInput): Promise<ProcessInputResponse> {
    const request: UserInputRequest = {
      input_type: input.input_type,
      value: input.value,
      files: input.files
    };
    return api.post<ProcessInputResponse>(ENDPOINTS.SESSION_INPUT(sessionId), request);
  }

  /**
   * 暂停会话
   * @param sessionId 会话ID
   * @returns 成功响应
   */
  static async pauseSession(sessionId: string): Promise<SuccessResponse> {
    return api.put<SuccessResponse>(ENDPOINTS.SESSION_PAUSE(sessionId));
  }

  /**
   * 恢复会话
   * @param sessionId 会话ID
   * @returns 成功响应
   */
  static async resumeSession(sessionId: string): Promise<SuccessResponse> {
    return api.put<SuccessResponse>(ENDPOINTS.SESSION_RESUME(sessionId));
  }

  /**
   * 删除会话
   * @param sessionId 会话ID
   * @returns 成功响应
   */
  static async deleteSession(sessionId: string): Promise<SuccessResponse> {
    return api.del<SuccessResponse>(ENDPOINTS.SESSION_DETAIL(sessionId));
  }

  /**
   * 处理流式输入响应
   * @param sessionId 会话ID
   * @param input 用户输入
   * @param onChunk 块处理回调
   */
  static async processStreamInput(
    sessionId: string, 
    input: UserInput, 
    onChunk: (chunk: any) => void
  ): Promise<void> {
    const request: UserInputRequest = {
      input_type: input.input_type,
      value: input.value,
      files: input.files
    };
    
    return api.handleStreamResponse(
      ENDPOINTS.SESSION_INPUT(sessionId),
      request,
      onChunk
    );
  }

  /**
   * 上传文件
   * @param sessionId 会话ID
   * @param file 文件
   * @param onProgress 进度回调
   * @returns 文件上传响应
   */
  static async uploadFile(
    sessionId: string, 
    file: File, 
    onProgress?: (percent: number) => void
  ): Promise<any> {
    return api.uploadFile(ENDPOINTS.SESSION_FILES(sessionId), file, onProgress);
  }
}

export default SessionService;