/**
 * API客户端工具
 */
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import { ErrorType, AppError } from '../types/api';

// API基础URL
const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:5432';

// 创建axios实例
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    // 可以在这里添加认证token等
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  (error: AxiosError) => {
    const appError: AppError = {
      type: ErrorType.API_ERROR,
      message: '请求失败',
      details: error.response?.data || error.message,
    };

    // 根据错误类型设置错误信息
    if (error.response) {
      // 服务器返回错误
      switch (error.response.status) {
        case 400:
          appError.type = ErrorType.VALIDATION_ERROR;
          appError.message = '请求参数错误';
          break;
        case 401:
          appError.type = ErrorType.AUTHENTICATION_ERROR;
          appError.message = '认证失败';
          break;
        case 404:
          appError.message = '请求的资源不存在';
          break;
        case 500:
          appError.message = '服务器内部错误';
          break;
        default:
          appError.message = `请求失败，状态码: ${error.response.status}`;
      }
    } else if (error.request) {
      // 请求发送但没有收到响应
      appError.type = ErrorType.NETWORK_ERROR;
      appError.message = '网络错误，无法连接到服务器';
    } else {
      // 请求配置错误
      appError.type = ErrorType.UNKNOWN_ERROR;
      appError.message = '请求配置错误';
    }

    return Promise.reject(appError);
  }
);

// 通用请求方法
export const request = async <T>(config: AxiosRequestConfig): Promise<T> => {
  try {
    const response: AxiosResponse<T> = await apiClient(config);
    return response.data;
  } catch (error) {
    throw error;
  }
};

// GET请求
export const get = <T>(url: string, params?: any, config?: AxiosRequestConfig): Promise<T> => {
  return request<T>({
    method: 'GET',
    url,
    params,
    ...config,
  });
};

// POST请求
export const post = <T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
  return request<T>({
    method: 'POST',
    url,
    data,
    ...config,
  });
};

// PUT请求
export const put = <T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
  return request<T>({
    method: 'PUT',
    url,
    data,
    ...config,
  });
};

// DELETE请求
export const del = <T>(url: string, config?: AxiosRequestConfig): Promise<T> => {
  return request<T>({
    method: 'DELETE',
    url,
    ...config,
  });
};

// 文件上传请求
export const uploadFile = <T>(url: string, file: File, onProgress?: (percent: number) => void): Promise<T> => {
  const formData = new FormData();
  formData.append('file', file);

  return request<T>({
    method: 'POST',
    url,
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    onUploadProgress: (progressEvent) => {
      if (onProgress && progressEvent.total) {
        const percent = Math.round((progressEvent.loaded * 100) / progressEvent.total);
        onProgress(percent);
      }
    },
  });
};

// 处理流式响应
export const handleStreamResponse = async <T>(
  url: string, 
  data?: any, 
  onChunk?: (chunk: T) => void,
  config?: AxiosRequestConfig
): Promise<void> => {
  try {
    const response = await apiClient({
      method: 'POST',
      url,
      data,
      responseType: 'stream',
      ...config,
    });

    const reader = response.data.getReader();
    const decoder = new TextDecoder();

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      const chunk = decoder.decode(value);
      const lines = chunk.split('\n').filter(line => line.trim());

      for (const line of lines) {
        try {
          const parsedChunk = JSON.parse(line) as T;
          if (onChunk) {
            onChunk(parsedChunk);
          }
        } catch (e) {
          console.error('Failed to parse chunk:', line, e);
        }
      }
    }
  } catch (error) {
    throw error;
  }
};

export default {
  get,
  post,
  put,
  del,
  uploadFile,
  handleStreamResponse,
};