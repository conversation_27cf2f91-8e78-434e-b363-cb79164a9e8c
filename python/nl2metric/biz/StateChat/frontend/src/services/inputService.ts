/**
 * 用户输入处理服务
 */
import api from './api';
import { SessionService } from './sessionService';
import { InputType, UserInput } from '../types/api';
import { ProcessInputResponse, FileUploadResponse } from '../types/responses';

/**
 * 输入处理服务类
 */
export class InputService {
  /**
   * 处理文本输入
   * @param sessionId 会话ID
   * @param value 文本值
   * @returns 处理结果
   */
  static async processTextInput(sessionId: string, value: string): Promise<ProcessInputResponse> {
    const input: UserInput = {
      input_type: InputType.TEXT,
      value: value
    };
    return SessionService.processInput(sessionId, input);
  }

  /**
   * 处理选择输入
   * @param sessionId 会话ID
   * @param value 选择值
   * @returns 处理结果
   */
  static async processChoiceInput(sessionId: string, value: string): Promise<ProcessInputResponse> {
    const input: UserInput = {
      input_type: InputType.CHOICE,
      value: value
    };
    return SessionService.processInput(sessionId, input);
  }

  /**
   * 处理多选输入
   * @param sessionId 会话ID
   * @param values 多选值数组
   * @returns 处理结果
   */
  static async processMultiChoiceInput(sessionId: string, values: string[]): Promise<ProcessInputResponse> {
    const input: UserInput = {
      input_type: InputType.MULTICHOICE,
      value: values
    };
    return SessionService.processInput(sessionId, input);
  }

  /**
   * 处理文件输入
   * @param sessionId 会话ID
   * @param filePaths 已上传文件路径数组
   * @param formData 表单数据
   * @returns 处理结果
   */
  static async processFileInput(
    sessionId: string, 
    filePaths: string[], 
    formData?: Record<string, any>
  ): Promise<ProcessInputResponse> {
    const input: UserInput = {
      input_type: InputType.FILE,
      value: formData || {},
      files: filePaths
    };
    return SessionService.processInput(sessionId, input);
  }

  /**
   * 上传单个文件
   * @param sessionId 会话ID
   * @param file 文件对象
   * @param onProgress 进度回调
   * @returns 文件上传响应
   */
  static async uploadFile(
    sessionId: string, 
    file: File, 
    onProgress?: (percent: number) => void
  ): Promise<FileUploadResponse> {
    return SessionService.uploadFile(sessionId, file, onProgress);
  }

  /**
   * 上传多个文件
   * @param sessionId 会话ID
   * @param files 文件对象数组
   * @param onProgress 进度回调
   * @returns 文件上传响应数组
   */
  static async uploadMultipleFiles(
    sessionId: string, 
    files: File[], 
    onProgress?: (file: File, percent: number) => void
  ): Promise<FileUploadResponse[]> {
    const uploadPromises = files.map(file => 
      this.uploadFile(
        sessionId, 
        file, 
        percent => onProgress && onProgress(file, percent)
      )
    );
    
    return Promise.all(uploadPromises);
  }

  /**
   * 处理流式输入响应
   * @param sessionId 会话ID
   * @param input 用户输入
   * @param onChunk 块处理回调
   */
  static async processStreamInput(
    sessionId: string, 
    input: UserInput, 
    onChunk: (chunk: any) => void
  ): Promise<void> {
    return SessionService.processStreamInput(sessionId, input, onChunk);
  }

  /**
   * 验证输入值
   * @param inputType 输入类型
   * @param value 输入值
   * @param options 选项（用于选择和多选类型）
   * @param requires 必需字段（用于文件类型）
   * @returns 验证结果和错误消息
   */
  static validateInput(
    inputType: InputType, 
    value: any, 
    options?: string[], 
    requires?: string[]
  ): { valid: boolean; message?: string } {
    switch (inputType) {
      case InputType.TEXT:
        if (!value || typeof value !== 'string' || value.trim() === '') {
          return { valid: false, message: '请输入文本内容' };
        }
        return { valid: true };
        
      case InputType.CHOICE:
        if (!value || typeof value !== 'string' || value.trim() === '') {
          return { valid: false, message: '请选择一个选项' };
        }
        if (options && !options.includes(value)) {
          return { valid: false, message: '无效的选择项' };
        }
        return { valid: true };
        
      case InputType.MULTICHOICE:
        if (!Array.isArray(value) || value.length === 0) {
          return { valid: false, message: '请至少选择一个选项' };
        }
        if (options && value.some(item => !options.includes(item))) {
          return { valid: false, message: '包含无效的选择项' };
        }
        return { valid: true };
        
      case InputType.FILE:
        if (!Array.isArray(value.files) || value.files.length === 0) {
          return { valid: false, message: '请上传文件' };
        }
        
        // 检查必需字段
        if (requires && requires.length > 0) {
          const formData = value.formData || {};
          const missingFields = requires.filter(field => !formData[field]);
          
          if (missingFields.length > 0) {
            return { 
              valid: false, 
              message: `缺少必需字段: ${missingFields.join(', ')}` 
            };
          }
        }
        
        return { valid: true };
        
      default:
        return { valid: false, message: '不支持的输入类型' };
    }
  }
}

export default InputService;