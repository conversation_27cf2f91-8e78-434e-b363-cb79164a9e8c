import React, { useState } from 'react';
import {
  Upload,
  Button,
  Card,
  List,
  Progress,
  Space,
  Typography,
  Alert,
  message,
  Modal
} from 'antd';
import {
  UploadOutlined,
  InboxOutlined,
  FileOutlined,
  DeleteOutlined,
  EyeOutlined,
  LoadingOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import type { UploadProps, UploadFile } from 'antd';
import { SessionService } from '../../services/sessionService';

const { Dragger } = Upload;
const { Text, Title } = Typography;

interface FileUploadProps {
  sessionId: string;
  maxFiles?: number;
  maxSize?: number; // MB
  acceptedTypes?: string[];
  onUploadComplete?: (files: UploadedFile[]) => void;
  onUploadError?: (error: string) => void;
  disabled?: boolean;
}

interface UploadedFile {
  filename: string;
  file_path: string;
  file_size: number;
  content_type: string;
  upload_time: string;
}

const FileUpload: React.FC<FileUploadProps> = ({
  sessionId,
  maxFiles = 5,
  maxSize = 10, // 10MB
  acceptedTypes = ['*'],
  onUploadComplete,
  onUploadError,
  disabled = false
}) => {
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [uploading, setUploading] = useState(false);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewFile, setPreviewFile] = useState<UploadFile | null>(null);

  // 文件上传前的检查
  const beforeUpload = (file: File): boolean => {
    // 检查文件大小
    const isLtMaxSize = file.size / 1024 / 1024 < maxSize;
    if (!isLtMaxSize) {
      message.error(`文件大小不能超过 ${maxSize}MB`);
      return false;
    }

    // 检查文件数量
    if (fileList.length >= maxFiles) {
      message.error(`最多只能上传 ${maxFiles} 个文件`);
      return false;
    }

    // 检查文件类型
    if (acceptedTypes.length > 0 && !acceptedTypes.includes('*')) {
      const fileType = file.type;
      const fileName = file.name.toLowerCase();
      const isAccepted = acceptedTypes.some(type => {
        if (type.startsWith('.')) {
          return fileName.endsWith(type);
        }
        return fileType.includes(type);
      });

      if (!isAccepted) {
        message.error(`不支持的文件类型，支持的类型: ${acceptedTypes.join(', ')}`);
        return false;
      }
    }

    return true;
  };

  // 自定义上传
  const customRequest: UploadProps['customRequest'] = async ({
    file,
    onSuccess,
    onError,
    onProgress
  }) => {
    try {
      setUploading(true);
      
      const response = await SessionService.uploadFile(
        sessionId,
        file as File,
        (percent) => {
          onProgress?.({ percent });
        }
      );

      const uploadedFile: UploadedFile = {
        filename: response.filename,
        file_path: response.file_path,
        file_size: response.file_size,
        content_type: response.content_type,
        upload_time: response.upload_time
      };

      setUploadedFiles(prev => [...prev, uploadedFile]);
      onSuccess?.(response);
      message.success(`${response.filename} 上传成功`);
      
      // 通知父组件
      onUploadComplete?.([...uploadedFiles, uploadedFile]);
    } catch (error: any) {
      onError?.(error);
      message.error(`上传失败: ${error.message}`);
      onUploadError?.(error.message);
    } finally {
      setUploading(false);
    }
  };

  // 文件列表变化
  const handleChange: UploadProps['onChange'] = ({ fileList: newFileList }) => {
    setFileList(newFileList);
  };

  // 移除文件
  const handleRemove = (file: UploadFile) => {
    const updatedFiles = uploadedFiles.filter(f => f.filename !== file.name);
    setUploadedFiles(updatedFiles);
    onUploadComplete?.(updatedFiles);
  };

  // 预览文件
  const handlePreview = (file: UploadFile) => {
    setPreviewFile(file);
    setPreviewVisible(true);
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 获取文件状态图标
  const getFileStatusIcon = (file: UploadFile) => {
    if (file.status === 'uploading') {
      return <LoadingOutlined style={{ color: '#1890ff' }} />;
    } else if (file.status === 'done') {
      return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
    } else if (file.status === 'error') {
      return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />;
    }
    return <FileOutlined />;
  };

  return (
    <Card title="文件上传" style={{ width: '100%' }}>
      <Space direction="vertical" style={{ width: '100%' }} size="large">
        {/* 上传提示 */}
        <Alert
          message="上传说明"
          description={
            <ul style={{ margin: 0, paddingLeft: '20px' }}>
              <li>最多上传 {maxFiles} 个文件</li>
              <li>单个文件大小不超过 {maxSize}MB</li>
              {acceptedTypes.length > 0 && !acceptedTypes.includes('*') && (
                <li>支持的文件类型: {acceptedTypes.join(', ')}</li>
              )}
            </ul>
          }
          type="info"
          showIcon
        />

        {/* 拖拽上传区域 */}
        <Dragger
          multiple
          fileList={fileList}
          beforeUpload={beforeUpload}
          customRequest={customRequest}
          onChange={handleChange}
          onRemove={handleRemove}
          onPreview={handlePreview}
          disabled={disabled || uploading}
          showUploadList={{
            showPreviewIcon: true,
            showRemoveIcon: true,
            showDownloadIcon: false
          }}
        >
          <p className="ant-upload-drag-icon">
            <InboxOutlined style={{ fontSize: '48px', color: '#1890ff' }} />
          </p>
          <p className="ant-upload-text">
            点击或拖拽文件到此区域上传
          </p>
          <p className="ant-upload-hint">
            支持单个或批量上传，严禁上传公司数据或其他敏感信息
          </p>
        </Dragger>

        {/* 或者使用按钮上传 */}
        <div style={{ textAlign: 'center' }}>
          <Upload
            fileList={[]}
            beforeUpload={beforeUpload}
            customRequest={customRequest}
            disabled={disabled || uploading}
            showUploadList={false}
          >
            <Button
              icon={uploading ? <LoadingOutlined /> : <UploadOutlined />}
              disabled={disabled || uploading}
              loading={uploading}
            >
              {uploading ? '上传中...' : '选择文件'}
            </Button>
          </Upload>
        </div>

        {/* 已上传文件列表 */}
        {uploadedFiles.length > 0 && (
          <div>
            <Title level={5}>已上传文件 ({uploadedFiles.length})</Title>
            <List
              size="small"
              dataSource={uploadedFiles}
              renderItem={(file, index) => (
                <List.Item
                  key={index}
                  actions={[
                    <Button
                      type="text"
                      size="small"
                      icon={<EyeOutlined />}
                      onClick={() => {
                        // 这里可以实现文件预览功能
                        message.info('文件预览功能待实现');
                      }}
                    >
                      预览
                    </Button>,
                    <Button
                      type="text"
                      size="small"
                      icon={<DeleteOutlined />}
                      danger
                      onClick={() => {
                        const updatedFiles = uploadedFiles.filter((_, i) => i !== index);
                        setUploadedFiles(updatedFiles);
                        onUploadComplete?.(updatedFiles);
                      }}
                    >
                      删除
                    </Button>
                  ]}
                >
                  <List.Item.Meta
                    avatar={<FileOutlined style={{ fontSize: '16px', color: '#1890ff' }} />}
                    title={file.filename}
                    description={
                      <Space>
                        <Text type="secondary">{formatFileSize(file.file_size)}</Text>
                        <Text type="secondary">{file.content_type}</Text>
                        <Text type="secondary">
                          {new Date(file.upload_time).toLocaleString()}
                        </Text>
                      </Space>
                    }
                  />
                </List.Item>
              )}
            />
          </div>
        )}
      </Space>

      {/* 预览模态框 */}
      <Modal
        open={previewVisible}
        title={previewFile?.name}
        footer={null}
        onCancel={() => setPreviewVisible(false)}
      >
        <div style={{ textAlign: 'center' }}>
          <FileOutlined style={{ fontSize: '64px', color: '#1890ff' }} />
          <div style={{ marginTop: '16px' }}>
            <Text>文件预览功能待实现</Text>
          </div>
        </div>
      </Modal>
    </Card>
  );
};

export default FileUpload;
