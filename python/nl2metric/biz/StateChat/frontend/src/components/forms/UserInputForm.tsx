import React, { useState } from 'react';
import {
  Form,
  Input,
  Select,
  Checkbox,
  Button,
  Space,
  Typography,
  Card,
  Alert,
  Upload,
  message
} from 'antd';
import {
  SendOutlined,
  UploadOutlined,
  FileOutlined,
  LoadingOutlined
} from '@ant-design/icons';
import type { UploadProps } from 'antd';
import { InputType, UserInput } from '../../types/api';
import { SessionService } from '../../services/sessionService';

const { TextArea } = Input;
const { Option } = Select;
const { Text } = Typography;

interface UserInputFormProps {
  sessionId: string;
  prompt: string;
  inputType: InputType;
  options?: string[];
  requires?: string[];
  onSubmit: (input: UserInput) => void;
  onStreamSubmit?: (input: UserInput) => void;
  loading?: boolean;
  disabled?: boolean;
  enableStreaming?: boolean;
}

const UserInputForm: React.FC<UserInputFormProps> = ({
  sessionId,
  prompt,
  inputType,
  options = [],
  requires = [],
  onSubmit,
  onStreamSubmit,
  loading = false,
  disabled = false,
  enableStreaming = false
}) => {
  const [form] = Form.useForm();
  const [uploadedFiles, setUploadedFiles] = useState<string[]>([]);
  const [uploading, setUploading] = useState(false);

  // 处理表单提交
  const handleSubmit = async (values: any) => {
    try {
      let inputValue: string | string[] | Record<string, any>;

      switch (inputType) {
        case InputType.TEXT:
          inputValue = values.text || '';
          break;
        case InputType.CHOICE:
          inputValue = values.choice || '';
          break;
        case InputType.MULTICHOICE:
          inputValue = values.multichoice || [];
          break;
        case InputType.FILE:
          inputValue = values.text || '';
          break;
        default:
          inputValue = values.text || '';
      }

      const userInput: UserInput = {
        input_type: inputType,
        value: inputValue,
        files: uploadedFiles.length > 0 ? uploadedFiles : undefined
      };

      onSubmit(userInput);
      
      // 重置表单（除了文件上传）
      if (inputType !== InputType.FILE) {
        form.resetFields();
      }
    } catch (error: any) {
      message.error(error.message || '提交失败');
    }
  };

  // 文件上传配置
  const uploadProps: UploadProps = {
    name: 'file',
    multiple: true,
    customRequest: async ({ file, onSuccess, onError, onProgress }) => {
      try {
        setUploading(true);
        const response = await SessionService.uploadFile(
          sessionId,
          file as File,
          (percent) => {
            onProgress?.({ percent });
          }
        );
        
        setUploadedFiles(prev => [...prev, response.file_path]);
        onSuccess?.(response);
        message.success(`${(file as File).name} 上传成功`);
      } catch (error: any) {
        onError?.(error);
        message.error(`${(file as File).name} 上传失败`);
      } finally {
        setUploading(false);
      }
    },
    onRemove: (file) => {
      if (file.response?.file_path) {
        setUploadedFiles(prev => prev.filter(path => path !== file.response.file_path));
      }
    },
    showUploadList: {
      showPreviewIcon: false,
      showRemoveIcon: !disabled && !loading
    }
  };

  // 渲染输入组件
  const renderInputComponent = () => {
    switch (inputType) {
      case InputType.TEXT:
        return (
          <Form.Item
            name="text"
            rules={[{ required: true, message: '请输入内容' }]}
          >
            <TextArea
              rows={4}
              placeholder="请输入您的回答..."
              disabled={disabled}
            />
          </Form.Item>
        );

      case InputType.CHOICE:
        return (
          <Form.Item
            name="choice"
            rules={[{ required: true, message: '请选择一个选项' }]}
          >
            <Select
              placeholder="请选择一个选项"
              disabled={disabled}
              size="large"
            >
              {options.map((option, index) => (
                <Option key={index} value={option}>
                  {option}
                </Option>
              ))}
            </Select>
          </Form.Item>
        );

      case InputType.MULTICHOICE:
        return (
          <Form.Item
            name="multichoice"
            rules={[{ required: true, message: '请至少选择一个选项' }]}
          >
            <Checkbox.Group disabled={disabled}>
              <Space direction="vertical">
                {options.map((option, index) => (
                  <Checkbox key={index} value={option}>
                    {option}
                  </Checkbox>
                ))}
              </Space>
            </Checkbox.Group>
          </Form.Item>
        );

      case InputType.FILE:
        return (
          <Space direction="vertical" style={{ width: '100%' }}>
            <Form.Item name="text">
              <TextArea
                rows={3}
                placeholder="请描述您要上传的文件或添加说明..."
                disabled={disabled}
              />
            </Form.Item>
            <Form.Item>
              <Upload {...uploadProps} disabled={disabled || loading}>
                <Button 
                  icon={uploading ? <LoadingOutlined /> : <UploadOutlined />}
                  disabled={disabled || loading}
                  loading={uploading}
                >
                  {uploading ? '上传中...' : '选择文件'}
                </Button>
              </Upload>
            </Form.Item>
          </Space>
        );

      default:
        return (
          <Form.Item
            name="text"
            rules={[{ required: true, message: '请输入内容' }]}
          >
            <Input
              placeholder="请输入内容..."
              disabled={disabled}
            />
          </Form.Item>
        );
    }
  };

  return (
    <Card>
      <Space direction="vertical" style={{ width: '100%' }}>
        {/* 提示信息 */}
        <div>
          <Text strong style={{ fontSize: '16px', display: 'block', marginBottom: '8px' }}>
            {prompt}
          </Text>
          {requires.length > 0 && (
            <Alert
              message="必填信息"
              description={
                <ul style={{ margin: 0, paddingLeft: '20px' }}>
                  {requires.map((req, index) => (
                    <li key={index}>{req}</li>
                  ))}
                </ul>
              }
              type="info"
              showIcon
              style={{ marginBottom: '16px' }}
            />
          )}
        </div>

        {/* 输入表单 */}
        <Form
          form={form}
          onFinish={handleSubmit}
          layout="vertical"
          disabled={disabled}
        >
          {renderInputComponent()}

          <Form.Item style={{ marginBottom: 0 }}>
            <Button
              type="primary"
              htmlType="submit"
              icon={<SendOutlined />}
              loading={loading}
              disabled={disabled}
              size="large"
              style={{ width: '100%' }}
            >
              {loading ? '处理中...' : '提交'}
            </Button>
          </Form.Item>
        </Form>

        {/* 已上传文件列表 */}
        {uploadedFiles.length > 0 && (
          <div>
            <Text strong>已上传文件：</Text>
            <div style={{ marginTop: '8px' }}>
              {uploadedFiles.map((filePath, index) => (
                <div key={index} style={{ display: 'flex', alignItems: 'center', marginBottom: '4px' }}>
                  <FileOutlined style={{ marginRight: '8px', color: '#1890ff' }} />
                  <Text>{filePath.split('/').pop()}</Text>
                </div>
              ))}
            </div>
          </div>
        )}
      </Space>
    </Card>
  );
};

export default UserInputForm;
