import React, { Component, ReactNode } from 'react';
import { Result, Button, Typography, Card, Space, Collapse } from 'antd';
import { 
  ExclamationCircleOutlined, 
  ReloadOutlined, 
  HomeOutlined,
  BugOutlined 
} from '@ant-design/icons';

const { Text, Paragraph } = Typography;
const { Panel } = Collapse;

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: React.ErrorInfo | null;
}

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}

class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null
    };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return {
      hasError: true,
      error,
      errorInfo: null
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    this.setState({
      error,
      errorInfo
    });

    // 调用错误回调
    this.props.onError?.(error, errorInfo);

    // 记录错误到控制台
    console.error('ErrorBoundary caught an error:', error, errorInfo);
  }

  handleReload = () => {
    window.location.reload();
  };

  handleGoHome = () => {
    window.location.href = '/';
  };

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null
    });
  };

  render() {
    if (this.state.hasError) {
      // 如果提供了自定义fallback，使用它
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // 默认错误页面
      return (
        <div style={{ 
          minHeight: '100vh', 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center',
          padding: '20px'
        }}>
          <Card style={{ maxWidth: '600px', width: '100%' }}>
            <Result
              status="error"
              icon={<ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />}
              title="页面出现错误"
              subTitle="抱歉，页面遇到了一个意外错误。您可以尝试刷新页面或返回首页。"
              extra={
                <Space>
                  <Button 
                    type="primary" 
                    icon={<ReloadOutlined />}
                    onClick={this.handleRetry}
                  >
                    重试
                  </Button>
                  <Button 
                    icon={<ReloadOutlined />}
                    onClick={this.handleReload}
                  >
                    刷新页面
                  </Button>
                  <Button 
                    icon={<HomeOutlined />}
                    onClick={this.handleGoHome}
                  >
                    返回首页
                  </Button>
                </Space>
              }
            />

            {/* 错误详情（开发环境显示） */}
            {process.env.NODE_ENV === 'development' && this.state.error && (
              <div style={{ marginTop: '24px' }}>
                <Collapse ghost>
                  <Panel 
                    header={
                      <Space>
                        <BugOutlined />
                        <Text strong>错误详情（开发模式）</Text>
                      </Space>
                    } 
                    key="error-details"
                  >
                    <div style={{ marginBottom: '16px' }}>
                      <Text strong>错误信息：</Text>
                      <Paragraph 
                        code 
                        style={{ 
                          background: '#fff2f0', 
                          border: '1px solid #ffccc7',
                          padding: '8px',
                          marginTop: '8px'
                        }}
                      >
                        {this.state.error.message}
                      </Paragraph>
                    </div>

                    <div style={{ marginBottom: '16px' }}>
                      <Text strong>错误堆栈：</Text>
                      <pre style={{ 
                        background: '#f5f5f5', 
                        border: '1px solid #d9d9d9',
                        padding: '12px',
                        marginTop: '8px',
                        fontSize: '12px',
                        overflow: 'auto',
                        maxHeight: '200px'
                      }}>
                        {this.state.error.stack}
                      </pre>
                    </div>

                    {this.state.errorInfo && (
                      <div>
                        <Text strong>组件堆栈：</Text>
                        <pre style={{ 
                          background: '#f5f5f5', 
                          border: '1px solid #d9d9d9',
                          padding: '12px',
                          marginTop: '8px',
                          fontSize: '12px',
                          overflow: 'auto',
                          maxHeight: '200px'
                        }}>
                          {this.state.errorInfo.componentStack}
                        </pre>
                      </div>
                    )}
                  </Panel>
                </Collapse>
              </div>
            )}
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
