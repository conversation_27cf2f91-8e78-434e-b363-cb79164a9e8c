import React from 'react';
import { Spin, Typography } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';

const { Text } = Typography;

interface GlobalLoadingProps {
  loading?: boolean;
  text?: string;
  size?: 'small' | 'default' | 'large';
  overlay?: boolean;
}

const GlobalLoading: React.FC<GlobalLoadingProps> = ({
  loading = true,
  text = '加载中...',
  size = 'large',
  overlay = true
}) => {
  if (!loading) return null;

  const loadingContent = (
    <div style={{ 
      display: 'flex', 
      flexDirection: 'column', 
      alignItems: 'center', 
      justifyContent: 'center',
      padding: '40px 20px'
    }}>
      <Spin 
        size={size} 
        indicator={<LoadingOutlined style={{ fontSize: size === 'large' ? 48 : size === 'default' ? 24 : 16 }} spin />}
      />
      {text && (
        <Text 
          type="secondary" 
          style={{ 
            marginTop: '16px', 
            fontSize: size === 'large' ? '16px' : '14px' 
          }}
        >
          {text}
        </Text>
      )}
    </div>
  );

  if (overlay) {
    return (
      <div style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(255, 255, 255, 0.8)',
        zIndex: 9999,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        {loadingContent}
      </div>
    );
  }

  return loadingContent;
};

export default GlobalLoading;
