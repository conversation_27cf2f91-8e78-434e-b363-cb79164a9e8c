import React from 'react';
import {
  Card,
  Descriptions,
  Tag,
  Progress,
  Space,
  Typography,
  Statistic,
  Row,
  Col,
  Button,
  Tooltip
} from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  InfoCircleOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { SessionDetail, SessionStatus as Status } from '../../types/api';
import { FlowService } from '../../services/flowService';

const { Text, Title } = Typography;

interface SessionStatusProps {
  sessionDetail: SessionDetail;
  onPause?: () => void;
  onResume?: () => void;
  onRefresh?: () => void;
  loading?: boolean;
}

const SessionStatus: React.FC<SessionStatusProps> = ({
  sessionDetail,
  onPause,
  onResume,
  onRefresh,
  loading = false
}) => {
  // 获取状态标签配置
  const getStatusConfig = (status: Status) => {
    const configs = {
      [Status.ACTIVE]: {
        color: 'green',
        text: '进行中',
        icon: <PlayCircleOutlined />
      },
      [Status.PAUSED]: {
        color: 'orange',
        text: '已暂停',
        icon: <PauseCircleOutlined />
      },
      [Status.COMPLETED]: {
        color: 'blue',
        text: '已完成',
        icon: <CheckCircleOutlined />
      },
      [Status.EXPIRED]: {
        color: 'red',
        text: '已过期',
        icon: <ClockCircleOutlined />
      }
    };
    
    return configs[status] || {
      color: 'default',
      text: status,
      icon: <InfoCircleOutlined />
    };
  };

  const statusConfig = getStatusConfig(sessionDetail.status);

  // 计算进度
  const calculateProgress = () => {
    // 这里可以根据实际业务逻辑计算进度
    // 暂时使用历史记录数量作为进度指标
    const totalSteps = 10; // 假设总共10个步骤
    const completedSteps = sessionDetail.history.length;
    return Math.min((completedSteps / totalSteps) * 100, 100);
  };

  const progress = calculateProgress();

  // 格式化时间差
  const formatTimeDiff = (startTime: string, endTime?: string) => {
    const start = new Date(startTime);
    const end = endTime ? new Date(endTime) : new Date();
    const diffMs = end.getTime() - start.getTime();
    
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    
    if (diffHours > 0) {
      return `${diffHours}小时${diffMinutes}分钟`;
    }
    return `${diffMinutes}分钟`;
  };

  return (
    <Space direction="vertical" style={{ width: '100%' }} size="large">
      {/* 基本信息卡片 */}
      <Card 
        title={
          <Space>
            <InfoCircleOutlined />
            会话信息
          </Space>
        }
        extra={
          <Button
            icon={<ReloadOutlined />}
            onClick={onRefresh}
            loading={loading}
            type="text"
          >
            刷新
          </Button>
        }
      >
        <Descriptions column={2} size="small">
          <Descriptions.Item label="会话ID">
            <Text code>{sessionDetail.session_id}</Text>
          </Descriptions.Item>
          <Descriptions.Item label="用户ID">
            <Text>{sessionDetail.user_id || '匿名用户'}</Text>
          </Descriptions.Item>
          <Descriptions.Item label="流程名称">
            <Text strong>{FlowService.getStateDisplayName(sessionDetail.flow_name)}</Text>
          </Descriptions.Item>
          <Descriptions.Item label="当前状态">
            <Text strong>{FlowService.getStateDisplayName(sessionDetail.current_state)}</Text>
          </Descriptions.Item>
          <Descriptions.Item label="会话状态">
            <Tag color={statusConfig.color} icon={statusConfig.icon}>
              {statusConfig.text}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="持续时间">
            <Text>{formatTimeDiff(sessionDetail.created_at, sessionDetail.updated_at)}</Text>
          </Descriptions.Item>
        </Descriptions>
      </Card>

      {/* 进度和统计 */}
      <Card title="进度统计">
        <Row gutter={[24, 24]}>
          <Col xs={24} sm={12}>
            <Statistic
              title="完成进度"
              value={progress}
              precision={1}
              suffix="%"
              prefix={<CheckCircleOutlined />}
            />
            <Progress
              percent={progress}
              status={sessionDetail.status === Status.COMPLETED ? 'success' : 'active'}
              style={{ marginTop: '8px' }}
            />
          </Col>
          <Col xs={24} sm={12}>
            <Statistic
              title="历史记录"
              value={sessionDetail.history.length}
              suffix="条"
              prefix={<ClockCircleOutlined />}
            />
          </Col>
        </Row>
      </Card>

      {/* 当前状态信息 */}
      <Card title="当前状态详情">
        <Descriptions column={1} size="small">
          <Descriptions.Item label="状态名称">
            <Text strong>{FlowService.getStateDisplayName(sessionDetail.state.name)}</Text>
          </Descriptions.Item>
          <Descriptions.Item label="提示信息">
            <Text>{sessionDetail.state.prompt}</Text>
          </Descriptions.Item>
          <Descriptions.Item label="输入类型">
            <Tag color="blue">{sessionDetail.state.input_type}</Tag>
          </Descriptions.Item>
          {sessionDetail.state.options && sessionDetail.state.options.length > 0 && (
            <Descriptions.Item label="可选项">
              <Space wrap>
                {sessionDetail.state.options.map((option, index) => (
                  <Tag key={index} color="default">{option}</Tag>
                ))}
              </Space>
            </Descriptions.Item>
          )}
          {sessionDetail.state.requires && sessionDetail.state.requires.length > 0 && (
            <Descriptions.Item label="必填项">
              <Space wrap>
                {sessionDetail.state.requires.map((req, index) => (
                  <Tag key={index} color="red">{req}</Tag>
                ))}
              </Space>
            </Descriptions.Item>
          )}
        </Descriptions>
      </Card>

      {/* 操作按钮 */}
      {(sessionDetail.status === Status.ACTIVE || sessionDetail.status === Status.PAUSED) && (
        <Card title="会话操作">
          <Space>
            {sessionDetail.status === Status.ACTIVE && onPause && (
              <Button
                icon={<PauseCircleOutlined />}
                onClick={onPause}
                loading={loading}
              >
                暂停会话
              </Button>
            )}
            {sessionDetail.status === Status.PAUSED && onResume && (
              <Button
                type="primary"
                icon={<PlayCircleOutlined />}
                onClick={onResume}
                loading={loading}
              >
                恢复会话
              </Button>
            )}
          </Space>
        </Card>
      )}

      {/* 上下文信息 */}
      {Object.keys(sessionDetail.context).length > 0 && (
        <Card title="会话上下文">
          <div style={{ maxHeight: '200px', overflowY: 'auto' }}>
            <pre style={{ 
              background: '#f5f5f5', 
              padding: '12px', 
              borderRadius: '4px',
              fontSize: '12px',
              margin: 0
            }}>
              {JSON.stringify(sessionDetail.context, null, 2)}
            </pre>
          </div>
        </Card>
      )}
    </Space>
  );
};

export default SessionStatus;
