import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  Typography,
  Space,
  Button,
  Alert,
  Spin,
  Divider
} from 'antd';
import {
  RobotOutlined,
  StopOutlined,
  CheckCircleOutlined,
  LoadingOutlined
} from '@ant-design/icons';
import { SessionService } from '../../services/sessionService';
import { UserInput } from '../../types/api';
import { StreamChunk } from '../../types/responses';

const { Text, Paragraph } = Typography;

interface StreamingResponseProps {
  sessionId: string;
  input: UserInput;
  onComplete?: (success: boolean, data?: any) => void;
  onError?: (error: string) => void;
}

const StreamingResponse: React.FC<StreamingResponseProps> = ({
  sessionId,
  input,
  onComplete,
  onError
}) => {
  const [content, setContent] = useState<string>('');
  const [isStreaming, setIsStreaming] = useState(false);
  const [isCompleted, setIsCompleted] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [metadata, setMetadata] = useState<any>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  // 开始流式处理
  useEffect(() => {
    startStreaming();
    
    // 清理函数
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [sessionId, input]);

  const startStreaming = async () => {
    try {
      setIsStreaming(true);
      setError(null);
      setContent('');
      setIsCompleted(false);

      // 创建AbortController用于取消请求
      abortControllerRef.current = new AbortController();

      await SessionService.processStreamInput(
        sessionId,
        input,
        handleStreamChunk
      );
    } catch (err: any) {
      if (err.name !== 'AbortError') {
        const errorMessage = err.message || '流式处理失败';
        setError(errorMessage);
        onError?.(errorMessage);
      }
    } finally {
      setIsStreaming(false);
    }
  };

  // 处理流式数据块
  const handleStreamChunk = (chunk: StreamChunk) => {
    try {
      // 处理元数据
      if (chunk.success !== undefined || chunk.intent_type || chunk.current_state) {
        setMetadata({
          success: chunk.success,
          intent_type: chunk.intent_type,
          should_continue_flow: chunk.should_continue_flow,
          current_state: chunk.current_state,
          completed: chunk.completed
        });
      }

      // 处理内容块
      if (chunk.chunk) {
        setContent(prev => prev + chunk.chunk);
      }

      // 处理结束标记
      if (chunk.end_stream) {
        setIsStreaming(false);
        setIsCompleted(true);
        onComplete?.(metadata?.success !== false, metadata);
      }
    } catch (err) {
      console.error('处理流式数据块失败:', err);
    }
  };

  // 停止流式处理
  const stopStreaming = () => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      setIsStreaming(false);
      setIsCompleted(true);
    }
  };

  // 重试
  const retry = () => {
    startStreaming();
  };

  return (
    <Card
      title={
        <Space>
          <RobotOutlined style={{ color: '#1890ff' }} />
          <Text strong>AI 响应</Text>
          {isStreaming && <LoadingOutlined style={{ color: '#1890ff' }} />}
          {isCompleted && <CheckCircleOutlined style={{ color: '#52c41a' }} />}
        </Space>
      }
      extra={
        isStreaming ? (
          <Button
            size="small"
            icon={<StopOutlined />}
            onClick={stopStreaming}
            danger
          >
            停止
          </Button>
        ) : error ? (
          <Button
            size="small"
            onClick={retry}
          >
            重试
          </Button>
        ) : null
      }
      style={{ marginBottom: '16px' }}
    >
      {/* 错误提示 */}
      {error && (
        <Alert
          message="处理失败"
          description={error}
          type="error"
          showIcon
          style={{ marginBottom: '16px' }}
        />
      )}

      {/* 元数据信息 */}
      {metadata && (
        <div style={{ marginBottom: '16px' }}>
          <Space wrap>
            {metadata.intent_type && (
              <Text type="secondary">
                意图类型: <Text code>{metadata.intent_type}</Text>
              </Text>
            )}
            {metadata.current_state && (
              <Text type="secondary">
                当前状态: <Text code>{metadata.current_state}</Text>
              </Text>
            )}
            {metadata.should_continue_flow !== undefined && (
              <Text type="secondary">
                继续流程: <Text code>{metadata.should_continue_flow ? '是' : '否'}</Text>
              </Text>
            )}
          </Space>
          <Divider style={{ margin: '12px 0' }} />
        </div>
      )}

      {/* 流式内容 */}
      <div style={{ minHeight: '100px' }}>
        {isStreaming && !content && (
          <div style={{ textAlign: 'center', padding: '40px 0' }}>
            <Spin size="large" />
            <div style={{ marginTop: '16px' }}>
              <Text type="secondary">AI 正在思考中...</Text>
            </div>
          </div>
        )}

        {content && (
          <div style={{ 
            background: '#f5f5f5', 
            padding: '16px', 
            borderRadius: '8px',
            border: '1px solid #d9d9d9',
            minHeight: '80px'
          }}>
            <Paragraph style={{ margin: 0, whiteSpace: 'pre-wrap' }}>
              {content}
              {isStreaming && (
                <span 
                  style={{ 
                    display: 'inline-block',
                    width: '8px',
                    height: '16px',
                    backgroundColor: '#1890ff',
                    marginLeft: '2px',
                    animation: 'blink 1s infinite'
                  }}
                />
              )}
            </Paragraph>
          </div>
        )}

        {isCompleted && !content && !error && (
          <div style={{ textAlign: 'center', padding: '20px 0' }}>
            <CheckCircleOutlined style={{ fontSize: '24px', color: '#52c41a' }} />
            <div style={{ marginTop: '8px' }}>
              <Text type="secondary">处理完成</Text>
            </div>
          </div>
        )}
      </div>

      {/* 状态信息 */}
      <div style={{ marginTop: '16px', textAlign: 'right' }}>
        <Text type="secondary" style={{ fontSize: '12px' }}>
          {isStreaming && '正在接收响应...'}
          {isCompleted && !error && '响应完成'}
          {error && '响应失败'}
        </Text>
      </div>

      {/* CSS动画 */}
      <style>
        {`
          @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
          }
        `}
      </style>
    </Card>
  );
};

export default StreamingResponse;
