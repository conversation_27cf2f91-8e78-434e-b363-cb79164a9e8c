import React from 'react';
import {
  Timeline,
  Card,
  Typography,
  Tag,
  Space,
  Empty,
  Divider
} from 'antd';
import {
  CheckCircleOutlined,
  ClockCircleOutlined,
  ArrowRightOutlined,
  FileOutlined,
  MessageOutlined
} from '@ant-design/icons';
import { SessionHistoryItem, InputType } from '../../types/api';
import { FlowService } from '../../services/flowService';

const { Text, Paragraph } = Typography;

interface SessionHistoryProps {
  history: SessionHistoryItem[];
  loading?: boolean;
}

const SessionHistory: React.FC<SessionHistoryProps> = ({
  history = [],
  loading = false
}) => {
  // 格式化输入值显示
  const formatInputValue = (value: any, inputType?: string): React.ReactNode => {
    if (!value) return <Text type="secondary">无输入</Text>;

    switch (inputType) {
      case InputType.TEXT:
        return (
          <div>
            <MessageOutlined style={{ marginRight: '8px', color: '#1890ff' }} />
            <Text>{String(value)}</Text>
          </div>
        );
      
      case InputType.CHOICE:
        return (
          <div>
            <Tag color="blue">{String(value)}</Tag>
          </div>
        );
      
      case InputType.MULTICHOICE:
        const choices = Array.isArray(value) ? value : [value];
        return (
          <div>
            <Space wrap>
              {choices.map((choice, index) => (
                <Tag key={index} color="green">{String(choice)}</Tag>
              ))}
            </Space>
          </div>
        );
      
      case InputType.FILE:
        return (
          <div>
            <FileOutlined style={{ marginRight: '8px', color: '#faad14' }} />
            <Text>文件上传</Text>
            {typeof value === 'string' && value && (
              <div style={{ marginTop: '4px' }}>
                <Text type="secondary">{value}</Text>
              </div>
            )}
          </div>
        );
      
      default:
        if (typeof value === 'object') {
          return (
            <div>
              <Text code>{JSON.stringify(value, null, 2)}</Text>
            </div>
          );
        }
        return <Text>{String(value)}</Text>;
    }
  };

  // 创建时间线项目
  const createTimelineItems = () => {
    if (history.length === 0) {
      return [];
    }

    return history.map((item, index) => {
      const isLast = index === history.length - 1;
      
      return {
        key: index,
        dot: isLast ? (
          <ClockCircleOutlined style={{ fontSize: '16px', color: '#1890ff' }} />
        ) : (
          <CheckCircleOutlined style={{ fontSize: '16px', color: '#52c41a' }} />
        ),
        color: isLast ? '#1890ff' : '#52c41a',
        children: (
          <Card size="small" style={{ marginBottom: '16px' }}>
            <Space direction="vertical" style={{ width: '100%' }}>
              {/* 状态转换信息 */}
              <div>
                <Space align="center">
                  <Tag color="default">
                    {FlowService.getStateDisplayName(item.from_state)}
                  </Tag>
                  <ArrowRightOutlined style={{ color: '#666' }} />
                  <Tag color="blue">
                    {FlowService.getStateDisplayName(item.to_state)}
                  </Tag>
                </Space>
              </div>

              {/* 用户输入 */}
              <div>
                <Text strong style={{ display: 'block', marginBottom: '8px' }}>
                  用户输入：
                </Text>
                {formatInputValue(item.input_value)}
              </div>

              {/* 时间戳 */}
              <div>
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  {new Date(item.timestamp).toLocaleString()}
                </Text>
              </div>
            </Space>
          </Card>
        )
      };
    });
  };

  const timelineItems = createTimelineItems();

  if (loading) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <Text>加载历史记录中...</Text>
        </div>
      </Card>
    );
  }

  if (timelineItems.length === 0) {
    return (
      <Card>
        <Empty
          description="暂无历史记录"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      </Card>
    );
  }

  return (
    <Card title="会话历史" style={{ height: '100%' }}>
      <div style={{ maxHeight: '600px', overflowY: 'auto' }}>
        <Timeline
          items={timelineItems}
          mode="left"
        />
      </div>
      
      <Divider />
      
      <div style={{ textAlign: 'center' }}>
        <Text type="secondary" style={{ fontSize: '12px' }}>
          共 {history.length} 条记录
        </Text>
      </div>
    </Card>
  );
};

export default SessionHistory;
