import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Grid, Badge } from 'antd';
import {
  HomeOutlined,
  AppstoreOutlined,
  InfoCircleOutlined,
  SettingOutlined
} from '@ant-design/icons';

const { useBreakpoint } = Grid;

const MobileNavigation: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const screens = useBreakpoint();

  // 只在移动端显示
  if (screens.md) {
    return null;
  }

  // 获取当前路径对应的值
  const getCurrentValue = () => {
    const path = location.pathname;
    if (path === '/') return 0;
    if (path.startsWith('/sessions')) return 1;
    if (path === '/system') return 2;
    return 0;
  };

  const navigationItems = [
    {
      key: '/',
      icon: <HomeOutlined />,
      label: '首页',
      path: '/'
    },
    {
      key: '/sessions',
      icon: <AppstoreOutlined />,
      label: '会话',
      path: '/sessions'
    },
    {
      key: '/system',
      icon: <InfoCircleOutlined />,
      label: '系统',
      path: '/system'
    }
  ];

  const currentPath = location.pathname;

  return (
    <div
      style={{
        position: 'fixed',
        bottom: 0,
        left: 0,
        right: 0,
        zIndex: 1000,
        backgroundColor: '#fff',
        borderTop: '1px solid #f0f0f0',
        boxShadow: '0 -2px 8px rgba(0, 0, 0, 0.1)',
        padding: '8px 0'
      }}
    >
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-around',
          alignItems: 'center',
          height: '52px'
        }}
      >
        {navigationItems.map((item) => {
          const isActive = currentPath === item.path ||
            (item.path === '/sessions' && currentPath.startsWith('/sessions'));

          return (
            <div
              key={item.key}
              onClick={() => navigate(item.path)}
              style={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                cursor: 'pointer',
                padding: '4px 8px',
                borderRadius: '4px',
                transition: 'all 0.2s',
                color: isActive ? '#1890ff' : '#666',
                backgroundColor: isActive ? '#f0f8ff' : 'transparent'
              }}
            >
              <div style={{ fontSize: '20px', marginBottom: '2px' }}>
                {item.icon}
              </div>
              <div style={{ fontSize: '10px', fontWeight: isActive ? 600 : 400 }}>
                {item.label}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default MobileNavigation;
