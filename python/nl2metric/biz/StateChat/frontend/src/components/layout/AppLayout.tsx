import React from 'react';
import { Layout, Menu, Typography, theme, Grid } from 'antd';
import { HomeOutlined, AppstoreOutlined, SettingOutlined } from '@ant-design/icons';
import { Link, useLocation } from 'react-router-dom';
import MobileNavigation from '../ui/MobileNavigation';

const { Header, Content, Footer, Sider } = Layout;
const { Title } = Typography;
const { useBreakpoint } = Grid;

interface AppLayoutProps {
  children: React.ReactNode;
}

const AppLayout: React.FC<AppLayoutProps> = ({ children }) => {
  const location = useLocation();
  const screens = useBreakpoint();
  const {
    token: { colorBgContainer, borderRadiusLG },
  } = theme.useToken();

  // 判断是否为移动端
  const isMobile = !screens.md;

  // 确定当前选中的菜单项
  const getSelectedKey = () => {
    const pathname = location.pathname;
    if (pathname === '/') return '1';
    if (pathname.startsWith('/sessions')) return '2';
    if (pathname.startsWith('/system')) return '3';
    return '1';
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      {/* 桌面端头部 */}
      {!isMobile && (
        <Header style={{
          display: 'flex',
          alignItems: 'center',
          background: colorBgContainer,
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
          zIndex: 100
        }}>
          <div className="logo" style={{ marginRight: '24px' }}>
            <Title level={4} style={{ margin: 0, color: '#1890ff' }}>
              咖啡店营业执照申请系统
            </Title>
          </div>
          <Menu
            theme="light"
            mode="horizontal"
            selectedKeys={[getSelectedKey()]}
            items={[
              {
                key: '1',
                icon: <HomeOutlined />,
                label: <Link to="/">首页</Link>,
              },
              {
                key: '2',
                icon: <AppstoreOutlined />,
                label: <Link to="/sessions">会话管理</Link>,
              },
              {
                key: '3',
                icon: <SettingOutlined />,
                label: <Link to="/system">系统信息</Link>,
              },
            ]}
            style={{ flex: 1, minWidth: 0 }}
          />
        </Header>
      )}

      {/* 移动端头部 */}
      {isMobile && (
        <Header style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          background: colorBgContainer,
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
          zIndex: 100,
          padding: '0 16px'
        }}>
          <Title level={5} style={{ margin: 0, color: '#1890ff' }}>
            咖啡店营业执照申请系统
          </Title>
        </Header>
      )}

      <Content style={{
        padding: isMobile ? '16px' : '24px',
        paddingBottom: isMobile ? '80px' : '24px' // 为移动端底部导航留出空间
      }}>
        <div
          style={{
            padding: isMobile ? 16 : 24,
            minHeight: 360,
            background: colorBgContainer,
            borderRadius: borderRadiusLG,
          }}
        >
          {children}
        </div>
      </Content>

      {/* 桌面端底部 */}
      {!isMobile && (
        <Footer style={{ textAlign: 'center', background: colorBgContainer }}>
          咖啡店营业执照申请系统 ©{new Date().getFullYear()} 基于状态机引擎
        </Footer>
      )}

      {/* 移动端底部导航 */}
      <MobileNavigation />
    </Layout>
  );
};

export default AppLayout;