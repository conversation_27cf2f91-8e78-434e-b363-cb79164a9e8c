import React, { useState, useEffect } from 'react';
import { Layout, Drawer, But<PERSON>, Grid } from 'antd';
import { MenuOutlined } from '@ant-design/icons';

const { Header, Sider, Content } = Layout;
const { useBreakpoint } = Grid;

interface ResponsiveLayoutProps {
  children: React.ReactNode;
  siderContent?: React.ReactNode;
  headerContent?: React.ReactNode;
  title?: string;
}

const ResponsiveLayout: React.FC<ResponsiveLayoutProps> = ({
  children,
  siderContent,
  headerContent,
  title = '咖啡店营业执照申请系统'
}) => {
  const [collapsed, setCollapsed] = useState(false);
  const [mobileDrawerVisible, setMobileDrawerVisible] = useState(false);
  const screens = useBreakpoint();

  // 判断是否为移动端
  const isMobile = !screens.md;

  useEffect(() => {
    // 在移动端自动收起侧边栏
    if (isMobile) {
      setCollapsed(true);
    }
  }, [isMobile]);

  // 切换侧边栏
  const toggleSider = () => {
    if (isMobile) {
      setMobileDrawerVisible(!mobileDrawerVisible);
    } else {
      setCollapsed(!collapsed);
    }
  };

  // 关闭移动端抽屉
  const closeMobileDrawer = () => {
    setMobileDrawerVisible(false);
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      {/* 桌面端侧边栏 */}
      {!isMobile && siderContent && (
        <Sider
          collapsible
          collapsed={collapsed}
          onCollapse={setCollapsed}
          width={250}
          style={{
            overflow: 'auto',
            height: '100vh',
            position: 'fixed',
            left: 0,
            top: 0,
            bottom: 0,
            zIndex: 100
          }}
        >
          {siderContent}
        </Sider>
      )}

      {/* 移动端抽屉 */}
      {isMobile && siderContent && (
        <Drawer
          title={title}
          placement="left"
          onClose={closeMobileDrawer}
          open={mobileDrawerVisible}
          bodyStyle={{ padding: 0 }}
          width={280}
        >
          {siderContent}
        </Drawer>
      )}

      <Layout
        style={{
          marginLeft: !isMobile && siderContent ? (collapsed ? 80 : 250) : 0,
          transition: 'margin-left 0.2s'
        }}
      >
        {/* 头部 */}
        <Header
          style={{
            position: 'fixed',
            zIndex: 99,
            width: '100%',
            marginLeft: !isMobile && siderContent ? (collapsed ? -80 : -250) : 0,
            transition: 'margin-left 0.2s',
            background: '#fff',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
            padding: isMobile ? '0 16px' : '0 24px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between'
          }}
        >
          <div style={{ display: 'flex', alignItems: 'center' }}>
            {/* 移动端菜单按钮 */}
            {isMobile && siderContent && (
              <Button
                type="text"
                icon={<MenuOutlined />}
                onClick={toggleSider}
                style={{ marginRight: '16px' }}
              />
            )}
            
            {/* 标题 */}
            <h1 style={{ 
              margin: 0, 
              fontSize: isMobile ? '16px' : '20px',
              fontWeight: 600,
              color: '#1890ff'
            }}>
              {title}
            </h1>
          </div>

          {/* 头部内容 */}
          {headerContent && (
            <div style={{ flex: 1, marginLeft: '24px' }}>
              {headerContent}
            </div>
          )}
        </Header>

        {/* 主内容区 */}
        <Content
          style={{
            marginTop: 64, // Header 高度
            padding: isMobile ? '16px' : '24px',
            background: '#f0f2f5',
            minHeight: 'calc(100vh - 64px)'
          }}
        >
          <div
            style={{
              background: '#fff',
              borderRadius: '8px',
              minHeight: '100%',
              padding: isMobile ? '16px' : '24px'
            }}
          >
            {children}
          </div>
        </Content>
      </Layout>
    </Layout>
  );
};

export default ResponsiveLayout;
