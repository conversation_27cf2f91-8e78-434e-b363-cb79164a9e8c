/* 全局样式 */
body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, 'Helvetica Neue', <PERSON><PERSON>,
    'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
    'Noto Color Emoji';
}

/* 布局样式 */
.site-layout-content {
  min-height: 280px;
  padding: 24px;
  background: #fff;
}

.logo {
  float: left;
  margin-right: 24px;
}

/* 表单样式 */
.form-container {
  max-width: 800px;
  margin: 0 auto;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .form-container {
    padding: 0 16px;
  }
}

/* 文件上传样式 */
.upload-list-inline .ant-upload-list-item {
  float: left;
  width: 200px;
  margin-right: 8px;
}

.upload-list-inline [class*='-upload-list-rtl'] .ant-upload-list-item {
  float: right;
}

/* 进度条样式 */
.progress-container {
  margin: 24px 0;
}

/* 会话列表样式 */
.session-list .ant-list-item {
  cursor: pointer;
  transition: all 0.3s;
}

.session-list .ant-list-item:hover {
  background: #f0f2f5;
}

/* 状态标签样式 */
.status-tag-active {
  color: #52c41a;
}

.status-tag-paused {
  color: #faad14;
}

.status-tag-completed {
  color: #1890ff;
}

.status-tag-expired {
  color: #ff4d4f;
}

/* 意图处理样式 */
.intent-chat {
  background: #f6ffed;
  border-left: 3px solid #52c41a;
  padding: 8px 12px;
  margin-bottom: 16px;
}

.intent-query {
  background: #e6f7ff;
  border-left: 3px solid #1890ff;
  padding: 8px 12px;
  margin-bottom: 16px;
}

.intent-business {
  background: #fff7e6;
  border-left: 3px solid #faad14;
  padding: 8px 12px;
  margin-bottom: 16px;
}

/* 流式响应样式 */
.stream-response {
  min-height: 100px;
  max-height: 300px;
  overflow-y: auto;
  padding: 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  margin-bottom: 16px;
}

/* 错误消息样式 */
.error-message {
  color: #ff4d4f;
  margin-bottom: 16px;
}

/* 响应式设计 */
@media (max-width: 576px) {
  /* 小屏幕手机 */
  .ant-layout-header {
    padding: 0 16px !important;
  }

  .ant-layout-content {
    padding: 16px !important;
  }

  .ant-card {
    margin-bottom: 16px;
  }

  .ant-card-body {
    padding: 16px !important;
  }

  .ant-table-wrapper {
    overflow-x: auto;
  }

  .ant-descriptions {
    font-size: 12px;
  }

  .ant-statistic-title {
    font-size: 12px;
  }

  .ant-statistic-content {
    font-size: 18px;
  }
}

@media (max-width: 768px) {
  /* 平板和大屏手机 */
  .ant-layout-header .ant-menu {
    display: none;
  }

  .ant-layout-header .logo {
    margin-right: 0;
  }

  .ant-row {
    margin-left: 0 !important;
    margin-right: 0 !important;
  }

  .ant-col {
    padding-left: 8px !important;
    padding-right: 8px !important;
  }

  .ant-table-pagination {
    text-align: center;
  }
}

@media (max-width: 992px) {
  /* 小屏幕桌面 */
  .ant-layout-content {
    padding: 20px;
  }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 加载动画 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.loading-text {
  margin-top: 16px;
  color: #666;
}

/* 卡片悬停效果 */
.hover-card {
  transition: all 0.3s ease;
  cursor: pointer;
}

.hover-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

/* 状态指示器 */
.status-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
}

.status-indicator.active {
  background-color: #52c41a;
  box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2);
}

.status-indicator.paused {
  background-color: #faad14;
  box-shadow: 0 0 0 2px rgba(250, 173, 20, 0.2);
}

.status-indicator.completed {
  background-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.status-indicator.expired {
  background-color: #ff4d4f;
  box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

/* 文本省略 */
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-ellipsis-2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 间距工具类 */
.mt-8 { margin-top: 8px; }
.mt-16 { margin-top: 16px; }
.mt-24 { margin-top: 24px; }
.mb-8 { margin-bottom: 8px; }
.mb-16 { margin-bottom: 16px; }
.mb-24 { margin-bottom: 24px; }
.ml-8 { margin-left: 8px; }
.ml-16 { margin-left: 16px; }
.mr-8 { margin-right: 8px; }
.mr-16 { margin-right: 16px; }

/* 文本对齐 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

/* Flex 布局 */
.flex { display: flex; }
.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}
.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.flex-column {
  display: flex;
  flex-direction: column;
}

/* 高度工具类 */
.h-full { height: 100%; }
.min-h-200 { min-height: 200px; }
.min-h-300 { min-height: 300px; }
.min-h-400 { min-height: 400px; }

/* 宽度工具类 */
.w-full { width: 100%; }
.max-w-400 { max-width: 400px; }
.max-w-600 { max-width: 600px; }
.max-w-800 { max-width: 800px; }