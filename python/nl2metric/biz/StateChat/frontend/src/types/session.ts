/**
 * 会话相关类型定义
 */
import { Session, SessionDetail, SessionSummary, State, UserInput } from './api';
import { ProcessInputResponse } from './responses';

// 会话上下文类型
export interface SessionContextType {
  currentSession: Session | null;
  sessionDetail: SessionDetail | null;
  loading: boolean;
  error: string | null;
  createSession: (userId?: string, flowName?: string) => Promise<void>;
  processInput: (input: UserInput) => Promise<ProcessInputResponse>;
  pauseSession: () => Promise<void>;
  resumeSession: () => Promise<void>;
  deleteSession: () => Promise<void>;
  getSessionDetail: () => Promise<void>;
}

// 用户上下文类型
export interface UserContextType {
  userId: string | null;
  setUserId: (userId: string) => void;
  clearUser: () => void;
}

// 应用上下文类型
export interface ApplicationContextType {
  flows: FlowConfig[];
  selectedFlow: FlowConfig | null;
  loading: boolean;
  error: string | null;
  loadFlows: () => Promise<void>;
  selectFlow: (flowName: string) => Promise<void>;
}

// 流程配置类型
export interface FlowConfig {
  flow_name: string;
  description: string;
  version: string;
  initial_state: string;
  final_states: string[];
  total_states: number;
}

// 会话状态类型
export interface SessionState {
  session: Session | null;
  detail: SessionDetail | null;
  currentState: State | null;
  loading: boolean;
  error: string | null;
}

// 流程状态类型
export interface FlowState {
  flows: FlowConfig[];
  selectedFlow: FlowConfig | null;
  loading: boolean;
  error: string | null;
}