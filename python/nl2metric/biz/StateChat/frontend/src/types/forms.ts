/**
 * 表单相关类型定义
 */
import { InputType } from './api';

// 表单字段验证规则
export interface ValidationRule {
  required?: boolean;
  message?: string;
  min?: number;
  max?: number;
  pattern?: RegExp;
  validator?: (value: any) => boolean | Promise<boolean>;
}

// 表单字段配置
export interface FormFieldConfig {
  name: string;
  label?: string;
  type: InputType;
  placeholder?: string;
  options?: string[] | { label: string; value: string }[];
  rules?: ValidationRule[];
  defaultValue?: any;
  disabled?: boolean;
  hidden?: boolean;
  help?: string;
  dependencies?: string[];
}

// 动态表单配置
export interface DynamicFormConfig {
  fields: FormFieldConfig[];
  layout?: 'horizontal' | 'vertical' | 'inline';
  labelCol?: { span: number };
  wrapperCol?: { span: number };
  submitText?: string;
  cancelText?: string;
  showCancel?: boolean;
  initialValues?: Record<string, any>;
}

// 文件上传配置
export interface FileUploadConfig {
  accept?: string;
  multiple?: boolean;
  maxSize?: number; // 单位：字节
  maxCount?: number;
  listType?: 'text' | 'picture' | 'picture-card';
  showUploadList?: boolean;
  beforeUpload?: (file: File) => boolean | Promise<boolean>;
}

// 文件信息
export interface FileInfo {
  uid: string;
  name: string;
  status: 'uploading' | 'done' | 'error' | 'removed';
  url?: string;
  thumbUrl?: string;
  size: number;
  type: string;
  percent?: number;
  response?: any;
  error?: any;
}

// 表单提交事件处理器
export type FormSubmitHandler<T = any> = (values: T) => void | Promise<void>;

// 表单取消事件处理器
export type FormCancelHandler = () => void;

// 表单值变化事件处理器
export type FormValueChangeHandler = (changedValues: any, allValues: any) => void;

// 文本输入字段属性
export interface TextInputProps {
  name: string;
  label?: string;
  placeholder?: string;
  rules?: ValidationRule[];
  defaultValue?: string;
  disabled?: boolean;
  hidden?: boolean;
  help?: string;
  onChange?: (value: string) => void;
}

// 选择输入字段属性
export interface ChoiceInputProps {
  name: string;
  label?: string;
  options: string[] | { label: string; value: string }[];
  rules?: ValidationRule[];
  defaultValue?: string;
  disabled?: boolean;
  hidden?: boolean;
  help?: string;
  onChange?: (value: string) => void;
}

// 多选输入字段属性
export interface MultiChoiceInputProps {
  name: string;
  label?: string;
  options: string[] | { label: string; value: string }[];
  rules?: ValidationRule[];
  defaultValue?: string[];
  disabled?: boolean;
  hidden?: boolean;
  help?: string;
  onChange?: (value: string[]) => void;
}

// 文件上传字段属性
export interface FileInputProps {
  name: string;
  label?: string;
  accept?: string;
  multiple?: boolean;
  maxSize?: number;
  maxCount?: number;
  rules?: ValidationRule[];
  disabled?: boolean;
  hidden?: boolean;
  help?: string;
  onChange?: (fileList: FileInfo[]) => void;
  beforeUpload?: (file: File) => boolean | Promise<boolean>;
}

// 输入组件属性
export type InputComponentProps = 
  | TextInputProps 
  | ChoiceInputProps 
  | MultiChoiceInputProps 
  | FileInputProps;