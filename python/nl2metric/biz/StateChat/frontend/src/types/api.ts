/**
 * API类型定义
 */

// 会话状态枚举
export enum SessionStatus {
  ACTIVE = "active",
  PAUSED = "paused",
  COMPLETED = "completed",
  EXPIRED = "expired"
}

// 输入类型枚举
export enum InputType {
  TEXT = "text",
  CHOICE = "choice",
  MULTICHOICE = "multichoice",
  FILE = "file"
}

// 错误类型枚举
export enum ErrorType {
  API_ERROR = "api_error",
  VALIDATION_ERROR = "validation_error",
  NETWORK_ERROR = "network_error",
  AUTHENTICATION_ERROR = "authentication_error",
  UNKNOWN_ERROR = "unknown_error"
}

// 会话接口
export interface Session {
  session_id: string;
  user_id?: string;
  flow_name: string;
  current_state: string;
  status: SessionStatus;
  created_at: string;
  updated_at: string;
}

// 会话摘要接口
export interface SessionSummary {
  session_id: string;
  user_id?: string;
  flow_name: string;
  current_state: string;
  status: SessionStatus;
  progress_percentage: number;
  created_at: string;
  updated_at: string;
}

// 状态节点接口
export interface State {
  name: string;
  prompt: string;
  input_type: InputType;
  options?: string[];
  requires?: string[];
  next_states?: string[];
}

// 会话历史项接口
export interface SessionHistoryItem {
  from_state: string;
  to_state: string;
  input_value: any;
  timestamp: string;
}

// 会话详情接口
export interface SessionDetail {
  session_id: string;
  user_id?: string;
  flow_name: string;
  current_state: string;
  status: SessionStatus;
  context: Record<string, any>;
  history: SessionHistoryItem[];
  state: State;
  created_at: string;
  updated_at: string;
}

// 用户输入接口
export interface UserInput {
  input_type: InputType;
  value: string | string[] | Record<string, any>;
  files?: string[];
}

// 流程配置接口
export interface FlowConfig {
  flow_name: string;
  description: string;
  version: string;
  initial_state: string;
  final_states: string[];
  total_states: number;
}

// 应用错误接口
export interface AppError {
  type: ErrorType;
  message: string;
  details?: any;
}

// 分页参数接口
export interface PaginationParams {
  page: number;
  size: number;
}

// 分页响应接口
export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

// 文件上传响应接口
export interface FileUploadResponse {
  filename: string;
  file_path: string;
  file_size: number;
  content_type: string;
  upload_time: string;
}

// 健康检查响应接口
export interface HealthCheckResponse {
  status: string;
  timestamp: string;
  version: string;
  uptime?: number;
}

// 成功响应接口
export interface SuccessResponse {
  success: boolean;
  message: string;
  data?: Record<string, any>;
}

// 验证结果响应接口
export interface ValidationResultResponse {
  valid: boolean;
  errors: string[];
  warnings: string[];
}