/**
 * API响应类型定义
 */
import { 
  FlowConfig, 
  SessionStatus, 
  State, 
  SessionSummary, 
  SessionDetail, 
  FileUploadResponse, 
  SuccessResponse, 
  ValidationResultResponse, 
  HealthCheckResponse, 
  PaginatedResponse 
} from './api';

// 创建会话响应
export interface CreateSessionResponse {
  session_id: string;
  flow_name: string;
  current_state: string;
  prompt: string;
  input_type: string;
  options?: string[];
  requires?: string[];
}

// 处理输入响应
export interface ProcessInputResponse {
  success: boolean;
  completed: boolean;
  error?: string;
  intent_type?: string;
  response?: string;
  current_state: string;
  previous_state?: string;
  prompt?: string;
  input_type?: string;
  options?: string[];
  requires?: string[];
  progress?: number;
  message?: string;
  should_continue_flow?: boolean;
}

// 流式响应块
export interface StreamChunk {
  chunk?: string;
  end_stream?: boolean;
  success?: boolean;
  completed?: boolean;
  intent_type?: string;
  should_continue_flow?: boolean;
  current_state?: string;
}

// 意图处理响应
export interface IntentProcessResponse {
  intent_type: string;
  success: boolean;
  response?: string;
  should_continue_flow: boolean;
  data?: Record<string, any>;
}

// 状态信息响应
export interface StateInfoResponse {
  name: string;
  prompt: string;
  input_type: string;
  options?: string[];
  requires?: string[];
  next_states?: string[];
}

// 错误响应
export interface ErrorResponse {
  error: string;
  message: string;
  details?: Record<string, any>;
}

// 类型导出
export type {
  FlowConfig,
  SessionStatus,
  State,
  SessionSummary,
  SessionDetail,
  FileUploadResponse,
  SuccessResponse,
  ValidationResultResponse,
  HealthCheckResponse,
  PaginatedResponse
};