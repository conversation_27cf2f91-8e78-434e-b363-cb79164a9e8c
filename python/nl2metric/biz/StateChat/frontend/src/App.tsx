import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import AppLayout from './components/layout/AppLayout';
import HomePage from './features/dashboard/HomePage';
import SessionsPage from './features/session/SessionsPage';
import SessionDetailPage from './features/session/SessionDetailPage';
import SystemInfoPage from './features/system/SystemInfoPage';
import ErrorBoundary from './components/ui/ErrorBoundary';
import { AppProvider } from './contexts/AppContext';
import { SessionProvider } from './contexts/SessionContext';
import './App.css';

// 临时页面组件，后续会替换为实际组件
const NotFoundPage = () => (
  <div style={{ textAlign: 'center', padding: '100px 0' }}>
    <h2>404 - 页面不存在</h2>
    <p>您访问的页面不存在，请检查URL是否正确。</p>
  </div>
);

const App: React.FC = () => {
  return (
    <ErrorBoundary>
      <ConfigProvider locale={zhCN}>
        <AppProvider>
          <SessionProvider>
            <Router>
              <AppLayout>
                <Routes>
                  <Route path="/" element={<HomePage />} />
                  <Route path="/sessions" element={<SessionsPage />} />
                  <Route path="/sessions/:sessionId" element={<SessionDetailPage />} />
                  <Route path="/system" element={<SystemInfoPage />} />
                  <Route path="*" element={<NotFoundPage />} />
                </Routes>
              </AppLayout>
            </Router>
          </SessionProvider>
        </AppProvider>
      </ConfigProvider>
    </ErrorBoundary>
  );
};

export default App;