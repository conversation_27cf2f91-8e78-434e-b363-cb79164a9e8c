import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Typography,
  Input,
  Row,
  Col,
  Dropdown,
  Modal,
  message,
  Tooltip,
  Progress,
  Empty
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  MoreOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  DeleteOutlined,
  EyeOutlined,
  ReloadOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import type { ColumnsType } from 'antd/es/table';
import type { MenuProps } from 'antd';
import { SessionService } from '../../services/sessionService';
import { FlowService } from '../../services/flowService';
import { SessionSummary, SessionStatus } from '../../types/api';
import { PaginatedResponse } from '../../types/responses';

const { Title } = Typography;
const { Search } = Input;
const { confirm } = Modal;

const SessionsPage: React.FC = () => {
  const navigate = useNavigate();
  const [sessions, setSessions] = useState<SessionSummary[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });

  // 加载会话列表
  const loadSessions = async (page: number = 1, size: number = 10) => {
    try {
      setLoading(true);
      const response: PaginatedResponse<SessionSummary> = await SessionService.listSessions(page, size);
      setSessions(response.items);
      setPagination({
        current: page,
        pageSize: size,
        total: response.total
      });
    } catch (error: any) {
      message.error(error.message || '加载会话列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadSessions();
  }, []);

  // 获取状态标签
  const getStatusTag = (status: SessionStatus) => {
    const statusConfig = {
      [SessionStatus.ACTIVE]: { color: 'green', text: '进行中' },
      [SessionStatus.PAUSED]: { color: 'orange', text: '已暂停' },
      [SessionStatus.COMPLETED]: { color: 'blue', text: '已完成' },
      [SessionStatus.EXPIRED]: { color: 'red', text: '已过期' }
    };
    
    const config = statusConfig[status] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 处理会话操作
  const handleSessionAction = async (action: string, sessionId: string) => {
    try {
      switch (action) {
        case 'view':
          navigate(`/sessions/${sessionId}`);
          break;
        case 'pause':
          await SessionService.pauseSession(sessionId);
          message.success('会话已暂停');
          loadSessions(pagination.current, pagination.pageSize);
          break;
        case 'resume':
          await SessionService.resumeSession(sessionId);
          message.success('会话已恢复');
          loadSessions(pagination.current, pagination.pageSize);
          break;
        case 'delete':
          confirm({
            title: '确认删除',
            icon: <ExclamationCircleOutlined />,
            content: '确定要删除这个会话吗？此操作不可恢复。',
            okText: '删除',
            okType: 'danger',
            cancelText: '取消',
            onOk: async () => {
              await SessionService.deleteSession(sessionId);
              message.success('会话已删除');
              loadSessions(pagination.current, pagination.pageSize);
            }
          });
          break;
      }
    } catch (error: any) {
      message.error(error.message || '操作失败');
    }
  };

  // 创建新会话
  const handleCreateSession = () => {
    navigate('/');
  };

  // 获取操作菜单
  const getActionMenu = (session: SessionSummary): MenuProps => ({
    items: [
      {
        key: 'view',
        icon: <EyeOutlined />,
        label: '查看详情',
        onClick: () => handleSessionAction('view', session.session_id)
      },
      ...(session.status === SessionStatus.ACTIVE ? [{
        key: 'pause',
        icon: <PauseCircleOutlined />,
        label: '暂停会话',
        onClick: () => handleSessionAction('pause', session.session_id)
      }] : []),
      ...(session.status === SessionStatus.PAUSED ? [{
        key: 'resume',
        icon: <PlayCircleOutlined />,
        label: '恢复会话',
        onClick: () => handleSessionAction('resume', session.session_id)
      }] : []),
      {
        type: 'divider'
      },
      {
        key: 'delete',
        icon: <DeleteOutlined />,
        label: '删除会话',
        danger: true,
        onClick: () => handleSessionAction('delete', session.session_id)
      }
    ]
  });

  // 表格列定义
  const columns: ColumnsType<SessionSummary> = [
    {
      title: '会话ID',
      dataIndex: 'session_id',
      key: 'session_id',
      width: 120,
      render: (text: string) => (
        <Tooltip title={text}>
          <Button 
            type="link" 
            onClick={() => navigate(`/sessions/${text}`)}
            style={{ padding: 0 }}
          >
            {text.substring(0, 8)}...
          </Button>
        </Tooltip>
      )
    },
    {
      title: '用户ID',
      dataIndex: 'user_id',
      key: 'user_id',
      width: 120,
      render: (text: string) => text || '匿名用户'
    },
    {
      title: '流程名称',
      dataIndex: 'flow_name',
      key: 'flow_name',
      width: 150,
      render: (text: string) => FlowService.getStateDisplayName(text)
    },
    {
      title: '当前状态',
      dataIndex: 'current_state',
      key: 'current_state',
      width: 150,
      render: (text: string) => FlowService.getStateDisplayName(text)
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: SessionStatus) => getStatusTag(status)
    },
    {
      title: '进度',
      dataIndex: 'progress_percentage',
      key: 'progress_percentage',
      width: 120,
      render: (progress: number) => (
        <Progress 
          percent={Math.round(progress)} 
          size="small" 
          status={progress === 100 ? 'success' : 'active'}
        />
      )
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180,
      render: (text: string) => new Date(text).toLocaleString()
    },
    {
      title: '更新时间',
      dataIndex: 'updated_at',
      key: 'updated_at',
      width: 180,
      render: (text: string) => new Date(text).toLocaleString()
    },
    {
      title: '操作',
      key: 'actions',
      width: 80,
      fixed: 'right',
      render: (_, session) => (
        <Dropdown menu={getActionMenu(session)} trigger={['click']}>
          <Button type="text" icon={<MoreOutlined />} />
        </Dropdown>
      )
    }
  ];

  // 过滤会话数据
  const filteredSessions = sessions.filter(session =>
    session.session_id.toLowerCase().includes(searchText.toLowerCase()) ||
    session.flow_name.toLowerCase().includes(searchText.toLowerCase()) ||
    session.current_state.toLowerCase().includes(searchText.toLowerCase()) ||
    (session.user_id && session.user_id.toLowerCase().includes(searchText.toLowerCase()))
  );

  return (
    <div>
      <Card>
        <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
          <Col>
            <Title level={3} style={{ margin: 0 }}>会话管理</Title>
          </Col>
          <Col>
            <Space>
              <Search
                placeholder="搜索会话..."
                allowClear
                style={{ width: 300 }}
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                prefix={<SearchOutlined />}
              />
              <Button
                icon={<ReloadOutlined />}
                onClick={() => loadSessions(pagination.current, pagination.pageSize)}
                loading={loading}
              >
                刷新
              </Button>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleCreateSession}
              >
                新建会话
              </Button>
            </Space>
          </Col>
        </Row>

        <Table
          columns={columns}
          dataSource={filteredSessions}
          rowKey="session_id"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: (page, pageSize) => {
              loadSessions(page, pageSize);
            }
          }}
          scroll={{ x: 1200 }}
          locale={{
            emptyText: (
              <Empty
                description="暂无会话数据"
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              >
                <Button type="primary" onClick={handleCreateSession}>
                  创建第一个会话
                </Button>
              </Empty>
            )
          }}
        />
      </Card>
    </div>
  );
};

export default SessionsPage;
