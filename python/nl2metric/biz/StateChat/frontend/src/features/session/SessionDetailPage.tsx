import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Col,
  Card,
  Typo<PERSON>,
  Space,
  Button,
  message,
  Spin,
  Alert,
  Breadcrumb
} from 'antd';
import {
  ArrowLeftOutlined,
  ReloadOutlined,
  HomeOutlined,
  AppstoreOutlined
} from '@ant-design/icons';
import { useParams, useNavigate } from 'react-router-dom';
import { SessionService } from '../../services/sessionService';
import { SessionDetail, UserInput, SessionStatus } from '../../types/api';
import { ProcessInputResponse } from '../../types/responses';
import SessionStatusComponent from '../../components/ui/SessionStatus';
import SessionHistory from '../../components/ui/SessionHistory';
import UserInputForm from '../../components/forms/UserInputForm';

const { Title } = Typography;

const SessionDetailPage: React.FC = () => {
  const { sessionId } = useParams<{ sessionId: string }>();
  const navigate = useNavigate();
  
  const [sessionDetail, setSessionDetail] = useState<SessionDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 加载会话详情
  const loadSessionDetail = async () => {
    if (!sessionId) {
      setError('会话ID不存在');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const detail = await SessionService.getSessionDetail(sessionId);
      setSessionDetail(detail);
    } catch (err: any) {
      setError(err.message || '加载会话详情失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadSessionDetail();
  }, [sessionId]);

  // 处理用户输入
  const handleUserInput = async (input: UserInput) => {
    if (!sessionId) return;

    try {
      setSubmitting(true);
      setError(null);

      const response: ProcessInputResponse = await SessionService.processInput(sessionId, input);

      if (response.success) {
        if (response.completed) {
          message.success(response.message || '流程已完成！');
        } else {
          message.success('输入处理成功');
        }
        
        // 重新加载会话详情
        await loadSessionDetail();
      } else {
        setError(response.error || '处理输入失败');
      }
    } catch (err: any) {
      setError(err.message || '处理输入失败');
    } finally {
      setSubmitting(false);
    }
  };

  // 暂停会话
  const handlePauseSession = async () => {
    if (!sessionId) return;

    try {
      setSubmitting(true);
      await SessionService.pauseSession(sessionId);
      message.success('会话已暂停');
      await loadSessionDetail();
    } catch (err: any) {
      message.error(err.message || '暂停会话失败');
    } finally {
      setSubmitting(false);
    }
  };

  // 恢复会话
  const handleResumeSession = async () => {
    if (!sessionId) return;

    try {
      setSubmitting(true);
      await SessionService.resumeSession(sessionId);
      message.success('会话已恢复');
      await loadSessionDetail();
    } catch (err: any) {
      message.error(err.message || '恢复会话失败');
    } finally {
      setSubmitting(false);
    }
  };

  // 返回会话列表
  const handleGoBack = () => {
    navigate('/sessions');
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '100px 0' }}>
        <Spin size="large" />
        <div style={{ marginTop: '16px' }}>
          <Typography.Text>加载会话详情中...</Typography.Text>
        </div>
      </div>
    );
  }

  if (error && !sessionDetail) {
    return (
      <div style={{ padding: '24px' }}>
        <Alert
          message="加载失败"
          description={error}
          type="error"
          showIcon
          action={
            <Space>
              <Button size="small" onClick={loadSessionDetail}>
                重试
              </Button>
              <Button size="small" onClick={handleGoBack}>
                返回
              </Button>
            </Space>
          }
        />
      </div>
    );
  }

  if (!sessionDetail) {
    return (
      <div style={{ textAlign: 'center', padding: '100px 0' }}>
        <Typography.Text>会话不存在</Typography.Text>
        <div style={{ marginTop: '16px' }}>
          <Button onClick={handleGoBack}>返回会话列表</Button>
        </div>
      </div>
    );
  }

  const canInteract = sessionDetail.status === SessionStatus.ACTIVE;

  return (
    <div>
      {/* 面包屑导航 */}
      <Card style={{ marginBottom: '16px' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>
          <Breadcrumb
            items={[
              {
                href: '/',
                title: <HomeOutlined />
              },
              {
                href: '/sessions',
                title: (
                  <Space>
                    <AppstoreOutlined />
                    会话管理
                  </Space>
                )
              },
              {
                title: `会话详情 (${sessionId?.substring(0, 8)}...)`
              }
            ]}
          />
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={loadSessionDetail}
              loading={loading}
            >
              刷新
            </Button>
            <Button
              icon={<ArrowLeftOutlined />}
              onClick={handleGoBack}
            >
              返回
            </Button>
          </Space>
        </div>
      </Card>

      {/* 错误提示 */}
      {error && (
        <Alert
          message={error}
          type="error"
          showIcon
          closable
          onClose={() => setError(null)}
          style={{ marginBottom: '16px' }}
        />
      )}

      <Row gutter={[24, 24]}>
        {/* 左侧：会话状态和输入表单 */}
        <Col xs={24} lg={14}>
          <Space direction="vertical" style={{ width: '100%' }} size="large">
            {/* 会话状态 */}
            <SessionStatusComponent
              sessionDetail={sessionDetail}
              onPause={handlePauseSession}
              onResume={handleResumeSession}
              onRefresh={loadSessionDetail}
              loading={submitting}
            />

            {/* 用户输入表单 */}
            {canInteract && (
              <UserInputForm
                sessionId={sessionId!}
                prompt={sessionDetail.state.prompt}
                inputType={sessionDetail.state.input_type}
                options={sessionDetail.state.options}
                requires={sessionDetail.state.requires}
                onSubmit={handleUserInput}
                loading={submitting}
                disabled={!canInteract}
              />
            )}

            {/* 非活跃状态提示 */}
            {!canInteract && (
              <Card>
                <Alert
                  message="会话不可交互"
                  description={
                    sessionDetail.status === SessionStatus.PAUSED
                      ? '会话已暂停，请恢复后继续操作'
                      : sessionDetail.status === SessionStatus.COMPLETED
                      ? '会话已完成'
                      : '会话已过期'
                  }
                  type="info"
                  showIcon
                />
              </Card>
            )}
          </Space>
        </Col>

        {/* 右侧：会话历史 */}
        <Col xs={24} lg={10}>
          <SessionHistory
            history={sessionDetail.history}
            loading={loading}
          />
        </Col>
      </Row>
    </div>
  );
};

export default SessionDetailPage;
