import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Button, 
  Typography, 
  Row, 
  Col, 
  Space, 
  Spin, 
  Alert, 
  Select, 
  Divider,
  Statistic,
  Tag
} from 'antd';
import { 
  PlayCircleOutlined, 
  AppstoreOutlined, 
  InfoCircleOutlined,
  RocketOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { FlowService } from '../../services/flowService';
import { SessionService } from '../../services/sessionService';
import { FlowConfig } from '../../types/api';

const { Title, Paragraph, Text } = Typography;
const { Option } = Select;

const HomePage: React.FC = () => {
  const navigate = useNavigate();
  const [flows, setFlows] = useState<FlowConfig[]>([]);
  const [selectedFlow, setSelectedFlow] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [flowsLoading, setFlowsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 加载可用流程
  useEffect(() => {
    const loadFlows = async () => {
      try {
        setFlowsLoading(true);
        const flowList = await FlowService.listFlows();
        setFlows(flowList);
        if (flowList.length > 0) {
          setSelectedFlow(flowList[0].flow_name);
        }
      } catch (err: any) {
        setError(err.message || '加载流程列表失败');
      } finally {
        setFlowsLoading(false);
      }
    };

    loadFlows();
  }, []);

  // 开始新会话
  const handleStartSession = async () => {
    if (!selectedFlow) {
      setError('请选择一个流程');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      const response = await SessionService.createSession({
        flow_name: selectedFlow
      });
      
      // 跳转到会话详情页面
      navigate(`/sessions/${response.session_id}`);
    } catch (err: any) {
      setError(err.message || '创建会话失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取选中流程的详情
  const getSelectedFlowConfig = (): FlowConfig | null => {
    return flows.find(flow => flow.flow_name === selectedFlow) || null;
  };

  const selectedFlowConfig = getSelectedFlowConfig();

  return (
    <div style={{ padding: '24px 0' }}>
      {/* 欢迎区域 */}
      <Row gutter={[24, 24]}>
        <Col span={24}>
          <Card>
            <div style={{ textAlign: 'center', padding: '40px 0' }}>
              <RocketOutlined style={{ fontSize: '64px', color: '#1890ff', marginBottom: '24px' }} />
              <Title level={1} style={{ marginBottom: '16px' }}>
                咖啡店营业执照申请系统
              </Title>
              <Paragraph style={{ fontSize: '18px', color: '#666', maxWidth: '600px', margin: '0 auto 32px' }}>
                基于智能状态机引擎的营业执照申请流程管理系统，为您提供便捷、高效的申请体验。
                系统将引导您完成整个申请流程，确保所有必要信息都得到正确收集和处理。
              </Paragraph>
              
              {error && (
                <Alert
                  message={error}
                  type="error"
                  showIcon
                  closable
                  onClose={() => setError(null)}
                  style={{ marginBottom: '24px', maxWidth: '500px', margin: '0 auto 24px' }}
                />
              )}

              <Space direction="vertical" size="large" style={{ width: '100%', maxWidth: '400px' }}>
                <div>
                  <Text strong style={{ display: 'block', marginBottom: '8px' }}>
                    选择申请流程：
                  </Text>
                  <Select
                    value={selectedFlow}
                    onChange={setSelectedFlow}
                    style={{ width: '100%' }}
                    size="large"
                    loading={flowsLoading}
                    placeholder="请选择申请流程"
                  >
                    {flows.map(flow => (
                      <Option key={flow.flow_name} value={flow.flow_name}>
                        <Space>
                          <AppstoreOutlined />
                          {flow.description}
                        </Space>
                      </Option>
                    ))}
                  </Select>
                </div>

                <Button
                  type="primary"
                  size="large"
                  icon={<PlayCircleOutlined />}
                  onClick={handleStartSession}
                  loading={loading}
                  disabled={!selectedFlow || flowsLoading}
                  style={{ width: '100%', height: '48px', fontSize: '16px' }}
                >
                  开始申请
                </Button>
              </Space>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 流程信息 */}
      {selectedFlowConfig && (
        <Row gutter={[24, 24]} style={{ marginTop: '24px' }}>
          <Col span={24}>
            <Card title={
              <Space>
                <InfoCircleOutlined />
                流程信息
              </Space>
            }>
              <Row gutter={[24, 24]}>
                <Col xs={24} sm={12} md={8}>
                  <Statistic
                    title="流程名称"
                    value={selectedFlowConfig.description}
                    prefix={<AppstoreOutlined />}
                  />
                </Col>
                <Col xs={24} sm={12} md={8}>
                  <Statistic
                    title="版本"
                    value={selectedFlowConfig.version}
                    prefix={<Tag color="blue">V</Tag>}
                  />
                </Col>
                <Col xs={24} sm={12} md={8}>
                  <Statistic
                    title="总步骤数"
                    value={selectedFlowConfig.total_states}
                    prefix={<CheckCircleOutlined />}
                  />
                </Col>
              </Row>
              
              <Divider />
              
              <Row gutter={[16, 16]}>
                <Col xs={24} md={12}>
                  <Card size="small" title="起始状态">
                    <Space>
                      <ClockCircleOutlined style={{ color: '#52c41a' }} />
                      <Text>{FlowService.getStateDisplayName(selectedFlowConfig.initial_state)}</Text>
                    </Space>
                  </Card>
                </Col>
                <Col xs={24} md={12}>
                  <Card size="small" title="结束状态">
                    <Space direction="vertical" size="small">
                      {selectedFlowConfig.final_states.map(state => (
                        <Space key={state}>
                          <CheckCircleOutlined style={{ color: '#1890ff' }} />
                          <Text>{FlowService.getStateDisplayName(state)}</Text>
                        </Space>
                      ))}
                    </Space>
                  </Card>
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>
      )}

      {/* 功能介绍 */}
      <Row gutter={[24, 24]} style={{ marginTop: '24px' }}>
        <Col xs={24} md={8}>
          <Card>
            <div style={{ textAlign: 'center', padding: '20px 0' }}>
              <AppstoreOutlined style={{ fontSize: '48px', color: '#52c41a', marginBottom: '16px' }} />
              <Title level={4}>智能流程引导</Title>
              <Paragraph>
                系统会根据您的输入智能判断下一步操作，确保申请流程的顺利进行。
              </Paragraph>
            </div>
          </Card>
        </Col>
        <Col xs={24} md={8}>
          <Card>
            <div style={{ textAlign: 'center', padding: '20px 0' }}>
              <CheckCircleOutlined style={{ fontSize: '48px', color: '#1890ff', marginBottom: '16px' }} />
              <Title level={4}>状态跟踪</Title>
              <Paragraph>
                实时跟踪申请进度，随时了解当前状态和已完成的步骤。
              </Paragraph>
            </div>
          </Card>
        </Col>
        <Col xs={24} md={8}>
          <Card>
            <div style={{ textAlign: 'center', padding: '20px 0' }}>
              <ClockCircleOutlined style={{ fontSize: '48px', color: '#faad14', marginBottom: '16px' }} />
              <Title level={4}>会话管理</Title>
              <Paragraph>
                支持暂停和恢复申请流程，您可以随时中断并在方便时继续。
              </Paragraph>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default HomePage;
