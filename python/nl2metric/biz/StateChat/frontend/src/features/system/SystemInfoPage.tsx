import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Typography,
  Space,
  Button,
  Table,
  Tag,
  Alert,
  Descriptions,
  Progress,
  Spin,
  message
} from 'antd';
import {
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ReloadOutlined,
  SettingOutlined,
  AppstoreOutlined,
  ClockCircleOutlined,
  DatabaseOutlined,
  ApiOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { FlowService } from '../../services/flowService';
import { SessionService } from '../../services/sessionService';
import { FlowConfig, SessionSummary } from '../../types/api';
import { HealthCheckResponse } from '../../types/responses';

const { Title, Text } = Typography;

interface SystemStats {
  totalSessions: number;
  activeSessions: number;
  completedSessions: number;
  pausedSessions: number;
}

const SystemInfoPage: React.FC = () => {
  const [healthStatus, setHealthStatus] = useState<HealthCheckResponse | null>(null);
  const [flows, setFlows] = useState<FlowConfig[]>([]);
  const [systemStats, setSystemStats] = useState<SystemStats>({
    totalSessions: 0,
    activeSessions: 0,
    completedSessions: 0,
    pausedSessions: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 加载系统信息
  const loadSystemInfo = async () => {
    try {
      setLoading(true);
      setError(null);

      // 并行加载各种信息
      const [healthResponse, flowsResponse, sessionsResponse] = await Promise.all([
        FlowService.healthCheck(),
        FlowService.listFlows(),
        SessionService.listSessions(1, 1000) // 获取所有会话用于统计
      ]);

      setHealthStatus(healthResponse);
      setFlows(flowsResponse);

      // 计算会话统计
      const sessions = sessionsResponse.items;
      const stats: SystemStats = {
        totalSessions: sessions.length,
        activeSessions: sessions.filter(s => s.status === 'active').length,
        completedSessions: sessions.filter(s => s.status === 'completed').length,
        pausedSessions: sessions.filter(s => s.status === 'paused').length
      };
      setSystemStats(stats);

    } catch (err: any) {
      setError(err.message || '加载系统信息失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadSystemInfo();
  }, []);

  // 格式化运行时间
  const formatUptime = (seconds: number): string => {
    const days = Math.floor(seconds / (24 * 3600));
    const hours = Math.floor((seconds % (24 * 3600)) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);

    if (days > 0) {
      return `${days}天 ${hours}小时 ${minutes}分钟`;
    } else if (hours > 0) {
      return `${hours}小时 ${minutes}分钟`;
    } else {
      return `${minutes}分钟`;
    }
  };

  // 流程表格列定义
  const flowColumns: ColumnsType<FlowConfig> = [
    {
      title: '流程名称',
      dataIndex: 'flow_name',
      key: 'flow_name',
      render: (text: string) => <Text strong>{text}</Text>
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description'
    },
    {
      title: '版本',
      dataIndex: 'version',
      key: 'version',
      render: (text: string) => <Tag color="blue">v{text}</Tag>
    },
    {
      title: '初始状态',
      dataIndex: 'initial_state',
      key: 'initial_state',
      render: (text: string) => FlowService.getStateDisplayName(text)
    },
    {
      title: '总状态数',
      dataIndex: 'total_states',
      key: 'total_states',
      align: 'center'
    },
    {
      title: '结束状态',
      dataIndex: 'final_states',
      key: 'final_states',
      render: (states: string[]) => (
        <Space wrap>
          {states.map(state => (
            <Tag key={state} color="green">
              {FlowService.getStateDisplayName(state)}
            </Tag>
          ))}
        </Space>
      )
    }
  ];

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '100px 0' }}>
        <Spin size="large" />
        <div style={{ marginTop: '16px' }}>
          <Text>加载系统信息中...</Text>
        </div>
      </div>
    );
  }

  const isHealthy = healthStatus?.status === 'healthy';

  return (
    <div>
      <Space direction="vertical" style={{ width: '100%' }} size="large">
        {/* 页面标题 */}
        <Card>
          <Row justify="space-between" align="middle">
            <Col>
              <Title level={3} style={{ margin: 0 }}>系统信息</Title>
            </Col>
            <Col>
              <Button
                icon={<ReloadOutlined />}
                onClick={loadSystemInfo}
                loading={loading}
              >
                刷新
              </Button>
            </Col>
          </Row>
        </Card>

        {/* 错误提示 */}
        {error && (
          <Alert
            message={error}
            type="error"
            showIcon
            closable
            onClose={() => setError(null)}
          />
        )}

        {/* 系统健康状态 */}
        <Card title={
          <Space>
            <SettingOutlined />
            系统健康状态
          </Space>
        }>
          <Row gutter={[24, 24]}>
            <Col xs={24} sm={12} md={6}>
              <Statistic
                title="系统状态"
                value={healthStatus?.status || '未知'}
                prefix={
                  isHealthy ? (
                    <CheckCircleOutlined style={{ color: '#52c41a' }} />
                  ) : (
                    <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />
                  )
                }
                valueStyle={{ color: isHealthy ? '#52c41a' : '#ff4d4f' }}
              />
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Statistic
                title="系统版本"
                value={healthStatus?.version || '未知'}
                prefix={<ApiOutlined />}
              />
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Statistic
                title="运行时间"
                value={healthStatus?.uptime ? formatUptime(healthStatus.uptime) : '未知'}
                prefix={<ClockCircleOutlined />}
              />
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Statistic
                title="最后检查"
                value={healthStatus?.timestamp ? new Date(healthStatus.timestamp).toLocaleString() : '未知'}
                prefix={<DatabaseOutlined />}
              />
            </Col>
          </Row>
        </Card>

        {/* 会话统计 */}
        <Card title={
          <Space>
            <DatabaseOutlined />
            会话统计
          </Space>
        }>
          <Row gutter={[24, 24]}>
            <Col xs={24} sm={12} md={6}>
              <Statistic
                title="总会话数"
                value={systemStats.totalSessions}
                prefix={<AppstoreOutlined />}
              />
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Statistic
                title="活跃会话"
                value={systemStats.activeSessions}
                prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Statistic
                title="已完成"
                value={systemStats.completedSessions}
                prefix={<CheckCircleOutlined style={{ color: '#1890ff' }} />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Statistic
                title="已暂停"
                value={systemStats.pausedSessions}
                prefix={<ExclamationCircleOutlined style={{ color: '#faad14' }} />}
                valueStyle={{ color: '#faad14' }}
              />
            </Col>
          </Row>

          {/* 会话分布图 */}
          {systemStats.totalSessions > 0 && (
            <div style={{ marginTop: '24px' }}>
              <Text strong>会话状态分布：</Text>
              <div style={{ marginTop: '8px' }}>
                <Progress
                  percent={(systemStats.activeSessions / systemStats.totalSessions) * 100}
                  success={{ percent: (systemStats.completedSessions / systemStats.totalSessions) * 100 }}
                  format={() => ''}
                />
                <div style={{ marginTop: '8px', fontSize: '12px' }}>
                  <Space>
                    <span>
                      <span style={{ color: '#52c41a' }}>●</span> 活跃 ({systemStats.activeSessions})
                    </span>
                    <span>
                      <span style={{ color: '#1890ff' }}>●</span> 完成 ({systemStats.completedSessions})
                    </span>
                    <span>
                      <span style={{ color: '#faad14' }}>●</span> 暂停 ({systemStats.pausedSessions})
                    </span>
                  </Space>
                </div>
              </div>
            </div>
          )}
        </Card>

        {/* 流程配置 */}
        <Card title={
          <Space>
            <AppstoreOutlined />
            流程配置
          </Space>
        }>
          <Table
            columns={flowColumns}
            dataSource={flows}
            rowKey="flow_name"
            pagination={false}
            size="small"
          />
        </Card>
      </Space>
    </div>
  );
};

export default SystemInfoPage;
