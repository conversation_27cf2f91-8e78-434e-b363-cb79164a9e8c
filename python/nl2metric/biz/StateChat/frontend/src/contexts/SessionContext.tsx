import React, { createContext, useContext, useReducer, use<PERSON><PERSON>back, ReactNode } from 'react';
import { Session, SessionDetail, UserInput, SessionStatus } from '../types/api';
import { ProcessInputResponse, CreateSessionResponse } from '../types/responses';
import { SessionService } from '../services/sessionService';
import { message } from 'antd';

// 会话状态类型
interface SessionState {
  currentSession: Session | null;
  sessionDetail: SessionDetail | null;
  loading: boolean;
  error: string | null;
  submitting: boolean;
}

// 动作类型
type SessionAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_SUBMITTING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_CURRENT_SESSION'; payload: Session | null }
  | { type: 'SET_SESSION_DETAIL'; payload: SessionDetail | null }
  | { type: 'UPDATE_SESSION_STATUS'; payload: SessionStatus }
  | { type: 'RESET_SESSION' };

// 上下文类型
interface SessionContextType {
  state: SessionState;
  createSession: (userId?: string, flowName?: string) => Promise<CreateSessionResponse | null>;
  loadSessionDetail: (sessionId: string) => Promise<void>;
  processInput: (sessionId: string, input: UserInput) => Promise<ProcessInputResponse | null>;
  pauseSession: (sessionId: string) => Promise<void>;
  resumeSession: (sessionId: string) => Promise<void>;
  deleteSession: (sessionId: string) => Promise<void>;
  clearSession: () => void;
  setError: (error: string | null) => void;
}

// 初始状态
const initialState: SessionState = {
  currentSession: null,
  sessionDetail: null,
  loading: false,
  error: null,
  submitting: false
};

// Reducer
const sessionReducer = (state: SessionState, action: SessionAction): SessionState => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    case 'SET_SUBMITTING':
      return { ...state, submitting: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    case 'SET_CURRENT_SESSION':
      return { ...state, currentSession: action.payload };
    case 'SET_SESSION_DETAIL':
      return { ...state, sessionDetail: action.payload };
    case 'UPDATE_SESSION_STATUS':
      return {
        ...state,
        currentSession: state.currentSession ? {
          ...state.currentSession,
          status: action.payload
        } : null,
        sessionDetail: state.sessionDetail ? {
          ...state.sessionDetail,
          status: action.payload
        } : null
      };
    case 'RESET_SESSION':
      return initialState;
    default:
      return state;
  }
};

// 创建上下文
const SessionContext = createContext<SessionContextType | undefined>(undefined);

// Provider组件
interface SessionProviderProps {
  children: ReactNode;
}

export const SessionProvider: React.FC<SessionProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(sessionReducer, initialState);

  // 设置错误
  const setError = useCallback((error: string | null) => {
    dispatch({ type: 'SET_ERROR', payload: error });
  }, []);

  // 创建会话
  const createSession = useCallback(async (userId?: string, flowName?: string): Promise<CreateSessionResponse | null> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'SET_ERROR', payload: null });

      const response = await SessionService.createSession({ user_id: userId, flow_name: flowName });

      // 创建Session对象
      const session: Session = {
        session_id: response.session_id,
        user_id: userId,
        flow_name: response.flow_name,
        current_state: response.current_state,
        status: SessionStatus.ACTIVE,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      dispatch({ type: 'SET_CURRENT_SESSION', payload: session });
      message.success('会话创建成功');
      return response;
    } catch (error: any) {
      const errorMessage = error.message || '创建会话失败';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
      message.error(errorMessage);
      return null;
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  }, []);

  // 加载会话详情
  const loadSessionDetail = useCallback(async (sessionId: string): Promise<void> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'SET_ERROR', payload: null });

      const detail = await SessionService.getSessionDetail(sessionId);
      dispatch({ type: 'SET_SESSION_DETAIL', payload: detail });

      // 同时更新当前会话信息
      const session: Session = {
        session_id: detail.session_id,
        user_id: detail.user_id,
        flow_name: detail.flow_name,
        current_state: detail.current_state,
        status: detail.status,
        created_at: detail.created_at,
        updated_at: detail.updated_at
      };
      dispatch({ type: 'SET_CURRENT_SESSION', payload: session });
    } catch (error: any) {
      const errorMessage = error.message || '加载会话详情失败';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
      message.error(errorMessage);
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  }, []);

  // 处理用户输入
  const processInput = useCallback(async (sessionId: string, input: UserInput): Promise<ProcessInputResponse | null> => {
    try {
      dispatch({ type: 'SET_SUBMITTING', payload: true });
      dispatch({ type: 'SET_ERROR', payload: null });

      const response = await SessionService.processInput(sessionId, input);

      if (response.success) {
        // 重新加载会话详情以获取最新状态
        await loadSessionDetail(sessionId);

        if (response.completed) {
          message.success(response.message || '流程已完成！');
          dispatch({ type: 'UPDATE_SESSION_STATUS', payload: SessionStatus.COMPLETED });
        } else {
          message.success('输入处理成功');
        }
      } else {
        const errorMessage = response.error || '处理输入失败';
        dispatch({ type: 'SET_ERROR', payload: errorMessage });
        message.error(errorMessage);
      }

      return response;
    } catch (error: any) {
      const errorMessage = error.message || '处理输入失败';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
      message.error(errorMessage);
      return null;
    } finally {
      dispatch({ type: 'SET_SUBMITTING', payload: false });
    }
  }, [loadSessionDetail]);

  // 暂停会话
  const pauseSession = useCallback(async (sessionId: string): Promise<void> => {
    try {
      dispatch({ type: 'SET_SUBMITTING', payload: true });
      dispatch({ type: 'SET_ERROR', payload: null });

      await SessionService.pauseSession(sessionId);
      dispatch({ type: 'UPDATE_SESSION_STATUS', payload: SessionStatus.PAUSED });
      message.success('会话已暂停');
    } catch (error: any) {
      const errorMessage = error.message || '暂停会话失败';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
      message.error(errorMessage);
    } finally {
      dispatch({ type: 'SET_SUBMITTING', payload: false });
    }
  }, []);

  // 恢复会话
  const resumeSession = useCallback(async (sessionId: string): Promise<void> => {
    try {
      dispatch({ type: 'SET_SUBMITTING', payload: true });
      dispatch({ type: 'SET_ERROR', payload: null });

      await SessionService.resumeSession(sessionId);
      dispatch({ type: 'UPDATE_SESSION_STATUS', payload: SessionStatus.ACTIVE });
      message.success('会话已恢复');
    } catch (error: any) {
      const errorMessage = error.message || '恢复会话失败';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
      message.error(errorMessage);
    } finally {
      dispatch({ type: 'SET_SUBMITTING', payload: false });
    }
  }, []);

  // 删除会话
  const deleteSession = useCallback(async (sessionId: string): Promise<void> => {
    try {
      dispatch({ type: 'SET_SUBMITTING', payload: true });
      dispatch({ type: 'SET_ERROR', payload: null });

      await SessionService.deleteSession(sessionId);
      dispatch({ type: 'RESET_SESSION' });
      message.success('会话已删除');
    } catch (error: any) {
      const errorMessage = error.message || '删除会话失败';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
      message.error(errorMessage);
    } finally {
      dispatch({ type: 'SET_SUBMITTING', payload: false });
    }
  }, []);

  // 清除会话
  const clearSession = useCallback(() => {
    dispatch({ type: 'RESET_SESSION' });
  }, []);

  // 上下文值
  const contextValue: SessionContextType = {
    state,
    createSession,
    loadSessionDetail,
    processInput,
    pauseSession,
    resumeSession,
    deleteSession,
    clearSession,
    setError
  };

  return (
    <SessionContext.Provider value={contextValue}>
      {children}
    </SessionContext.Provider>
  );
};

// Hook for using session context
export const useSession = (): SessionContextType => {
  const context = useContext(SessionContext);
  if (context === undefined) {
    throw new Error('useSession must be used within a SessionProvider');
  }
  return context;
};

export default SessionContext;