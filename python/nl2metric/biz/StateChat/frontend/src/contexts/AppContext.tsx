import React, { createContext, useContext, useReducer, useCallback, useEffect, ReactNode } from 'react';
import { FlowConfig } from '../types/api';
import { HealthCheckResponse } from '../types/responses';
import { FlowService } from '../services/flowService';
import { message } from 'antd';

// 应用状态类型
interface AppState {
  flows: FlowConfig[];
  selectedFlow: FlowConfig | null;
  healthStatus: HealthCheckResponse | null;
  loading: boolean;
  error: string | null;
}

// 动作类型
type AppAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_FLOWS'; payload: FlowConfig[] }
  | { type: 'SET_SELECTED_FLOW'; payload: FlowConfig | null }
  | { type: 'SET_HEALTH_STATUS'; payload: HealthCheckResponse | null }
  | { type: 'RESET_APP' };

// 上下文类型
interface AppContextType {
  state: AppState;
  loadFlows: () => Promise<void>;
  selectFlow: (flowName: string) => void;
  loadHealthStatus: () => Promise<void>;
  setError: (error: string | null) => void;
  clearError: () => void;
}

// 初始状态
const initialState: AppState = {
  flows: [],
  selectedFlow: null,
  healthStatus: null,
  loading: false,
  error: null
};

// Reducer
const appReducer = (state: AppState, action: AppAction): AppState => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    case 'SET_FLOWS':
      return { ...state, flows: action.payload };
    case 'SET_SELECTED_FLOW':
      return { ...state, selectedFlow: action.payload };
    case 'SET_HEALTH_STATUS':
      return { ...state, healthStatus: action.payload };
    case 'RESET_APP':
      return initialState;
    default:
      return state;
  }
};

// 创建上下文
const AppContext = createContext<AppContextType | undefined>(undefined);

// Provider组件
interface AppProviderProps {
  children: ReactNode;
}

export const AppProvider: React.FC<AppProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(appReducer, initialState);

  // 设置错误
  const setError = useCallback((error: string | null) => {
    dispatch({ type: 'SET_ERROR', payload: error });
  }, []);

  // 清除错误
  const clearError = useCallback(() => {
    dispatch({ type: 'SET_ERROR', payload: null });
  }, []);

  // 加载流程列表
  const loadFlows = useCallback(async (): Promise<void> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'SET_ERROR', payload: null });

      const flows = await FlowService.listFlows();
      dispatch({ type: 'SET_FLOWS', payload: flows });

      // 如果没有选中的流程且有可用流程，自动选择第一个
      if (!state.selectedFlow && flows.length > 0) {
        dispatch({ type: 'SET_SELECTED_FLOW', payload: flows[0] });
      }
    } catch (error: any) {
      const errorMessage = error.message || '加载流程列表失败';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
      message.error(errorMessage);
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  }, [state.selectedFlow]);

  // 选择流程
  const selectFlow = useCallback((flowName: string) => {
    const flow = state.flows.find(f => f.flow_name === flowName);
    dispatch({ type: 'SET_SELECTED_FLOW', payload: flow || null });
  }, [state.flows]);

  // 加载健康状态
  const loadHealthStatus = useCallback(async (): Promise<void> => {
    try {
      const healthStatus = await FlowService.healthCheck();
      dispatch({ type: 'SET_HEALTH_STATUS', payload: healthStatus });
    } catch (error: any) {
      console.error('Failed to load health status:', error);
      // 健康检查失败不显示错误消息，只在控制台记录
    }
  }, []);

  // 初始化时加载数据
  useEffect(() => {
    loadFlows();
    loadHealthStatus();
  }, [loadFlows, loadHealthStatus]);

  // 定期检查健康状态
  useEffect(() => {
    const interval = setInterval(() => {
      loadHealthStatus();
    }, 30000); // 每30秒检查一次

    return () => clearInterval(interval);
  }, [loadHealthStatus]);

  // 上下文值
  const contextValue: AppContextType = {
    state,
    loadFlows,
    selectFlow,
    loadHealthStatus,
    setError,
    clearError
  };

  return (
    <AppContext.Provider value={contextValue}>
      {children}
    </AppContext.Provider>
  );
};

// Hook for using app context
export const useApp = (): AppContextType => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
};

export default AppContext;
