# 简化的测试流程配置
flow_name: "simple_test_flow"
description: "简化的测试流程"
version: "1.0.0"

states:
  welcome_user:
    name: "welcome_user"
    prompt: "您好！欢迎使用状态机引擎。"
    input_type: "text"
    next: "check_registration"

  check_registration:
    name: "check_registration"
    prompt: "您是否已经注册？"
    input_type: "choice"
    options: ["是", "否"]
    next:
      是: "main_process"
      否: "register"

  register:
    name: "register"
    prompt: "请选择注册类型："
    input_type: "choice"
    options: ["个人", "企业"]
    next:
      个人: "collect_personal_info"
      企业: "collect_business_info"

  collect_personal_info:
    name: "collect_personal_info"
    prompt: "请提供个人信息："
    input_type: "file"
    requires: ["name", "email", "phone"]
    next: "main_process"

  collect_business_info:
    name: "collect_business_info"
    prompt: "请提供企业信息："
    input_type: "file"
    requires: ["company_name", "business_type", "contact_email"]
    next: "main_process"

  main_process:
    name: "main_process"
    prompt: "请选择您需要的服务："
    input_type: "multichoice"
    options: ["服务A", "服务B", "服务C"]
    next:
      服务A: "service_a"
      服务B: "service_b"
      服务C: "service_c"
      none: "done"

  service_a:
    name: "service_a"
    prompt: "正在处理服务A..."
    input_type: "text"
    next: "done"

  service_b:
    name: "service_b"
    prompt: "正在处理服务B..."
    input_type: "text"
    next: "done"

  service_c:
    name: "service_c"
    prompt: "正在处理服务C..."
    input_type: "text"
    next: "done"

  done:
    name: "done"
    prompt: "流程已完成，感谢使用！"
    input_type: "text"
    next: null

# 初始状态
initial_state: "welcome_user"

# 终止状态
final_states: ["done"]
