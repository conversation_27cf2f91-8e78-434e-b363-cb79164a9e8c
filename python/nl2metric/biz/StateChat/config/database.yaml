# 数据库配置
development:
  type: sqlite
  database: data/state_chat.db
  echo: true

test:
  type: sqlite
  database: ":memory:"  # 使用内存数据库进行测试
  echo: true

production:
  type: mysql
  host: ${DB_HOST}  # 从环境变量获取
  port: ${DB_PORT:-3306}  # 默认3306
  database: ${DB_NAME}
  user: ${DB_USER}
  password: ${DB_PASSWORD}
  charset: utf8mb4
  pool_size: 20
  max_overflow: 10
  echo: false

# 连接池配置
pool:
  # 连接池大小
  pool_size: 5
  # 最大溢出连接数
  max_overflow: 10
  # 连接超时时间(秒)
  timeout: 30
  # 回收连接的时间间隔(秒)
  recycle: 3600
  # 是否在引擎创建时预创建连接
  pre_ping: true
  # 是否显示SQL语句
  echo: false

# 迁移配置
migrations:
  # 迁移脚本目录
  directory: migrations
  # 是否自动迁移
  auto_migrate: true
  # 是否在启动时验证当前数据库版本
  check_version: true
