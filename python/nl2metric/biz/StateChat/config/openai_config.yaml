# OpenAI配置
api:
  base_url: "http://123.181.192.99:16701/v1"  # 或者使用自定义endpoint
  api_key: "xxxx"  # 从环境变量获取
  organization: "${OPENAI_ORG_ID}"  # 可选

models:
  chat:
    name: "qwen3_32b"  # 用于闲聊的模型
    temperature: 0.7
    max_tokens: 1000
    top_p: 0.95
    frequency_penalty: 0.0
    presence_penalty: 0.0
    extra_body:
      chat_template_kwargs:
        enable_thinking: False
    system_prompt: |
      你是一个友好的AI助手，负责与用户进行日常对话。
      请保持对话轻松愉快，同时专业且有帮助。
      如果涉及到具体的业务问题，建议用户通过正式流程办理。

  intent:
    name: "qwen3_32b"  # 用于意图识别的模型
    temperature: 0.3  # 降低随机性以提高准确性
    max_tokens: 500
    top_p: 0.9
    frequency_penalty: 0.0
    presence_penalty: 0.0
    extra_body:
      chat_template_kwargs:
        enable_thinking: False
    system_prompt: |
      你是一个专门的意图识别助手。
      请分析用户输入，并将其分类为以下意图之一：
      - chat: 闲聊、问候、社交对话
      - rag_query: 知识查询、咨询问题、了解信息
      - business: 业务办理、申请、提交
      - unknown: 无法确定意图
      仅返回意图类型，不要包含其他内容。

rate_limits:
  max_requests_per_minute: 60
  max_tokens_per_minute: 90000

retry:
  max_retries: 3
  initial_delay: 1
  max_delay: 10
  backoff_factor: 2

cache:
  enabled: true
  ttl: 3600  # 缓存1小时
  max_size: 1000  # 最多缓存1000条记录

logging:
  level: "INFO"
  request_logging: true
  response_logging: false  # 避免记录敏感信息
