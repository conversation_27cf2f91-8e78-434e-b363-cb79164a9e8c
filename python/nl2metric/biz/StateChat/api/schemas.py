"""
API数据模型定义
"""

from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Any, Union

from pydantic import BaseModel, Field

from models.session import SessionStatus
from models.state import InputType, StateNode


class CreateSessionRequest(BaseModel):
    """创建会话请求"""

    user_id: Optional[str] = Field(None, description="用户ID")
    flow_name: Optional[str] = Field(None, description="流程名称")

class ChatRequest(BaseModel):
    """聊天请求"""

    text: str = Field(..., description="用户输入文本")
    session_id: Optional[str] = Field(None, description="会话ID")

class CreateSessionResponse(BaseModel):
    """创建会话响应"""

    session_id: str = Field(..., description="会话ID")
    flow_name: str = Field(..., description="流程名称")
    current_state: str = Field(..., description="当前状态")
    prompt: str = Field(..., description="状态提示")
    input_type: str = Field(..., description="输入类型")
    options: Optional[List[str]] = Field(None, description="选择项")
    requires: Optional[List[str]] = Field(None, description="必需字段")


class UserInputRequest(BaseModel):
    """用户输入请求"""

    input_type: InputType = Field(..., description="输入类型")
    value: Union[str, List[str], Dict[str, Any]] = Field(..., description="输入值")
    files: Optional[List[str]] = Field(None, description="上传文件路径列表")


class ProcessInputResponse(BaseModel):
    """处理输入响应"""

    success: bool = Field(..., description="是否成功")
    completed: bool = Field(False, description="流程是否完成")
    error: Optional[str] = Field(None, description="错误信息")
    intent_type: Optional[str] = Field(None, description="意图类型")
    current_state: StateNode = Field(None, description="当前状态")
    next_states: List[StateNode] = Field(
        default_factory=list, description="下一个状态集"
    )
    progress: Optional[float] = Field(None, description="进度百分比")
    message: Optional[str] = Field(None, description="消息")


class SessionSummaryResponse(BaseModel):
    """会话摘要响应"""

    session_id: str = Field(..., description="会话ID")
    user_id: Optional[str] = Field(None, description="用户ID")
    flow_name: str = Field(..., description="流程名称")
    current_state: str = Field(..., description="当前状态")
    status: SessionStatus = Field(..., description="会话状态")
    progress_percentage: float = Field(..., description="进度百分比")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        use_enum_values = True
        json_encoders = {datetime: lambda v: v.isoformat()}


class SessionDetailResponse(BaseModel):
    """会话详情响应"""

    session_id: str = Field(..., description="会话ID")
    user_id: Optional[str] = Field(None, description="用户ID")
    flow_name: str = Field(..., description="流程名称")
    current_state: str = Field(..., description="当前状态")
    status: SessionStatus = Field(..., description="会话状态")
    context: Dict[str, Any] = Field(..., description="会话上下文")
    history: List[Dict[str, Any]] = Field(..., description="状态转移历史")
    state: StateNode = Field(..., description="当前状态节点")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        use_enum_values = True
        json_encoders = {datetime: lambda v: v.isoformat()}


class IntentProcessResponse(BaseModel):
    """意图处理响应"""

    intent_type: str = Field(..., description="意图类型")
    success: bool = Field(..., description="是否成功")
    response: Optional[str] = Field(None, description="处理响应")
    should_continue_flow: bool = Field(..., description="是否继续业务流程")
    data: Optional[Dict[str, Any]] = Field(None, description="返回数据")


class FileUploadResponse(BaseModel):
    """文件上传响应"""

    filename: str = Field(..., description="文件名")
    file_path: str = Field(..., description="文件存储路径")
    file_size: int = Field(..., description="文件大小")
    content_type: str = Field(..., description="文件类型")
    upload_time: datetime = Field(..., description="上传时间")

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}


class IntentType(str, Enum):
    """意图类型"""

    CHAT = "chat"  # 闲聊
    RAG_QUERY = "rag_query"  # 查询知识库
    BUSINESS = "business"  # 继续业务办理
    UNKNOWN = "unknown"  # 未知意图


class IntentResponse(BaseModel):
    """意图响应"""

    intent_type: IntentType = Field(..., description="意图类型")


class FlowConfigResponse(BaseModel):
    """流程配置响应"""

    flow_name: str = Field(..., description="流程名称")
    description: str = Field(..., description="流程描述")
    version: str = Field(..., description="版本号")
    initial_state: str = Field(..., description="初始状态")
    final_states: List[str] = Field(..., description="终止状态列表")
    total_states: int = Field(..., description="总状态数")


class StateInfoResponse(BaseModel):
    """状态信息响应"""

    name: str = Field(..., description="状态名称")
    prompt: str = Field(..., description="状态提示")
    input_type: str = Field(..., description="输入类型")
    options: Optional[List[str]] = Field(None, description="选择项")
    requires: Optional[List[str]] = Field(None, description="必需字段")
    next_states: Optional[List[str]] = Field(None, description="可能的下一状态")


class ValidationResultResponse(BaseModel):
    """验证结果响应"""

    valid: bool = Field(..., description="是否有效")
    errors: List[str] = Field(..., description="错误列表")
    warnings: List[str] = Field(..., description="警告列表")


class ErrorResponse(BaseModel):
    """错误响应"""

    error: str = Field(..., description="错误类型")
    message: str = Field(..., description="错误消息")
    details: Optional[Dict[str, Any]] = Field(None, description="错误详情")


class SuccessResponse(BaseModel):
    """成功响应"""

    success: bool = Field(True, description="操作成功")
    message: str = Field(..., description="成功消息")
    data: Optional[Dict[str, Any]] = Field(None, description="返回数据")


class PaginationParams(BaseModel):
    """分页参数"""

    page: int = Field(1, ge=1, description="页码")
    size: int = Field(20, ge=1, le=100, description="每页大小")


class PaginatedResponse(BaseModel):
    """分页响应"""

    items: List[Any] = Field(..., description="数据项列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页大小")
    pages: int = Field(..., description="总页数")

    @classmethod
    def create(cls, items: List[Any], total: int, page: int, size: int):
        """创建分页响应"""
        pages = (total + size - 1) // size  # 向上取整
        return cls(items=items, total=total, page=page, size=size, pages=pages)


class HealthCheckResponse(BaseModel):
    """健康检查响应"""

    status: str = Field("healthy", description="服务状态")
    timestamp: datetime = Field(default_factory=datetime.now, description="检查时间")
    version: str = Field(..., description="服务版本")
    uptime: Optional[float] = Field(None, description="运行时间(秒)")

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}
