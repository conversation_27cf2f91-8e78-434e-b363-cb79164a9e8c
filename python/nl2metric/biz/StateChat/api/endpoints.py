"""
API端点定义
"""

import json
import os
import time
from datetime import datetime
from typing import List

from fastapi import APIRouter, HTTPException, UploadFile, File, Body, Depends
from fastapi.responses import StreamingResponse
from loguru import logger
from sqlalchemy.ext.asyncio import AsyncSession

from core import IntentProcessor
from core.state_machine import StateMachine
from models.state import UserInput
from storage.database import get_db_session
from utils.config_loader import ConfigLoader
from .schemas import (
    CreateSessionRequest,
    CreateSessionResponse,
    UserInputRequest,
    ProcessInputResponse,
    SessionDetailResponse,
    FileUploadResponse,
    FlowConfigResponse,
    StateInfoResponse,
    ValidationResultResponse,
    SuccessResponse,
    PaginationParams,
    PaginatedResponse,
    HealthCheckResponse,
    IntentResponse,
    IntentType, ChatRequest,
)

# 创建路由
router = APIRouter()

# 加载流程配置
config_path = os.getenv("FLOW_CONFIG_PATH", "config/simple_test_flow.yaml")
flow_config = ConfigLoader.load_flow_config(config_path)

# 启动时间
START_TIME = time.time()


@router.post("/sessions", response_model=CreateSessionResponse)
async def create_session(
    request: CreateSessionRequest, db_session: AsyncSession = Depends(get_db_session)
):
    """创建新会话"""
    try:
        state_machine = StateMachine(flow_config, db_session=db_session)
        session = await state_machine.create_session(request.user_id)
        state = await state_machine.get_current_state(session.session_id)

        return CreateSessionResponse(
            session_id=session.session_id,
            flow_name=session.flow_name,
            current_state=session.current_state,
            prompt=state.prompt,
            input_type=state.input_type.value
            if hasattr(state.input_type, "value")
            else state.input_type,
            options=state.options,
            requires=state.requires,
        )

    except Exception as e:
        logger.exception(f"创建会话失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/sessions/{session_id}/intent", response_model=IntentResponse)
async def get_intent(
    req:   ChatRequest, db_session: AsyncSession = Depends(get_db_session)
):
    """获取意图"""
    # todo session_id 获取上下文
    intent: IntentType = await IntentProcessor().process(req.text,)
    return IntentResponse(intent_type=intent)


@router.post("/sessions/{session_id}/chat")
async def chat(
     req:   ChatRequest, db_session: AsyncSession = Depends(get_db_session)
):
    """会话闲聊"""
    # todo session_id 获取上下文
    return StreamingResponse(
        IntentProcessor().handle_chat_stream(req.text, {})
    )


@router.post("/sessions/{session_id}/rag")
async def rag(
    req:   ChatRequest, db_session: AsyncSession = Depends(get_db_session)
):
    """会话闲聊"""
    # todo session_id 获取上下文
    return StreamingResponse(
        IntentProcessor().handle_rag_query_stream(req.text, {}),
    )


@router.post(
    "/sessions/{session_id}/input",
    response_model=ProcessInputResponse,
)
async def process_input(
    session_id: str,
    request: UserInputRequest,
    db_session: AsyncSession = Depends(get_db_session),
):
    """处理用户输入"""
    try:
        state_machine = StateMachine(
            flow_config,
            db_session=db_session,
        )
        user_input = UserInput(
            input_type=request.input_type, value=request.value, files=request.files
        )

        result = await state_machine.process_user_input(session_id, user_input)
        return result

    except Exception as e:
        logger.exception(f"处理输入失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/sessions/{session_id}", response_model=SessionDetailResponse)
async def get_session_detail(
    session_id: str, db_session: AsyncSession = Depends(get_db_session)
):
    """获取会话详情"""
    try:
        state_machine = StateMachine(flow_config, db_session=db_session)
        session = await state_machine.get_session(session_id)
        context = await state_machine.get_context(session_id)
        current_state = await state_machine.get_current_state(session_id)

        return SessionDetailResponse(
            session_id=session.session_id,
            user_id=session.user_id,
            flow_name=session.flow_name,
            current_state=session.current_state,
            status=session.status,
            context=context.model_dump(),
            history=session.get_history(),
            state=current_state,
            created_at=session.created_at,
            updated_at=session.updated_at,
        )

    except Exception as e:
        logger.exception(f"获取会话详情失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/sessions", response_model=PaginatedResponse)
async def list_sessions(
    params: PaginationParams = Depends(),
    db_session: AsyncSession = Depends(get_db_session),
):
    """获取会话列表"""
    try:
        state_machine = StateMachine(flow_config, db_session=db_session)
        # 获取所有会话摘要
        summaries = await state_machine.list_sessions()

        # 计算分页
        start = (params.page - 1) * params.size
        end = start + params.size
        page_items = summaries[start:end]

        return PaginatedResponse.create(
            items=page_items, total=len(summaries), page=params.page, size=params.size
        )

    except Exception as e:
        logger.exception(f"获取会话列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/sessions/{session_id}/pause")
async def pause_session(
    session_id: str, db_session: AsyncSession = Depends(get_db_session)
):
    """暂停会话"""
    try:
        state_machine = StateMachine(flow_config, db_session=db_session)
        await state_machine.pause_session(session_id)
        return SuccessResponse(message=f"会话 {session_id} 已暂停")

    except Exception as e:
        logger.exception(f"暂停会话失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/sessions/{session_id}/resume")
async def resume_session(
    session_id: str, db_session: AsyncSession = Depends(get_db_session)
):
    """恢复会话"""
    try:
        state_machine = StateMachine(flow_config, db_session=db_session)
        await state_machine.resume_session(session_id)
        return SuccessResponse(message=f"会话 {session_id} 已恢复")

    except Exception as e:
        logger.exception(f"恢复会话失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/sessions/{session_id}")
async def delete_session(
    session_id: str, db_session: AsyncSession = Depends(get_db_session)
):
    """删除会话"""
    try:
        state_machine = StateMachine(flow_config, db_session=db_session)
        await state_machine.delete_session(session_id)
        return SuccessResponse(message=f"会话 {session_id} 已删除")

    except Exception as e:
        logger.exception(f"删除会话失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/sessions/{session_id}/files", response_model=FileUploadResponse)
async def upload_file(
    session_id: str,
    file: UploadFile = File(...),
    db_session: AsyncSession = Depends(get_db_session),
):
    """上传文件"""
    try:
        # state_machine = StateMachine(flow_config, db_session=db_session)
        # 创建上传目录
        upload_dir = f"uploads/{session_id}"
        os.makedirs(upload_dir, exist_ok=True)

        # 保存文件
        file_path = os.path.join(upload_dir, file.filename)
        with open(file_path, "wb") as f:
            content = await file.read()
            f.write(content)

        # 获取文件信息
        file_size = os.path.getsize(file_path)

        return FileUploadResponse(
            filename=file.filename,
            file_path=file_path,
            file_size=file_size,
            content_type=file.content_type,
            upload_time=datetime.now(),
        )

    except Exception as e:
        logger.exception(f"文件上传失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health", response_model=HealthCheckResponse)
async def health_check():
    """健康检查"""
    return HealthCheckResponse(
        status="healthy", version=flow_config.version, uptime=time.time() - START_TIME
    )


@router.post("/validate-config", response_model=ValidationResultResponse)
async def validate_config(config_path: str = Body(..., embed=True)):
    """验证流程配置"""
    try:
        result = ConfigLoader.validate_config_file(config_path)
        return ValidationResultResponse(
            valid=result["valid"], errors=result["errors"], warnings=result["warnings"]
        )

    except Exception as e:
        logger.exception(f"配置验证失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))
