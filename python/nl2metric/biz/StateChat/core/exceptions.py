"""
状态机异常类定义
"""


class StateMachineError(Exception):
    """状态机基础异常"""

    pass


class StateNotFoundError(StateMachineError):
    """状态未找到异常"""

    def __init__(self, state_name: str):
        self.state_name = state_name
        super().__init__(f"状态 '{state_name}' 未找到")


class InvalidTransitionError(StateMachineError):
    """无效状态转移异常"""

    def __init__(self, from_state: str, to_state: str, reason: str = ""):
        self.from_state = from_state
        self.to_state = to_state
        self.reason = reason
        message = f"无效的状态转移: {from_state} -> {to_state}"
        if reason:
            message += f" ({reason})"
        super().__init__(message)


class SessionNotFoundError(StateMachineError):
    """会话未找到异常"""

    def __init__(self, session_id: str):
        self.session_id = session_id
        super().__init__(f"会话 '{session_id}' 未找到")


class FlowConfigError(StateMachineError):
    """流程配置异常"""

    def __init__(self, message: str):
        super().__init__(f"流程配置错误: {message}")


class ValidationError(StateMachineError):
    """验证异常"""

    def __init__(self, field_name: str, message: str):
        self.field_name = field_name
        super().__init__(f"字段 '{field_name}' 验证失败: {message}")


class FileUploadError(StateMachineError):
    """文件上传异常"""

    def __init__(self, filename: str, reason: str):
        self.filename = filename
        self.reason = reason
        super().__init__(f"文件 '{filename}' 上传失败: {reason}")


class BusinessLogicError(StateMachineError):
    """业务逻辑异常"""

    def __init__(self, message: str):
        super().__init__(f"业务逻辑错误: {message}")
