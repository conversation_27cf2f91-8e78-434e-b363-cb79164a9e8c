"""
意图识别模块 - 识别用户输入的意图并控制流程流转
"""

from typing import Dict, Any, AsyncGenerator

from loguru import logger

from api.schemas import IntentType
from utils.openai_client import default_openai_client


class IntentClassifier:
    """意图分类器"""

    def __init__(self):
        # 意图关键词匹配规则
        self.intent_patterns = {
            IntentType.CHAT: [
                "你好",
                "嗨",
                "在吗",
                "聊天",
                "怎么样",
                "再见",
                "谢谢",
                "天气",
                "新闻",
                "笑话",
            ],
            IntentType.RAG_QUERY: [
                "查询",
                "搜索",
                "查找",
                "了解",
                "什么是",
                "怎么办",
                "如何",
                "说明",
                "介绍",
                "文档",
            ],
            IntentType.BUSINESS: [
                "申请",
                "办理",
                "提交",
                "继续",
                "下一步",
                "许可",
                "执照",
                "注册",
                "上传",
                "确认",
                "选择",
                "使用",
            ],
        }

    def process(self, text: str) -> IntentType:
        """
        对输入文本进行意图分类

        Args:
            text: 输入文本

        Returns:
            IntentType: 识别出的意图类型
        """
        # 转换为小写进行匹配
        text = text.lower()

        # 遍历意图模式进行匹配
        for intent_type, patterns in self.intent_patterns.items():
            for pattern in patterns:
                if pattern in text:
                    logger.info(f"规则意图识别结果: {text} -> {intent_type.value}")
                    return intent_type

        # 未匹配到任何模式返回未知意图
        logger.info(f"规则意图识别未匹配: {text} -> unknown")
        return IntentType.UNKNOWN


class IntentProcessor:
    """意图处理器"""

    def __init__(self):
        """
        初始化意图处理器

        Args:
            use_llm: 是否使用大语言模型进行意图识别，否则使用规则匹配
        """

        self.classifier = IntentClassifier()
        self.intent_patterns = self.classifier.intent_patterns

        self.llm_client = default_openai_client

    async def process(self, text: str, use_llm: bool = True) -> IntentType:
        """
        对输入文本进行意图分类

        Args:
            text: 输入文本

        Returns:
            IntentType: 识别出的意图类型
        """
        if use_llm:
            try:
                # 使用大语言模型进行意图识别
                intent_str = await self.llm_client.get_intent(text)
                logger.info(f"LLM意图识别结果: {text} -> {intent_str}")

                # 将字符串转换为枚举
                for intent_type in IntentType:
                    if intent_type.value == intent_str:
                        return intent_type

                # 如果LLM返回的意图无效，回退到规则匹配
                logger.warning(f"LLM返回的意图类型无效: {intent_str}，回退到规则匹配")
                return self.classifier.process(text)

            except Exception as e:
                # 如果LLM调用失败，回退到规则匹配
                logger.error(f"LLM意图识别失败: {e}，回退到规则匹配")
                return self.classifier.process(text)
        else:
            # 使用规则匹配
            return self.classifier.process(text)

    async def handle_chat_stream(
        self, text: str, context: Dict[str, Any]
    ) -> AsyncGenerator[str, None]:
        """处理闲聊意图 (流式)"""

        try:
            async for chunk in self.llm_client.get_chat_response_stream(text):
                yield chunk
        except Exception as e:
            logger.error(f"LLM聊天流式调用失败: {e}")
            yield "抱歉，我现在无法进行对话，请稍后再试。"

    async def handle_rag_query_stream(
        self, text: str, context: Dict[str, Any]
    ) -> AsyncGenerator[str, None]:
        """处理知识库查询意图 (流式)"""

        try:
            system_prompt = """
                你是一个专业的业务咨询助手，专门回答关于新加坡咖啡店营业执照申请的问题。
                请提供准确、专业的答案，并在适当的时候建议用户通过正式流程办理相关业务。
                """
            async for chunk in self.llm_client.get_chat_response_stream(
                text, system_prompt=system_prompt
            ):
                yield chunk
        except Exception as e:
            logger.error(f"LLM知识库查询流式失败: {e}")
            yield "抱歉，我暂时无法访问知识库，请稍后再试。"

    async def handle_business(
        self, text: str, context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """处理业务办理意图"""
        return {
            "intent": IntentType.BUSINESS.value,
            "success": True,
            "response": None,  # 业务处理不直接返回响应
            "should_continue_flow": True,  # 继续业务流程
        }


# 创建默认意图处理器实例
default_intent_processor = IntentProcessor()
