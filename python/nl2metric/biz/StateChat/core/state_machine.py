"""
状态机核心引擎实现
"""

import uuid
from datetime import datetime
from types import MethodType
from typing import Dict, List, Optional, Union

from loguru import logger
from pydantic import ValidationError
from sqlalchemy.ext.asyncio import AsyncSession
from transitions import Machine

from api.schemas import ProcessInputResponse
from models.context import FlowContext, FileUpload
from models.session import UserSession, SessionStatus, SessionSummary
from models.state import FlowConfig, StateNode, UserInput, InputType, StateTransition
from storage.session_store import SessionStore, ContextStore
from .exceptions import StateNotFoundError, SessionNotFoundError, FlowConfigError
from .intent import IntentType


class StateMachine:
    """状态机核心引擎"""

    def __init__(
        self,
        flow_config: FlowConfig,
        db_session: AsyncSession,
    ):
        """
        初始化状态机

        Args:
            flow_config: 流程配置
            db_session: 数据库会话
        """
        self.flow_config = flow_config
        # 验证流程配置
        self._validate_flow_config()
        self.db_session = db_session

        # 初始化存储管理器
        self.session_store = SessionStore(db_session)
        self.context_store = ContextStore(db_session)

        self.state_map: Dict[str, StateNode] = flow_config.states
        self.states = list(self.state_map.keys())
        self.transitions: List[dict] = []
        self.current_session: Optional[UserSession] = None
        self.current_context: Optional[FlowContext] = None
        self.current_input: Optional[UserInput] = None

        for stage in flow_config.states.values():
            if isinstance(stage.next, dict):  # conditional branching
                for src, dest in stage.next.items():
                    self.transitions.append(
                        {
                            "trigger": f"advance_from_{stage.name}_on_{dest}",
                            "source": stage.name,
                            "dest": dest,
                            "conditions": [f"can_{stage.name}_on_{dest}"],
                        }
                    )
                    setattr(
                        self,
                        f"can_{stage.name}_on_{dest}",
                        MethodType(lambda self: True, self),
                    )

            elif isinstance(stage.next, str):  # linear flow
                self.transitions.append(
                    {
                        "trigger": f"advance_{stage.next}",
                        "source": stage.name,
                        "dest": stage.next,
                        "conditions": [f"can_{stage.next}"],
                    }
                )
                setattr(
                    self,
                    f"can_{stage.name}_on_{stage.next}",
                    MethodType(lambda self: True, self),
                )
            elif stage.next is None:  # no next state
                self.transitions.append(
                    {
                        "trigger": f"advance_{stage.name}",
                        "source": "*",
                        "dest": stage.name,
                        "conditions": [f"can_{stage.name}"],
                    }
                )
                setattr(
                    self,
                    f"can_{stage.name}",
                    MethodType(lambda self: True, self),
                )
        self.m = Machine(
            model=self,
            states=self.states,
            transitions=self.transitions,
            initial=flow_config.initial_state,
            auto_transitions=False,
        )

        logger.info(f"状态机初始化完成: {flow_config.flow_name} v{flow_config.version}")

    def _validate_flow_config(self):
        """验证流程配置的完整性"""
        # 检查初始状态是否存在
        if self.flow_config.initial_state not in self.flow_config.states:
            raise FlowConfigError(f"初始状态 '{self.flow_config.initial_state}' 不存在")

        # 检查终止状态是否存在
        for final_state in self.flow_config.final_states:
            if final_state not in self.flow_config.states:
                raise FlowConfigError(f"终止状态 '{final_state}' 不存在")

        # 检查状态转移的目标状态是否存在
        for state_name, state in self.flow_config.states.items():
            if state.next:
                if isinstance(state.next, str):
                    if state.next not in self.flow_config.states:
                        raise FlowConfigError(
                            f"状态 '{state_name}' 的目标状态 '{state.next}' 不存在"
                        )
                elif isinstance(state.next, dict):
                    for condition, target_state in state.next.items():
                        if target_state and target_state not in self.flow_config.states:
                            raise FlowConfigError(
                                f"状态 '{state_name}' 的条件 '{condition}' 对应的目标状态 '{target_state}' 不存在"
                            )

    async def create_session(
        self, user_id: Optional[str] = None, session_id: Optional[str] = None
    ) -> UserSession:
        """
        创建新的用户会话

        Args:
            user_id: 用户ID，可选
            session_id: 会话ID，可选，不提供则自动生成

        Returns:
            UserSession: 新创建的会话对象
        """
        if not session_id:
            session_id = str(uuid.uuid4())

        # 创建会话
        session = UserSession(
            session_id=session_id,
            user_id=user_id,
            flow_name=self.flow_config.flow_name,
            current_state=self.flow_config.initial_state,
            status=SessionStatus.ACTIVE,
        )

        # 创建上下文
        context = FlowContext(
            session_id=session_id, flow_name=self.flow_config.flow_name
        )

        # 存储会话和上下文 (根据是否使用数据库)

        await self.session_store.create_session(session)
        await self.context_store.create_context(context)

        logger.info(
            f"创建新会话: {session_id}, 用户: {user_id}, 初始状态: {self.flow_config.initial_state}"
        )

        return session

    async def get_session(self, session_id: str) -> UserSession:
        """获取会话"""
        current_session = await self.session_store.get_session(session_id)
        if not current_session:
            raise SessionNotFoundError(session_id)
        return current_session

    async def get_context(self, session_id: str) -> FlowContext:
        """获取会话上下文"""
        current_contexts = await self.context_store.get_context(session_id)
        if not current_contexts:
            raise SessionNotFoundError(session_id)
        return current_contexts

    async def get_current_state(self, session_id: str) -> StateNode:
        """获取当前状态节点"""
        session = await self.get_session(session_id)
        current_state_name = session.current_state
        if current_state_name not in self.flow_config.states:
            raise StateNotFoundError(current_state_name)
        return self.flow_config.states[current_state_name]

    async def add_transition(self, session_id: str, transition: StateTransition):
        await self.session_store.add_transition(session_id, transition)
        self.current_session.add_transition(transition)

    async def process_user_input(
        self, session_id: str, user_input: UserInput
    ) -> ProcessInputResponse:
        """
        处理用户输入并执行状态转移
        Args:
            session_id: 会话ID
            user_input: 用户输入
        Returns:
            Dict: 处理结果，包含下一状态信息
        """
        try:
            self.current_input = user_input
            logger.info(f"处理用户输入: {user_input}")
            self.current_session: UserSession = await self.get_session(session_id)
            self.current_context: FlowContext = await self.get_context(session_id)
            current_state = await self.get_current_state(session_id)

            logger.info(
                f"处理用户输入: 会话={session_id}, 当前状态={current_state.name}, 输入类型={user_input.input_type}"
            )

            # 验证用户输入
            if not self.validate_user_input(
                current_state, user_input, self.current_context
            ):
                return ProcessInputResponse(
                    success=False,
                    error="输入处理失败",
                    current_state=current_state,
                )

            # 更新上下文
            await self._update_context_with_input(
                self.current_context, current_state, user_input
            )
            trigger = None
            # 确定下一个状态
            if isinstance(current_state.next, dict):
                if isinstance(user_input.value, str):
                    trigger = f"advance_from_{current_state.name}_on_{current_state.next[user_input.value]}"
                elif isinstance(user_input.value, list):
                    # 不支持多选输入的条件触发
                    if len(user_input.value) != 1:
                        logger.warning("多选输入不支持条件触发，请使用单选或文本输入")
                    trigger = f"advance_from_{current_state.name}_on_{current_state.next[user_input.value[0]]}"
            else:
                trigger = f"advance_{current_state.next}"

            if not trigger or not hasattr(self, trigger):
                raise ValueError(
                    f"Invalid input '{user_input}' for state '{current_state.name}'"
                )
            logger.info(f"trigger before state : {self.state}")  # noqa
            before_state = self.state  # noqa
            getattr(self, trigger)()  # noqa
            logger.info(f"trigger after state : {self.state}")  # noqa
            transition = StateTransition(
                from_state=before_state,
                to_state=self.state,  # noqa
                user_input=user_input.model_dump(),
            )
            await self.add_transition(session_id, transition)
            new_current_state_name: str = self.state  # noqa

            if new_current_state_name in self.flow_config.final_states:
                self.current_session.status = SessionStatus.COMPLETED
                logger.info(f"会话完成: {session_id}")
                # 更新数据库中的会话状态
                await self.session_store.update_session(self.current_session)
                return ProcessInputResponse(
                    success=True,
                    completed=True,
                    current_state=current_state,
                    progress=100,
                    intent_type=IntentType.BUSINESS,
                    message="",
                )
            await self.session_store.update_session(self.current_session)
            await self.context_store.update_context(self.current_context)

            # 获取新状态信息
            new_current_state: StateNode = self.flow_config.states[
                new_current_state_name
            ]
            logger.info(
                f"状态转移成功: {current_state.name} -> {new_current_state_name}"
            )
            next_states = []
            # 可能的下一个状态
            possible_states: Optional[Union[str, Dict[str, str]]] = self.state_map[
                new_current_state_name
            ].next
            if isinstance(possible_states, dict):
                for key, value in possible_states.items():
                    next_states.append(self.state_map[value])
            elif isinstance(possible_states, str):
                next_states.append(self.state_map[possible_states])
            return ProcessInputResponse(
                success=True,
                completed=False,
                current_state=new_current_state,
                next_states=next_states,
                progress=self.get_progress(new_current_state_name),
            )

        except Exception as e:
            logger.exception(f"处理用户输入失败: {e}")
            session = await self.get_session(session_id)  # 确保获取最新会话状态
            session.status = SessionStatus.ERROR
            # 更新数据库中的会话状态
            await self.session_store.update_session(session)
            return ProcessInputResponse(
                success=False,
                error=str(e),
                current_state=self.state_map[session.current_state],
            )

    async def _update_context_with_input(
        self, context: FlowContext, state: StateNode, user_input: UserInput
    ):
        """根据用户输入更新上下文"""
        if state.input_type == InputType.FILE:
            # 处理文件上传
            if isinstance(user_input.value, dict):
                for field_name, field_value in user_input.value.items():
                    context.add_form_data(
                        field_name,
                        field_value,
                        "file",
                        field_name in (state.requires or []),
                    )

            # 处理上传的文件
            if user_input.files:
                for file_path in user_input.files:
                    # 这里应该从实际的文件存储中获取文件信息
                    file_upload = FileUpload(
                        filename=file_path.split("/")[-1],
                        file_path=file_path,
                        file_size=0,  # 实际应用中需要获取真实文件大小
                        content_type="application/octet-stream",  # 实际应用中需要检测文件类型
                    )
                    context.add_uploaded_file(state.name, file_upload)

        elif state.input_type in [InputType.CHOICE, InputType.MULTICHOICE]:
            # 处理选择类型输入
            input_type_str = (
                state.input_type.value
                if hasattr(state.input_type, "value")
                else state.input_type
            )
            context.add_form_data(state.name, user_input.value, input_type_str)

        elif state.input_type == InputType.TEXT:
            # 处理文本输入
            context.add_form_data(state.name, user_input.value, "text")

        # 根据状态名称更新特定的业务信息
        await self._update_business_context(context, state, user_input)

    async def _update_business_context(
        self, context: FlowContext, state: StateNode, user_input: UserInput
    ):
        """更新业务相关的上下文信息"""
        # 根据不同的状态更新相应的业务信息
        if "business_info" in state.name or "company" in state.name:
            if isinstance(user_input.value, dict):
                for key, value in user_input.value.items():
                    context.update_business_info(key, value)

        elif (
            "personal" in state.name
            or "owner" in state.name
            or "director" in state.name
        ):
            if isinstance(user_input.value, dict):
                for key, value in user_input.value.items():
                    context.update_personal_info(key, value)

        elif "license" in state.name or "permit" in state.name:
            if isinstance(user_input.value, dict):
                for key, value in user_input.value.items():
                    context.update_license_info(key, value)

        # 更新数据库中的上下文
        await self.context_store.update_context(context)

    async def _calculate_progress(self, session_id: str) -> float:
        """计算流程进度"""
        context = await self.get_context(session_id)
        total_states = len(self.flow_config.states)
        return context.get_progress_percentage(total_states)

    async def get_session_summary(self, session_id: str) -> SessionSummary:
        """获取会话摘要"""
        session = await self.get_session(session_id)
        progress = await self._calculate_progress(session_id)

        return SessionSummary(
            session_id=session.session_id,
            user_id=session.user_id,
            flow_name=session.flow_name,
            current_state=session.current_state,
            status=session.status,
            progress_percentage=progress,
            created_at=session.created_at,
            updated_at=session.updated_at,
        )

    async def list_sessions(
        self, user_id: Optional[str] = None
    ) -> List[SessionSummary]:
        """列出会话摘要"""
        if self.db_session:
            session_summaries = await self.session_store.list_sessions(user_id)
            # 对于从数据库加载的会话，需要单独计算进度
            for summary in session_summaries:
                summary.progress_percentage = await self._calculate_progress(
                    summary.session_id
                )
            return session_summaries
        else:
            summaries = []
            for session in self.sessions.values():
                if user_id is None or session.user_id == user_id:
                    summary = await self.get_session_summary(session.session_id)
                    summaries.append(summary)

            return sorted(summaries, key=lambda x: x.updated_at, reverse=True)

    async def pause_session(self, session_id: str):
        """暂停会话"""
        session = await self.get_session(session_id)
        session.status = SessionStatus.PAUSED
        session.updated_at = datetime.now()
        logger.info(f"会话已暂停: {session_id}")

        await self.session_store.update_session(session)

    async def resume_session(self, session_id: str):
        """恢复会话"""
        session = await self.get_session(session_id)
        if session.status == SessionStatus.PAUSED:
            session.status = SessionStatus.ACTIVE
            session.updated_at = datetime.now()
            logger.info(f"会话已恢复: {session_id}")
            if self.db_session:
                await self.session_store.update_session(session)

    async def delete_session(self, session_id: str):
        """删除会话"""
        if self.db_session:
            await self.session_store.delete_session(session_id)
        else:
            if session_id in self.sessions:
                del self.sessions[session_id]
            if session_id in self.contexts:
                del self.contexts[session_id]
        logger.info(f"会话已删除: {session_id}")

    def validate_user_input(
        self, state: StateNode, user_input: UserInput, context: FlowContext
    ) -> bool:
        """验证用户输入"""
        try:
            # 基础类型验证
            if not self._validate_input_type(state, user_input):
                return False

            # 必填字段验证
            if not self._validate_required_fields(state, user_input, context):
                return False

            # 选择项验证
            if not self._validate_choices(state, user_input):
                return False
            return True

        except Exception as e:
            logger.error(f"输入验证失败: {e}")
            return False

    def _validate_input_type(self, state: StateNode, user_input: UserInput) -> bool:
        """验证输入类型匹配"""
        # 处理状态的input_type可能是字符串或枚举的情况
        state_input_type = (
            state.input_type.value
            if hasattr(state.input_type, "value")
            else state.input_type
        )
        user_input_type = (
            user_input.input_type.value
            if hasattr(user_input.input_type, "value")
            else user_input.input_type
        )

        if state_input_type != user_input_type:
            raise ValidationError(
                "input_type", f"期望类型 {state_input_type}, 实际类型 {user_input_type}"
            )
        return True

    def _validate_required_fields(
        self, state: StateNode, user_input: UserInput, context: FlowContext
    ) -> bool:
        """验证必填字段"""
        if not state.requires:
            return True

        if state.input_type == InputType.FILE:
            # 文件上传类型的必填字段验证
            if isinstance(user_input.value, dict):
                provided_fields = set(user_input.value.keys())
                required_fields = set(state.requires)
                missing_fields = required_fields - provided_fields

                if missing_fields:
                    raise ValidationError(
                        "required_fields", f"缺少必填字段: {', '.join(missing_fields)}"
                    )

        return True

    def _validate_choices(self, state: StateNode, user_input: UserInput) -> bool:
        """验证选择项"""
        if (
            state.input_type in [InputType.CHOICE, InputType.MULTICHOICE]
            and state.options
        ):
            if state.input_type == InputType.CHOICE:
                if (
                    isinstance(user_input.value, str)
                    and user_input.value not in state.options
                ):
                    raise ValidationError(
                        "choice",
                        f"无效选择 '{user_input.value}', 可选项: {', '.join(state.options)}",
                    )
            elif state.input_type == InputType.MULTICHOICE:
                if isinstance(user_input.value, list):
                    invalid_choices = [
                        choice
                        for choice in user_input.value
                        if choice not in state.options
                    ]
                    if invalid_choices:
                        raise ValidationError(
                            "multichoice",
                            f"无效选择 {invalid_choices}, 可选项: {', '.join(state.options)}",
                        )

        return True

    def get_progress(self, new_current_state_name) -> float:
        """获取会话进度百分比"""
        return (
            self.states.index(new_current_state_name)
            / len(self.flow_config.states)
            * 100
        )
