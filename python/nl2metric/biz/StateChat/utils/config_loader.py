"""
配置加载器
"""

import yaml
from pathlib import Path
from typing import Dict, Any
from loguru import logger

from models.state import FlowConfig, StateNode, InputType


class ConfigLoader:
    """配置文件加载器"""

    @staticmethod
    def load_flow_config(config_path: str) -> FlowConfig:
        """
        从YAML文件加载流程配置

        Args:
            config_path: 配置文件路径

        Returns:
            FlowConfig: 流程配置对象
        """
        try:
            config_file = Path(config_path)
            if not config_file.exists():
                raise FileNotFoundError(f"配置文件不存在: {config_path}")

            with open(config_file, "r", encoding="utf-8") as f:
                config_data = yaml.safe_load(f)

            logger.info(f"加载配置文件: {config_path}")

            # 转换状态配置
            states = {}
            for state_name, state_data in config_data.get("states", {}).items():
                states[state_name] = ConfigLoader._create_state_node(
                    state_name, state_data
                )

            # 创建流程配置对象
            flow_config = FlowConfig(
                flow_name=config_data.get("flow_name", ""),
                description=config_data.get("description", ""),
                version=config_data.get("version", "1.0.0"),
                states=states,
                initial_state=config_data.get("initial_state", ""),
                final_states=config_data.get("final_states", []),
            )

            logger.info(
                f"配置加载完成: {flow_config.flow_name} v{flow_config.version}, 状态数量: {len(states)}"
            )

            return flow_config

        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            raise

    @staticmethod
    def _create_state_node(state_name: str, state_data: Dict[str, Any]) -> StateNode:
        """创建状态节点对象"""
        # 处理输入类型
        input_type_str = state_data.get("input_type", "text")
        try:
            input_type = InputType(input_type_str)
        except ValueError:
            logger.warning(
                f"未知的输入类型 '{input_type_str}' 在状态 '{state_name}' 中，使用默认类型 'text'"
            )
            input_type = InputType.TEXT

        return StateNode(
            name=state_data.get("name", state_name),
            prompt=state_data.get("prompt", ""),
            input_type=input_type,
            options=state_data.get("options"),
            requires=state_data.get("requires"),
            next=state_data.get("next"),
        )

    @staticmethod
    def validate_config_file(config_path: str) -> Dict[str, Any]:
        """
        验证配置文件的有效性

        Args:
            config_path: 配置文件路径

        Returns:
            Dict: 验证结果
        """
        validation_result = {"valid": True, "errors": [], "warnings": []}

        try:
            config_file = Path(config_path)
            if not config_file.exists():
                validation_result["valid"] = False
                validation_result["errors"].append(f"配置文件不存在: {config_path}")
                return validation_result

            with open(config_file, "r", encoding="utf-8") as f:
                config_data = yaml.safe_load(f)

            # 检查必需字段
            required_fields = ["flow_name", "states", "initial_state", "final_states"]
            for field in required_fields:
                if field not in config_data:
                    validation_result["valid"] = False
                    validation_result["errors"].append(f"缺少必需字段: {field}")

            # 检查状态配置
            states = config_data.get("states", {})
            if not states:
                validation_result["valid"] = False
                validation_result["errors"].append("状态配置为空")

            # 检查初始状态
            initial_state = config_data.get("initial_state")
            if initial_state and initial_state not in states:
                validation_result["valid"] = False
                validation_result["errors"].append(
                    f"初始状态 '{initial_state}' 不存在于状态列表中"
                )

            # 检查终止状态
            final_states = config_data.get("final_states", [])
            for final_state in final_states:
                if final_state not in states:
                    validation_result["valid"] = False
                    validation_result["errors"].append(
                        f"终止状态 '{final_state}' 不存在于状态列表中"
                    )

            # 检查状态转移
            for state_name, state_data in states.items():
                ConfigLoader._validate_state_config(
                    state_name, state_data, states, validation_result
                )

        except yaml.YAMLError as e:
            validation_result["valid"] = False
            validation_result["errors"].append(f"YAML格式错误: {e}")
        except Exception as e:
            validation_result["valid"] = False
            validation_result["errors"].append(f"验证过程中发生错误: {e}")

        return validation_result

    @staticmethod
    def _validate_state_config(
        state_name: str,
        state_data: Dict[str, Any],
        all_states: Dict[str, Any],
        validation_result: Dict[str, Any],
    ):
        """验证单个状态配置"""
        # 检查必需字段
        required_state_fields = ["name", "prompt", "input_type"]
        for field in required_state_fields:
            if field not in state_data:
                validation_result["errors"].append(
                    f"状态 '{state_name}' 缺少必需字段: {field}"
                )

        # 检查输入类型
        input_type = state_data.get("input_type")
        valid_input_types = [t.value for t in InputType]
        if input_type and input_type not in valid_input_types:
            validation_result["warnings"].append(
                f"状态 '{state_name}' 使用了未知的输入类型: {input_type}"
            )

        # 检查选择项
        if input_type in ["choice", "multichoice"]:
            options = state_data.get("options")
            if not options or not isinstance(options, list):
                validation_result["errors"].append(
                    f"状态 '{state_name}' 的选择类型必须提供选项列表"
                )

        # 检查下一状态
        next_state = state_data.get("next")
        if next_state:
            if isinstance(next_state, str):
                if next_state not in all_states:
                    validation_result["errors"].append(
                        f"状态 '{state_name}' 的下一状态 '{next_state}' 不存在"
                    )
            elif isinstance(next_state, dict):
                for condition, target_state in next_state.items():
                    if target_state and target_state not in all_states:
                        validation_result["errors"].append(
                            f"状态 '{state_name}' 的条件 '{condition}' 对应的目标状态 '{target_state}' 不存在"
                        )

    @staticmethod
    def save_flow_config(flow_config: FlowConfig, config_path: str):
        """
        保存流程配置到YAML文件

        Args:
            flow_config: 流程配置对象
            config_path: 保存路径
        """
        try:
            # 转换为字典格式
            config_data = {
                "flow_name": flow_config.flow_name,
                "description": flow_config.description,
                "version": flow_config.version,
                "initial_state": flow_config.initial_state,
                "final_states": flow_config.final_states,
                "states": {},
            }

            # 转换状态配置
            for state_name, state_node in flow_config.states.items():
                state_data = {
                    "name": state_node.name,
                    "prompt": state_node.prompt,
                    "input_type": state_node.input_type.value,
                }

                if state_node.options:
                    state_data["options"] = state_node.options

                if state_node.requires:
                    state_data["requires"] = state_node.requires

                if state_node.next:
                    state_data["next"] = state_node.next

                config_data["states"][state_name] = state_data

            # 保存到文件
            config_file = Path(config_path)
            config_file.parent.mkdir(parents=True, exist_ok=True)

            with open(config_file, "w", encoding="utf-8") as f:
                yaml.dump(
                    config_data,
                    f,
                    default_flow_style=False,
                    allow_unicode=True,
                    indent=2,
                )

            logger.info(f"配置文件已保存: {config_path}")

        except Exception as e:
            logger.error(f"保存配置文件失败: {e}")
            raise
