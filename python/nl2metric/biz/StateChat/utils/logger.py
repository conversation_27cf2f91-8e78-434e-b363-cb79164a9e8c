"""
日志工具配置
"""

import sys
from pathlib import Path
from loguru import logger


def setup_logger(log_level: str = "INFO", log_file: str = None):
    """
    配置日志系统

    Args:
        log_level: 日志级别
        log_file: 日志文件路径，可选
    """
    # 移除默认的日志处理器
    logger.remove()

    # 添加控制台输出
    logger.add(
        sys.stdout,
        level=log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        colorize=True,
    )

    # 添加文件输出（如果指定了文件路径）
    if log_file:
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)

        logger.add(
            log_file,
            level=log_level,
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
            rotation="10 MB",  # 日志文件大小达到10MB时轮转
            retention="30 days",  # 保留30天的日志
            compression="zip",  # 压缩旧日志文件
        )

    logger.info(f"日志系统初始化完成，级别: {log_level}")


def get_logger(name: str = None):
    """
    获取日志记录器

    Args:
        name: 日志记录器名称

    Returns:
        logger: 日志记录器实例
    """
    if name:
        return logger.bind(name=name)
    return logger


# 默认日志配置
setup_logger()
