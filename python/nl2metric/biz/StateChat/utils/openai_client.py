"""
基于LangChain的OpenAI客户端
"""

import os
from pathlib import Path
from typing import Dict, Any, Optional, AsyncGenerator
import yaml
from loguru import logger

from langchain_openai import ChatOpenAI
from langchain.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser


class LangChainClient:
    """基于LangChain的OpenAI客户端"""

    def __init__(self):
        project_root = Path(__file__).parent.parent
        config_path = project_root / "config" / "openai_config.yaml"
        self.config = self._load_config(str(config_path))

        # 设置OpenAI环境变量
        os.environ["OPENAI_API_KEY"] = (
            os.getenv("OPENAI_API_KEY") or self.config["api"]["api_key"]
        )
        if "base_url" in self.config["api"]:
            os.environ["OPENAI_API_BASE"] = self.config["api"]["base_url"]
        if "organization" in self.config["api"]:
            os.environ["OPENAI_ORGANIZATION"] = self.config["api"]["organization"]

        # 初始化聊天模型
        self.chat_model = ChatOpenAI(
            model=self.config["models"]["chat"]["name"],
            temperature=self.config["models"]["chat"]["temperature"],
            max_tokens=self.config["models"]["chat"]["max_tokens"],
            streaming=True,  # 启用流式
            extra_body=self.config["models"]["chat"]["extra_body"],
        )

        # 初始化意图识别模型 (意图识别通常不需要流式，但为了统一接口，也可以设置为流式)
        self.intent_model = ChatOpenAI(
            model=self.config["models"]["intent"]["name"],
            temperature=self.config["models"]["intent"]["temperature"],
            max_tokens=self.config["models"]["intent"]["max_tokens"],
            extra_body=self.config["models"]["intent"]["extra_body"],
        )

        # 创建意图识别Chain
        self.intent_prompt = ChatPromptTemplate.from_template("""
{system_prompt}

用户输入: {text}
意图类型:""")
        self.intent_chain = self.intent_prompt | self.intent_model | StrOutputParser()

        # 创建聊天Chain
        self.chat_prompt = ChatPromptTemplate.from_template("""
{system_prompt}

用户: {text}
助手:""")
        self.chat_chain = self.chat_prompt | self.chat_model | StrOutputParser()

    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        with open(config_path, "r", encoding="utf-8") as f:
            config = yaml.safe_load(f)
        return config

    async def get_intent(self, text: str) -> str:
        """获取意图分类 (非流式，因为意图通常是单次判断)"""
        try:
            result = await self.intent_chain.ainvoke(
                {
                    "text": text,
                    "system_prompt": self.config["models"]["intent"]["system_prompt"],
                }
            )
            logger.info(f"意图识别完成: {text} -> {result}")

            return result.strip().lower()

        except Exception as e:
            logger.error(f"意图识别调用失败: {e}")
            raise

    async def get_chat_response_stream(
        self,
        text: str,
        system_prompt: Optional[str] = None,
        chat_config: Optional[Dict[str, Any]] = None,
    ) -> AsyncGenerator[str, None]:
        """获取聊天回复 (流式)"""
        try:
            chain = self.chat_chain
            if system_prompt or chat_config:
                # 如果提供了自定义配置，创建新的Chain
                model_config = chat_config or self.config["models"]["chat"]
                custom_model = ChatOpenAI(
                    model=model_config["name"],
                    temperature=model_config["temperature"],
                    max_tokens=model_config["max_tokens"],
                    streaming=True,  # 启用流式
                )
                custom_prompt = ChatPromptTemplate.from_template("""
{system_prompt}

用户: {text}
助手:""")
                chain = custom_prompt | custom_model | StrOutputParser()

            async for chunk in chain.astream(
                {
                    "text": text,
                    "system_prompt": system_prompt
                    or self.config["models"]["chat"]["system_prompt"],
                }
            ):
                logger.debug(f"流式回复块: {chunk}")
                yield chunk

            logger.info(f"聊天回复流式生成完成: {text}")

        except Exception as e:
            logger.error(f"聊天流式调用失败: {e}")
            raise


# 创建默认客户端实例
default_openai_client = LangChainClient()
