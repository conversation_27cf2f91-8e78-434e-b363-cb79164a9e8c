import asyncio
from typing import List

from fastapi import Request
from common.utils.llm_utils import create_llm_model_by_project_config
from config.project_config import ProjectConfig
from langchain_core.messages import HumanMessage

from nl2document.index.chains.query_document import (
    aget_nodes_by_file_id,
    restore_nodes_to_document,
)
from nl2document.index.service.service import DocumentIndexService
from nl2document.common.msg.docservice import SearchTagRequest
from nl2document.tag.tag_manager import TagManager


MAX_TOKENS_PER_REQUEST = 32768
# Set max_tokens for completion to a smaller value, e.g., 2048
COMPLETION_MAX_TOKENS = 2048
# Leave space for prompt overhead (e.g., "总结以下文档内容：\n") and completion tokens
TARGET_CONTENT_TOKENS = (
    MAX_TOKENS_PER_REQUEST - COMPLETION_MAX_TOKENS - 500
)  # 500 for prompt overhead and safety


async def summary_with_query(file_ids: list[str], query_str: str):
    all_document_summaries = []  # This will store summaries for each individual document

    yield "\n--- 第一轮总结：每篇文档单独总结 ---\n"
    for file_id in file_ids:
        nodes = await aget_nodes_by_file_id(file_id)
        restored_documents_for_id = restore_nodes_to_document(nodes)

        document_parts_summaries = []
        current_batch_docs = []
        current_batch_length = 0

        for doc_content in restored_documents_for_id:
            doc_length = len(doc_content)  # doc_content is already a string

            if (
                current_batch_length + doc_length > TARGET_CONTENT_TOKENS
                and current_batch_docs
            ):
                batch_content = "\n".join(current_batch_docs)
                prompt = f"根据以下文档内容，回答问题：'{query_str}'。\n文档内容：\n{batch_content}"
                summary = await call_vllm_for_summary(prompt)
                document_parts_summaries.append(summary)
                current_batch_docs = []
                current_batch_length = 0

            current_batch_docs.append(doc_content)
            current_batch_length += doc_length

        if current_batch_docs:
            batch_content = "\n".join(current_batch_docs)
            prompt = f"根据以下文档内容，回答问题：'{query_str}'。\n文档内容：\n{batch_content}"
            summary = await call_vllm_for_summary(prompt)
            document_parts_summaries.append(summary)

        # Combine summaries for the current document if it was split into parts
        if document_parts_summaries:
            combined_doc_summary = "\n".join(document_parts_summaries)
            all_document_summaries.append(
                f"文档 ID {file_id} 总结:\n{combined_doc_summary}\n"
            )
        else:
            all_document_summaries.append(f"文档 ID {file_id} 没有内容可总结。\n")

    for s in all_document_summaries:
        yield s
    yield "\n"

    # --- 第二轮总结：根据上一轮的总结，再做一次分类并再次总结 ---
    if all_document_summaries:
        combined_first_round_summaries = "\n".join(all_document_summaries)
        second_round_prompt = f"请根据以下多篇文档的总结内容进行分类，并再次进行总结：\n{combined_first_round_summaries}"

        yield "\n--- 第二轮总结 (分类和再次总结) ---\n"
        # For the second round, if combined_first_round_summaries is very large,
        # it might also need to be batched. For now, we assume it fits.
        async for token in stream_vllm_for_summary(second_round_prompt):
            yield token
        yield "\n"
    else:
        yield "\n--- 没有第一轮总结内容，跳过第二轮总结 ---\n"


async def call_vllm_for_summary(prompt_content: str) -> str:
    """
    Calls the VLLM for summary using GeneralLLM and returns the full response content.
    This function is used for cases where a complete summary is needed (e.g., for combining).
    """
    project_config = ProjectConfig("mock", "mock")
    llm_instance = create_llm_model_by_project_config(
        "base_model_nothink", project_config
    )

    try:
        response = await llm_instance.ainvoke(prompt_content)
        return response.content
    except Exception as e:
        print(f"Error calling LLM for summary: {e}")
        return f"Error: LLM call failed - {e}"


async def stream_vllm_for_summary(prompt_content: str):
    """
    Calls the VLLM for summary using GeneralLLM and yields content chunks for streaming output.
    """
    project_config = ProjectConfig("mock", "mock")
    llm_instance = create_llm_model_by_project_config(
        "base_model_nothink", project_config
    )

    try:
        messages = [HumanMessage(content=prompt_content)]
        async for chat_response in llm_instance.astream(messages):
            yield chat_response.content
    except Exception as e:
        yield f"Error streaming LLM for summary: {e}"


"""
{'code': 0, 'msg': 'ok', 'data': [{'id': '444d3b4c-2949-481d-b71e-5d75179f164a', 'name': 'fintech innovation technology -> regulatory frameworks technology', 'synonyms': [], 'file_id': '1681'}, {'id': '35bcc577-6712-4ca7-bda6-4288ea25666b', 'name': 'fintech innovation technology -> regulatory frameworks technology', 'synonyms': [], 'file_id': '1642'}, {'id': '134667cc-2a81-44d2-b960-536d46a673a9', 'name': 'fintech innovation technology -> regulatory frameworks technology', 'synonyms': [], 'file_id': '1644'}, {'id': '07e1472c-0d54-49dc-ab16-43c8ced4b579', 'name': 'fintech innovation technology -> regulatory frameworks technology', 'synonyms': [], 'file_id': '1651'}, {'id': '0719e8f3-17b9-4c6d-bf26-98843980ebeb', 'name': 'fintech innovation technology -> regulatory frameworks technology', 'synonyms': [], 'file_id': '1764'}]}
"""


async def search_tag_with_query(query: str, top_k: int = 5) -> List[str]:
    """
    Searches for tags based on a query string.
    """
    document_index_service = DocumentIndexService()
    tags_response = await document_index_service.search_tag(
        req=SearchTagRequest(query=query, top_k=top_k),
        request=Request(
            scope={
                "type": "http",
                "asgi": {
                    "version": "3.0",
                    "spec_version": "2.0",
                },
                "headers": [("Traceid", "1234567890")],
            },
        ),
    )
    if tags_response["code"] != 0:
        print(f"Error searching tags: {tags_response.msg}")
        return []
    return list(set([tag["name"] for tag in tags_response["data"]]))




async def get_file_ids_by_tag(tag_names: list[str]) -> List[str]:
    """
    Gets file IDs associated with a specific tag.
    """
    tag_manager = TagManager()
    return tag_manager.get_file_ids_by_tag_name(tag_names)
