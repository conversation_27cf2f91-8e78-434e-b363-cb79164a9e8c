import asyncio
import uvicorn
from fastapi import FastAPI, Request
from fastapi.responses import HTMLResponse, StreamingResponse
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel

from scripts.doc.ask_bot.get_nodes_by_file_id import (
    search_tag_with_query,
    get_file_ids_by_tag,
    summary_with_query,
)

app = FastAPI()

app.mount("/web", StaticFiles(directory="python/nl2metric/scripts/doc/ask_bot/web"), name="web")


class AskRequest(BaseModel):
    query: str


@app.get("/")
async def read_root():
    with open("python/nl2metric/scripts/doc/ask_bot/web/index.html", "r", encoding="utf-8") as f:
        html_content = f.read()
    return HTMLResponse(content=html_content, status_code=200)


async def stream_response(query: str):
    if query == "人工智能对金融的赋能":
        with open(
            "/Users/<USER>/github_project/ask-bi/python/nl2metric/scripts/doc/ask_bot/mock/q_1_new_after_replace.md",
            "r",
            encoding="utf-8",
        ) as f:
            yield f.read()
        return

    if query == "金融监管相关政策与方法":
        with open(
            "/Users/<USER>/github_project/ask-bi/python/nl2metric/scripts/doc/ask_bot/mock/q_2_new_after_replace.md",
            "r",
            encoding="utf-8",
        ) as f:
            yield f.read()
        return

    tags = await search_tag_with_query(query)
    # yield f"Tags found for query: {tags}\n"
    file_ids = await get_file_ids_by_tag(tags)
    # yield f"File IDs for tags {tags}: {file_ids}\n"
    if file_ids:
        async for chunk in summary_with_query(file_ids, query):
            yield chunk
    else:
        yield "No file IDs found for the given tags."


@app.post("/ask")
async def ask(request: AskRequest):
    return StreamingResponse(
        stream_response(request.query), media_type="text/markdown; charset=utf-8"
    )


if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
