<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ask Bot - AI文档问答助手</title>
    <!-- 引入marked.js用于markdown渲染 -->
    <script src="https://cdn.jsdelivr.net/npm/marked@11.1.1/marked.min.js"></script>
    <!-- 引入highlight.js用于代码高亮 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/github.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Aria<PERSON>, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .input-section {
            padding: 30px;
            border-bottom: 1px solid #e9ecef;
        }

        .input-group {
            display: flex;
            gap: 15px;
            align-items: stretch;
        }

        #query {
            flex: 1;
            padding: 15px 20px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        #query:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        #askButton {
            padding: 15px 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            min-width: 120px;
        }

        #askButton:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
        }

        #askButton:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .result-section {
            padding: 30px;
            min-height: 200px;
        }

        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 40px;
            color: #6c757d;
        }

        .loading-spinner {
            width: 20px;
            height: 20px;
            border: 2px solid #e9ecef;
            border-top: 2px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        #result {
            line-height: 1.8;
            font-size: 16px;
        }

        /* Markdown样式 */
        #result h1, #result h2, #result h3, #result h4, #result h5, #result h6 {
            margin: 1.5em 0 0.5em 0;
            font-weight: 600;
            line-height: 1.3;
        }

        #result h1 {
            font-size: 2.2rem;
            color: #2c3e50;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
        }

        #result h2 {
            font-size: 1.8rem;
            color: #34495e;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 8px;
        }

        #result h3 {
            font-size: 1.4rem;
            color: #34495e;
        }

        #result p {
            margin: 1em 0;
            text-align: justify;
        }

        #result ul, #result ol {
            margin: 1em 0;
            padding-left: 2em;
        }

        #result li {
            margin: 0.5em 0;
        }

        #result blockquote {
            margin: 1.5em 0;
            padding: 1em 1.5em;
            background: #f8f9fa;
            border-left: 4px solid #667eea;
            border-radius: 0 8px 8px 0;
            font-style: italic;
        }

        #result code {
            background: #f1f3f4;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.9em;
        }

        #result pre {
            background: #f8f9fa;
            padding: 1.5em;
            border-radius: 8px;
            overflow-x: auto;
            margin: 1.5em 0;
            border: 1px solid #e9ecef;
        }

        #result pre code {
            background: none;
            padding: 0;
        }

        #result table {
            width: 100%;
            border-collapse: collapse;
            margin: 1.5em 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        #result th, #result td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }

        #result th {
            background: #667eea;
            color: white;
            font-weight: 600;
        }

        #result tr:hover {
            background: #f8f9fa;
        }

        #result a {
            color: #667eea;
            text-decoration: none;
            border-bottom: 1px solid transparent;
            transition: border-color 0.3s ease;
        }

        #result a:hover {
            border-bottom-color: #667eea;
        }

        #result img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            margin: 1em 0;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .input-group {
                flex-direction: column;
            }

            .input-section, .result-section {
                padding: 20px;
            }

            #result h1 {
                font-size: 1.8rem;
            }

            #result h2 {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Ask Bot</h1>
            <p>AI文档问答助手 - 基于人工智能的智能文档检索与分析</p>
        </div>

        <div class="input-section">
            <div class="input-group">
                <input type="text" id="query" placeholder="请输入您的问题，例如：人工智能对金融的赋能..." />
                <button id="askButton" onclick="ask()">提问</button>
            </div>
        </div>

        <div class="result-section">
            <div id="result"></div>
        </div>
    </div>

    <script>
        // 配置marked.js
        marked.setOptions({
            highlight: function(code, lang) {
                if (lang && hljs.getLanguage(lang)) {
                    try {
                        return hljs.highlight(code, { language: lang }).value;
                    } catch (err) {}
                }
                return hljs.highlightAuto(code).value;
            },
            breaks: true,
            gfm: true
        });

        let isAsking = false;
        let currentMarkdownContent = '';

        async function ask() {
            if (isAsking) return;

            const query = document.getElementById('query').value.trim();
            if (!query) {
                alert('请输入问题');
                return;
            }

            const resultDiv = document.getElementById('result');
            const askButton = document.getElementById('askButton');

            // 设置加载状态
            isAsking = true;
            askButton.disabled = true;
            askButton.textContent = '思考中...';
            resultDiv.innerHTML = '<div class="loading"><div class="loading-spinner"></div>正在分析您的问题，请稍候...</div>';
            currentMarkdownContent = '';

            try {
                const response = await fetch('/ask', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ query })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                resultDiv.innerHTML = '';

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) {
                        break;
                    }

                    // 累积markdown内容
                    currentMarkdownContent += decoder.decode(value, { stream: true });

                    // 实时渲染markdown
                    try {
                        const htmlContent = marked.parse(currentMarkdownContent);
                        resultDiv.innerHTML = htmlContent;

                        // 重新高亮代码块
                        resultDiv.querySelectorAll('pre code').forEach((block) => {
                            hljs.highlightElement(block);
                        });

                        // 滚动到底部以显示最新内容
                        resultDiv.scrollTop = resultDiv.scrollHeight;
                    } catch (markdownError) {
                        console.warn('Markdown parsing error:', markdownError);
                        // 如果markdown解析失败，显示原始文本
                        resultDiv.textContent = currentMarkdownContent;
                    }
                }
            } catch (error) {
                console.error('Error:', error);
                resultDiv.innerHTML = `<div style="color: #dc3545; padding: 20px; text-align: center;">
                    <h3>请求失败</h3>
                    <p>抱歉，处理您的请求时出现了错误。请稍后重试。</p>
                    <p style="font-size: 0.9em; opacity: 0.7;">错误信息: ${error.message}</p>
                </div>`;
            } finally {
                // 恢复按钮状态
                isAsking = false;
                askButton.disabled = false;
                askButton.textContent = '提问';
            }
        }

        // 支持回车键提交
        document.getElementById('query').addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !isAsking) {
                ask();
            }
        });

        // 页面加载完成后聚焦输入框
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('query').focus();
        });
    </script>
</body>
</html>