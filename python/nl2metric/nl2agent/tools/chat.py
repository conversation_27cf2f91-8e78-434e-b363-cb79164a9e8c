import json
from copy import deepcopy

import yaml
from langchain_core.messages import ChatMessage
from langchain.chains.llm import <PERSON><PERSON>hain
from langchain.docstore.document import Document
from langchain.prompts import PromptTemplate
import tiktoken
from concurrent.futures import ThreadPoolExecutor

from common.utils.llm_utils import create_llm_model_by_project_config
from config.project_config import get_project_config
from nl2agent.tools.base_tool import BaseTool, register_tool
from typing import Type, Dict
from pydantic import BaseModel, Field
from langchain_core.runnables import RunnableConfig, RunnableLambda, RunnableParallel
from langchain_core.output_parsers import StrOutputParser
from common.types.base import (
    CHAIN_META,
    ChainMeta,
    ChainRuntime,
    ParamsExtractStage,
)
from nl2agent.common.agent_reporter import reporter_run_chain
from typing import Optional, List

from config import doc_config
from tools.doc_tool import RetrieverDocument
from tools.web_tools import WebSearcherTool


# ATTATION: chat工具的入参是框架拼进去的，模型不感知
# 所以args_schema不能有入参，_run需要入参
# 直接使用上面的ChatInput会导致参数校验失败，所以args_schema是None
# 但是不加args_schema的话convert_to_openai_function又会出错
# 所有这里只能分成两个类了


class ChatInputOnlyForDisplay(BaseModel):
    pass


rules = {
    "year:": "年份:",
    "document_number:": "文号:",
    "sending_unit:": "来文单位:",
    "receipt_date:": "收文时间:",
    "title:": "文章标题:",
    "content:": "内容:",
    "partName:": "章节标题:",
    "file_theme:": "文件主题:",
}


def replace_doc_chunk(doc_content: str, rules: dict[str:str]):
    for old, new in rules.items():
        doc_content = doc_content.replace(old, new)
    return doc_content


# map reduce chain 太慢了，感觉我们的场景更适合这种简单的总结提取，测试下看看
def _summarize_long_doc(
    doc_content: str, node_idx_dict: List[dict], llm, question: str
) -> str:
    """
    Summarizes a long document using map-reduce based on the user's question.
    """
    tokenizer = tiktoken.get_encoding("cl100k_base")

    # Question-aware prompts
    map_template = """
    请根据以下文本，提取并总结与问题“{question}”相关的信息：

    文本：
    {text}

    相关的总结：
    """
    map_prompt = PromptTemplate.from_template(map_template)

    # Group nodes into chunks
    docs = []
    current_chunk_nodes = []
    DOC_CONTENT_TOKEN_LIMIT = doc_config.doc_query_document_context_window
    CHUNK_TOKEN_LIMIT = (
        DOC_CONTENT_TOKEN_LIMIT / 2
    )  # Set a reasonable chunk size for the map step

    for node in node_idx_dict:
        temp_chunk_for_check = current_chunk_nodes + [node]
        temp_content_for_check = yaml.dump(
            temp_chunk_for_check, indent=2, allow_unicode=True, sort_keys=False
        )

        if (
            len(tokenizer.encode(temp_content_for_check)) > CHUNK_TOKEN_LIMIT
            and current_chunk_nodes
        ):
            chunk_content = yaml.dump(
                current_chunk_nodes, indent=2, allow_unicode=True, sort_keys=False
            )
            docs.append(Document(page_content=replace_doc_chunk(chunk_content, rules)))
            current_chunk_nodes = [node]
        else:
            current_chunk_nodes.append(node)

    if current_chunk_nodes:
        chunk_content = yaml.dump(
            current_chunk_nodes, indent=2, allow_unicode=True, sort_keys=False
        )
        docs.append(Document(page_content=replace_doc_chunk(chunk_content, rules)))

    if not docs and doc_content:
        docs.append(Document(page_content=doc_content))

    # Create a chain that just runs the map step
    map_chain = LLMChain(llm=llm, prompt=map_prompt)

    # Prepare inputs for each document
    map_inputs = [{"text": doc.page_content, "question": question} for doc in docs]

    # Run the map chain for all documents in parallel using a thread pool
    with ThreadPoolExecutor(max_workers=5) as executor:
        # Use map_chain.invoke since we are iterating
        map_results = list(executor.map(map_chain.invoke, map_inputs))

    # Concatenate the results from the map step
    summaries = [result["text"] for result in map_results]
    return "\n\n".join(summaries)


def chat_preprocess(input: dict, config: RunnableConfig):
    history = config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.HISTORY_MESSAGES]
    if not history:
        history = []
    prompt_selector = config[CHAIN_META][ChainMeta.PROMPT_SELECTOR]
    messages = config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.MESSAGES_RECORD]
    extra_info = [
        m.content for m in messages if (isinstance(m, ChatMessage) and m.role == "tool")
    ]
    doc_search = input["doc_search"]
    web_search = input["web_search"]
    input["doc_content"] = ""
    is_meta_question_type = False
    if doc_search:
        text_nodes: List[dict] = (
            doc_search.get("data", {}).get("sourceNodes", {}).get("textNodes", [])
        )
        is_meta_question_type = (
            text_nodes[-1]["content"] == "问答类型：元数据问答" if text_nodes else False
        )
        if is_meta_question_type:
            text_nodes = text_nodes[:-1]  # Remove the last node which is meta info
            last_node = text_nodes[-1] if text_nodes else {}
            text_nodes = text_nodes[:50] + [
                last_node
            ]  # Limit to first 50 nodes for meta questions
        nodes = deepcopy(text_nodes)
        node_idx_dict = []
        for idx, node in enumerate(nodes):
            # index 放到 text_node 中开头
            node.pop("chapter_title", "")
            node.pop("columnIndex", "")
            node.pop("folderId", "")

            metadata = node.pop("metadata", {})
            if metadata:
                metadata.pop("file_parent_list", "")
                metadata.pop("sort_id", "")
                metadata.pop("conference_content_type", "")

            data_dict = {"index": idx}
            for key, v in {**metadata, **node}.items():
                if v:
                    data_dict[key] = v
            node_idx_dict.append(data_dict)

        doc_content = yaml.dump(
            node_idx_dict,
            indent=2,
            allow_unicode=True,
            default_flow_style=False,
            explicit_start=False,
            explicit_end=False,
            sort_keys=False,
        )
        doc_content = replace_doc_chunk(doc_content, rules)

        tokenizer = tiktoken.get_encoding("cl100k_base")
        token_count = len(tokenizer.encode(doc_content))
        DOC_CONTENT_TOKEN_LIMIT = doc_config.doc_query_document_context_window
        if token_count > DOC_CONTENT_TOKEN_LIMIT:
            project_config = get_project_config(
                project_name=config[CHAIN_META][ChainMeta.PROJECT_NAME],
                model_name=config[CHAIN_META][ChainMeta.MODEL_NAME],
            )
            llm = create_llm_model_by_project_config(
                "base_model_nothink", project_config
            )
            question = config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.QUESTION]
            doc_content = _summarize_long_doc(doc_content, node_idx_dict, llm, question)

        input["doc_content"] = doc_content
    stage = ParamsExtractStage.AGENT_CHAT
    if doc_search or web_search:
        stage = ParamsExtractStage.AGENT_DOC_CHAT
        if is_meta_question_type:
            stage = ParamsExtractStage.AGENT_DOC_META_CHAT
    prompt = prompt_selector.gen_prompt(
        input={
            "question": config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.QUESTION],
            "history": history,
            "extra_info": extra_info,
            **config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.HINT],
            **input,
        },
        stage=stage,
        config=config,
    )
    return {"prompt": prompt, **input}


class ChatToolOnlyForDisplay(BaseTool):
    name: str = "chat"
    description: str = "全能工具。能力范围：除针对环境中数据查询和处理外的所有问题"
    args_schema: Type[BaseModel] = ChatInputOnlyForDisplay

    def _run(self):
        raise NotImplementedError()


def process_message(inputs: Dict[str, str], config: RunnableConfig):
    project_config = get_project_config(
        project_name=config[CHAIN_META][ChainMeta.PROJECT_NAME],
        model_name=config[CHAIN_META][ChainMeta.MODEL_NAME],
    )
    model_type = (
        "base_model_nothink"
        if inputs.get("think", "false") == "false"
        else "base_model"
    )
    llm = create_llm_model_by_project_config(model_type, project_config)
    chains = (
        RunnableLambda(lambda input: input.get("prompt", "")) | llm | StrOutputParser()
    )

    if inputs.get("doc_search", ""):
        yield json.dumps(inputs.get("doc_search", ""), ensure_ascii=False, indent=2)
    yield "======sourceNodes=======:\n"
    yield "\n\n"

    # 添加缓冲区处理小chunk和跨chunk标签
    buffer = ""
    is_add = True

    for chunk in chains.stream(inputs, config):
        # 将当前chunk添加到缓冲区
        if is_add:
            buffer += chunk

        if len(buffer) > 10:
            if "<think>" not in buffer:
                yield "<think></think>"
            yield buffer
            is_add = False
            buffer = ""
            continue

        if not is_add:
            yield chunk

    if buffer:
        if "<think>" not in buffer:
            yield "<think></think>"
        yield buffer


class Input(BaseModel):
    think: str = Field(
        description="是否需要思考能力。如果需要深度推理或综合分析多个信息源，为true。如果只有单个信息源，不需要思考能力，那么为false"
    )


@register_tool(name="chat")
class ChatTool(BaseTool):
    name: str = "chat"
    description: str = "全能工具。能力范围：除针对环境中数据查询和处理外的所有问题。chat 工具与其他工具互斥，不可同时使用。"
    args_schema: Type[BaseModel] = Input
    breadcrumbs: Optional[List[str]] = None

    def _run(self, think, config: RunnableConfig):
        # run_time
        rt_enable_doc_search = config[CHAIN_META][ChainMeta.RUN_TIME].get(
            ChainMeta.ENABLE_DOC_SEARCH, True
        )
        rt_enable_internet_search = config[CHAIN_META][ChainMeta.RUN_TIME].get(
            ChainMeta.ENABLE_INTERNET_SEARCH, rt_enable_doc_search
        )
        # config
        enable_internet_search = config[CHAIN_META][ChainMeta.ENABLE_INTERNET_SEARCH]
        enable_doc_search = config[CHAIN_META][ChainMeta.ENABLE_DOC_SEARCH]
        query = config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.QUESTION]

        action = {
            "web_search": WebSearcherTool()
            if enable_internet_search and rt_enable_internet_search
            else lambda _: "",
            "doc_search": RetrieverDocument()
            if enable_doc_search and rt_enable_doc_search
            else lambda _: "",
            "think": lambda _: think,
        }
        chain = (
            RunnableParallel(action)
            | RunnableLambda(chat_preprocess, name="chat_preprocess")
            | RunnableLambda(process_message, name="llm_chat_and_doc_resource_node")
            | StrOutputParser()
        )
        chain.name = "chat"
        return reporter_run_chain(
            breadcrumbs=self.breadcrumbs,
            chain=chain,
            input={"query": query, "think": think},
            config=config,
            name=self.name,
            # chat没有后续步骤，
            # 所以大模型输出结束后直接上报succeed
            code=0,
        )
