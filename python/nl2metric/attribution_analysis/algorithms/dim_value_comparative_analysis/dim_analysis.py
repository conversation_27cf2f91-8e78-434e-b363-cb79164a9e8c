import concurrent.futures
import os

import pandas as pd

from attribution_analysis.algorithms.dim_value_comparative_analysis.utils import (
    cal_ele_contribution_abs,
    js_convergence,
    query_dim_values_diff,
    schema_link_extraction,
)


class Node:
    def __init__(self, label, value, desc, item):
        self.nodeLabel = label
        self.nodeValue = value
        self.nodeDesc = desc
        self.items = item
        self.children = []


def filterExtension(path, filter_intr):
    where_list = []
    for i in path:
        if isinstance(i[1], str):
            where_list.append(f"({i[0]}='{i[1]}')")
        else:
            where_list.append(f"({i[0]}={i[1]})")

    if filter_intr:
        where = filter_intr + " AND "
        where += " AND ".join(where_list)
    else:
        where = " AND ".join(where_list)

    return where


def dimension_analysis_process(
    dimension_name,
    dimension_dict,
    metric_where,
    metric_dict,
    date,
    topn,
    change,
    has_change,
    model_id,
):
    params_query = {
        "metric_where": metric_where,
        "groupbys": [dimension_name],
        "time": date,
        "metric_dict": metric_dict,
    }
    df, _ = query_dim_values_diff(params_query, model_id)
    if len(df) == 0:
        return
    # consider only one target metric, if there are multiple metrics, use metric_dict
    js = js_convergence(df, metric_where[0]["metric"], dimension_name)
    dimension_value_change = cal_ele_contribution_abs(
        df, dimension_name, metric_where[0]["metric"]
    )
    topn_elements = list(map(str, dimension_value_change["element"][:topn]))
    topn_change = list(map(str, dimension_value_change["change"][:topn]))
    topn_base = list(map(str, dimension_value_change["base"][:topn]))
    topn_compare = list(map(str, dimension_value_change["compare"][:topn]))
    ret = pd.DataFrame(
        {
            "dim": [dimension_name],
            "js": js,
            "element": ",".join(topn_elements),
            "base": ",".join(topn_base),
            "compare": ",".join(topn_compare),
            "change": ",".join(topn_change),
        }
    )
    return ret


def dimensions_analysis_concurrent(
    dimensions: list[str],
    dimension_dict,
    metric_where,
    metric_dict,
    date,
    topn,
    js_contribution_df,
    change,
    has_change: bool,
    model_id,
):
    max_workers = os.environ.get("analysis_concurrency_num", 5)
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_tasks = [
            executor.submit(
                dimension_analysis_process,
                dimension_name,
                dimension_dict,
                metric_where,
                metric_dict,
                date,
                topn,
                change,
                has_change,
                model_id,
            )
            for dimension_name in dimensions
        ]
        results = [
            future.result() for future in concurrent.futures.as_completed(future_tasks)
        ]
    return pd.concat([js_contribution_df] + results, ignore_index=True)


def dimension_analysis(
    metric_where,  # compare, base
    date,
    topn,
    model_id: str,
    model_name: str,
    metric_dict: dict,
    dimension_dict: dict,
):
    # # some meta info extraction
    # model = get_semantic_model(model_id)
    # project_id = model.semantic_project_id
    # metric_dict, dimension_dict = meta_extraction(metric_where, project_id, model_name)
    dimensions = list(dimension_dict.keys())
    wheres = "AND".join(["(" + mw["filter"] + ")" for mw in metric_where])
    filter_groupbys = schema_link_extraction(
        f"SELECT COUNT(*) FROM TABLE WHERE {wheres}"
    )
    for dim in filter_groupbys:
        dimensions.remove(dim)
    if not dimensions:
        raise Exception("No more dimension for analysis.")

    js_contribution_df = pd.DataFrame(columns=["dim", "js", "change"])
    params_query = {
        "metric_where": metric_where,
        "groupbys": [],
        "time": date,
        "metric_dict": metric_dict,
    }
    target_values, metric_sql = query_dim_values_diff(params_query, model_id)
    target_values = target_values.sort_values(by="base_compare", ascending=True)
    target_values["filter"] = [metric_where[1]["filter"], metric_where[0]["filter"]]
    target_values["sql"] = [metric_sql["sqlBase"], metric_sql["sqlCompare"]]
    # consider only one target metric
    metric_label = metric_where[0]["metric"]
    if target_values[metric_label].values[0] != 0:
        change = float(
            (
                target_values[metric_label].values[1]
                - target_values[metric_label].values[0]
            )
            / target_values[metric_label].values[0]
        )
    else:
        change = float(
            target_values[metric_label].values[1]
            - target_values[metric_label].values[0]
        )

    js_contribution_df = dimensions_analysis_concurrent(
        dimensions,
        dimension_dict,
        metric_where,
        metric_dict,
        date,
        topn,
        js_contribution_df,
        change,
        True,
        model_id,
    )

    js_contribution_df = js_contribution_df.sort_values(
        by="js", ascending=False
    ).reset_index(drop=True)
    js_contribution_df["metric"] = metric_label

    # try:
    #     if not (
    #         len(dimension_dict.keys()) == 1
    #         and list(dimension_dict.keys())[0] == "org_name"
    #     ):
    #         # 中原特殊场景。只对A001 A016相关指标下，维度org_name生效
    #         js_contribution_df["drop_or_not"] = js_contribution_df.apply(
    #             lambda x: len(x["element_pos"].split(",")) == 1
    #             and len(x["element_neg"].split(",")) == 1,
    #             axis=1,
    #         )
    #         js_contribution_df = js_contribution_df[
    #             js_contribution_df["drop_or_not"] == False
    #         ][
    #             [
    #                 "metric",
    #                 "dim",
    #                 "js",
    #                 "change"
    #             ]
    #         ].reset_index(
    #             drop=True
    #         )
    # except:
    #     pass
    single_dim = js_contribution_df.copy()

    return target_values, single_dim


def dfs(start, path, dimensions, params, res, max_depth=3):
    date = params["time"]
    topn = params["topn"]
    dimension_dict = params["dimension_dict"]
    filter_intr = params["filter_intr"]
    model_id = params["model_id"]

    if (
        len(dimensions) == 0
        or (len(path) >= max_depth and not start)
        or (len(path) >= (max_depth - 1) and start)
    ):
        if not start:
            # reformat path and js
            path_temp = [[i[:2] for i in path.copy()]]
            path_temp.extend(map(float, path.copy()[-1][2:]))
            res.append(path_temp)
        else:
            for node in start.children:
                dim = node.nodeLabel
                ele = node.nodeValue
                contribution = float(node.items[0]["value"])
                contribution_rate_of_change = float(node.items[-1]["value"])
                path_temp = [i[:2] for i in path.copy()]
                path_temp.append((dim, ele))
                path_temp = [path_temp, contribution, contribution_rate_of_change]
                res.append(path_temp)
        return res

    for node in start.children:
        dim = node.nodeLabel
        ele = node.nodeValue
        contribution = node.items[0]["value"]
        contribution_rate_of_change = node.items[-1]["value"]
        cur_path = path.copy()
        cur_path.append((dim, ele, contribution, contribution_rate_of_change))
        filter = filterExtension(cur_path, filter_intr)
        js_contribution_df = pd.DataFrame(
            columns=["dim", "js", "element", "contribution"]
        )
        js_contribution_df = dimensions_analysis_concurrent(
            dimensions,
            params["metric"],
            date,
            filter,
            topn,
            js_contribution_df,
            0,
            False,
            model_id,
        )
        js_contribution_df = js_contribution_df.sort_values(
            by="js", ascending=False
        ).reset_index()
        # js_contribution_df = js_contribution_df[js_contribution_df["js"] != 0][
        #    ["dim", "js", "element", "contribution"]
        # ]
        js_contribution_df = js_contribution_df[
            ["dim", "js", "element", "contribution"]
        ]

        try:
            js_contribution_df["drop_or_not"] = js_contribution_df.apply(
                lambda x: len(x["element"].split(",")) == 1, axis=1
            )
            js_contribution_df = js_contribution_df[
                not js_contribution_df["drop_or_not"]
            ][["dim", "js", "element", "contribution"]].reset_index()
        except (KeyError, IndexError, AttributeError, TypeError):
            pass

        if len(js_contribution_df) != 0:
            for i in range(len(js_contribution_df.element[0].split(","))):
                node.children.append(
                    Node(
                        label=js_contribution_df.dim[0],
                        value=js_contribution_df.element[0].split(",")[i],
                        desc=dimension_dict[js_contribution_df.dim[0]],
                        item=[
                            {
                                "text": "contribution",
                                "value": float(
                                    js_contribution_df.contribution[0].split(",")[i]
                                ),
                            },
                            {
                                "text": "contribution",
                                "value": float(
                                    js_contribution_df.contribution[0].split(",")[i]
                                )
                                * node.items[-1]["value"],
                            },
                        ],
                    )
                )
            next_dimensions = dimensions.copy()
            next_dimensions.remove(js_contribution_df.dim[0])
            res = dfs(node, cur_path, next_dimensions, params, res)
        else:
            res = dfs([], cur_path, [], params, res)
    return res
