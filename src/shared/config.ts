import axios, { InternalAxiosRequestConfig } from 'axios'
import { Request } from 'express'
import dayjs from 'dayjs'
import { pick } from 'lodash'
import { message } from 'antd'
import { createTraceId, getQueryState } from './common-utils'
import { DEFAULT_TIMEOUT, TOKEN_BI, TOKEN_RANGER, U_TOKEN_RANGER } from './constants'
import { askBIPageUrls } from './url-map'
import { APIResponse } from './common-types'
import { beforeLogout } from './auth'

function rangerInterceptor({ config, req }: { config: InternalAxiosRequestConfig<any>; req?: Request<any> }) {
  if (
    config.url?.startsWith(process.env.AUTH_LOGIN_HOST ?? '') ||
    config.url?.startsWith(process.env.XENGINE_PROXY_URL ?? '')
  ) {
    // const url = config.url
    // console.info(`Ranger[${url}] 拦截到RangerURL=${url}`)
    const skip =
      config.headers && (config.headers['authorization'] === 'init' || config.headers['Authorization'] === 'init')
    // console.info(`Ranger[${url}] 跳过鉴权=${skip}`)
    // node请求ranger时的token
    if (!skip && req?.headers && req.headers[TOKEN_RANGER]) {
      const token = `Bearer ${req.headers[TOKEN_RANGER]}`
      config.headers['Authorization'] = token
      // console.info(`Ranger[${url}] 设置headers['Authorization']=${token}, FROM=${TOKEN_RANGER}`)
    } else if (!skip && req?.cookies && req.cookies[TOKEN_RANGER]) {
      const token = `Bearer ${req.cookies[TOKEN_RANGER]}`
      config.headers['Authorization'] = token
      // console.info(`Ranger[${url}] 设置headers['Authorization']=${token}, FROM=${TOKEN_RANGER}`)
    } else if (!skip && req?.headers && req.headers[U_TOKEN_RANGER]) {
      config.headers.delete('Authorization')
      const token = `JSESSIONID=${req.headers[U_TOKEN_RANGER]}`
      config.headers.set('Cookie', token)
      // console.info(`Ranger[${url}] 设置Cookie=${token}, FROM=${U_TOKEN_RANGER}`)
    } else if (!skip && req?.cookies && req.cookies[U_TOKEN_RANGER]) {
      const token = `JSESSIONID=${req.headers[U_TOKEN_RANGER]}`
      config.headers.delete('Authorization')
      config.headers.set('Cookie', token)
      // console.info(`Ranger[${url}] 设置Cookie=${token}, FROM=${U_TOKEN_RANGER}`)
    }
    config.headers.set('Cookie', req?.headers.cookie)
    console.info(
      `[XE ${dayjs().format('YYYY-MM-DD HH:mm:ss')}] ${config.method} ${config.url} ${JSON.stringify(pick(config.headers, 'Authorization', 'cookie'))} ${JSON.stringify(config.data)}`,
    )
  }
}

let isShowErrorMessage = false

// 所有请求将会有一分钟的超时时间
axios.defaults.timeout = DEFAULT_TIMEOUT

// 请求拦截器
axios.interceptors.request.use(function (config) {
  const queryState = getQueryState(true)
  if (queryState.enableAutoLogin && queryState.username) {
    config.headers['x-username'] = queryState.username
  }

  // if (typeof window !== 'undefined') {
  //   const userInfo = sessionStorage.getItem('loginUserInfoData')
  //   if (userInfo) {
  //     const parseUserInfo = JSON.parse(userInfo) || {}
  //     if (parseUserInfo && parseUserInfo?.username) {
  //       config.headers['Authorization'] = `Bearer ${parseUserInfo?.token}`
  //     }
  //   }
  // }
  // TODO merge 上浦发之前 下面改成 兼容 session storage 的形式

  if (typeof window !== 'undefined') {
    const token = localStorage.getItem(TOKEN_BI)
    const rangerToken = localStorage.getItem(TOKEN_RANGER) // bi 会带着ranger token
    const rangerUToken = localStorage.getItem(U_TOKEN_RANGER)
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }
    if (rangerToken) {
      config.headers[TOKEN_RANGER] = rangerToken
    }
    if (rangerUToken) {
      config.headers[U_TOKEN_RANGER] = rangerUToken
    }
  } else if (typeof process !== 'undefined') {
    // axios 前后端共用的问题
    // eslint-disable-next-line @typescript-eslint/no-var-requires
    const { getCurrentContext } = require('src/server/utils/storage')
    const { req }: { req?: Request } = getCurrentContext() || {}
    rangerInterceptor({ req, config })
  }
  if (!config.headers.traceId) {
    config.headers.traceId = createTraceId()
  }
  return config
})

// 响应拦截器
axios.interceptors.response.use(async function (response) {
  const { data } = response
  // code 不存在，说明非标准格式，不做检查，直接放过
  if (data?.code == null) {
    return response
  }

  // 处理认证相关错误
  if (!isShowErrorMessage) {
    // 处理各种认证错误情况
    if (
      data.code === 401 ||
      data.code === 339971 // Ranger登录失效
    ) {
      const queryState = getQueryState(true)
      if (!queryState.enableAutoLogin) {
        // 清除所有认证相关的token
        if (typeof window !== 'undefined') {
          beforeLogout()
        }
        await handleLoginRedirect(data?.msg || '认证已过期，请重新登录')
      }
    } else if (data.code === 403) {
      isShowErrorMessage = true
      throw new Error(data?.msg || '暂无权限，请联系管理员开通权限')
    }
  }

  if (data.code !== 0) {
    throw new Error(response.data.data?.toString() ?? response.data.msg, { cause: response.data })
  }

  // 其他情况，直接返回 response
  return response
})

// 为了通过浦发安全验证添加一次性 token
// const csrfPlugin = createCsrfPlugin()
// csrfPlugin(axios)

// 设置全局的 Axios 默认配置
export function setAxiosDefaults(traceId?: string) {
  if (traceId) {
    axios.defaults.headers.common['traceId'] = traceId
  }
}
/**
 * 处理登录重定向
 *
 * @param msg 提示信息，默认为“登录已过期，请重新登录”
 * @returns 无返回值
 */
async function handleLoginRedirect(msg: string) {
  isShowErrorMessage = true

  if (typeof window !== 'undefined' && typeof localStorage !== 'undefined') {
    message.error(msg || '登录已过期，请重新登录')
    await new Promise((resolve) => setTimeout(resolve, 800))

    const brandInfo = localStorage.getItem('brandInfo')
    if (!brandInfo || brandInfo === 'null') {
      window.location.href = askBIPageUrls.login
    } else {
      const appId = brandInfo ? JSON.parse(brandInfo).appId : null
      const loginUrl = appId ? `${askBIPageUrls.login}?appId=${appId}` : askBIPageUrls.login
      localStorage.clear()
      sessionStorage.clear()
      window.location.href = loginUrl
    }
  } else {
    // Node.js 环境中的处理逻辑
    console.error(msg || '登录已过期，请重新登录')
  }
}

export class APIError extends Error {
  response: APIResponse<any>

  constructor(message: string, response: APIResponse<any>) {
    super(message)
    this.name = this.constructor.name
    this.response = response
  }
}
