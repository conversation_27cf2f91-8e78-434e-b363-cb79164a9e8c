export const PAGE_LIST = [
  // 智能对话
  '/chat',
  // 指标
  '/metric-store/metrics',
  // 维度
  '/metric-store/dimensions',
  // 指标树
  '/metric-store/metric-tree',
  // 报表
  '/metric-store/charts',
  // 文档数据
  '/metric-store/document',
  // 提问历史
  '/metric-store/ask-history',
  // 业务术语
  '/metric-store/hint',
  // 外部报表
  '/metric-store/external-report',
  // 模版管理
  '/metric-store/smart-report/template',
  // 报告管理
  '/metric-store/smart-report/report',
  // 打标任务管理
  '/metric-store/smart-report/marking-task',
  // ASKBOT项目管理
  '/manage/project/list',
  // ASKBOT场景管理
  '/manage/scenario',
  // 用户管理
  '/manage/admin/user',
  // 角色管理
  '/manage/admin/role',
  // 资源管理
  '/manage/admin/resource',
  // ETL画布
  '/manage/data-scene',
  // 外部数据源
  '/manage/external-datasource',
  // 文件数据源
  '/manage/file-datasource',
  // 虚拟表
  '/manage/data-model',
  // 指标模型管理
  '/manage/metric-model',
  // ER 管理
  '/manage/er-management/er-management',
  // 物化管理
  '/manage/materialization',
  // SQL查询
  '/manage/sql-query/sql-query',
  // 运维管理
  '/manage/operation',
  // 多租户
  '/manage/tenant',
] as const

// ASKBI 产品的页面
export const ASKBI_PAGE_LIST = [
  '/chat',
  '/metric-store/metrics',
  '/metric-store/dimensions',
  '/metric-store/metric-tree',
  '/metric-store/charts',
  '/metric-store/ask-history',
  '/metric-store/hint',
  '/metric-store/external-report',
  '/manage/project/list',
  '/manage/scenario',
  '/manage/admin/user',
  '/manage/admin/role',
  '/manage/admin/resource',
  '/manage/external-datasource',
  '/manage/file-datasource',
  '/manage/data-model',
  '/manage/metric-model',
  '/manage/er-management/er-management',
  '/manage/sql-query/sql-query',
] as const

// ASKDOC 产品的页面
export const ASKDOC_PAGE_LIST = [
  '/chat',
  '/metric-store/document',
  '/metric-store/ask-history',
  '/metric-store/hint',
  '/metric-store/smart-report/template',
  '/metric-store/smart-report/report',
  '/metric-store/smart-report/marking-task',
  '/manage/project/list',
  '/manage/scenario',
  '/manage/admin/user',
  '/manage/admin/role',
  '/manage/admin/resource',
] as const

// XE 产品的页面
export const XE_PAGE_LIST = [
  '/manage/admin/user',
  '/manage/admin/role',
  '/manage/admin/resource',
  '/manage/data-scene',
  '/manage/external-datasource',
  '/manage/file-datasource',
  '/manage/data-model',
  '/manage/metric-model',
  '/manage/er-management/er-management',
  '/manage/materialization',
  '/manage/sql-query/sql-query',
  '/manage/operation',
  '/manage/tenant',
] as const

export type Page = (typeof PAGE_LIST)[number]

export type Role = 'admin' | 'user'

export type Product = 'Ask-BI' | 'Ask-DOC' | 'X-Engine'

export const ROLE_PAGE_LIST: Record<Product, Record<Role, Page[]>> = {
  'Ask-BI': {
    admin: [
      '/chat',
      '/metric-store/metrics',
      '/metric-store/dimensions',
      '/metric-store/metric-tree',
      '/metric-store/charts',
      '/metric-store/ask-history',
      '/metric-store/hint',
      '/metric-store/external-report',
      '/manage/project/list',
      '/manage/scenario',
      '/manage/admin/user',
      '/manage/admin/role',
      '/manage/admin/resource',
      '/manage/data-scene',
      '/manage/external-datasource',
      '/manage/file-datasource',
      '/manage/data-model',
      '/manage/metric-model',
      '/manage/er-management/er-management',
      '/manage/materialization',
      '/manage/sql-query/sql-query',
      '/manage/operation',
      '/manage/tenant',
    ],
    user: ['/chat', '/metric-store/metrics', '/metric-store/dimensions', '/metric-store/charts'],
  },
  'Ask-DOC': {
    admin: [
      '/chat',
      '/metric-store/metrics',
      '/metric-store/dimensions',
      '/metric-store/metric-tree',
      '/metric-store/charts',
      '/metric-store/ask-history',
      '/metric-store/hint',
      '/metric-store/external-report',
      '/manage/project/list',
      '/manage/scenario',
      '/manage/admin/user',
      '/manage/admin/role',
      '/manage/admin/resource',
      '/manage/external-datasource',
      '/manage/file-datasource',
      '/manage/data-model',
      '/manage/metric-model',
      '/manage/er-management/er-management',
      '/manage/sql-query/sql-query',
    ],
    user: ['/chat', '/metric-store/metrics', '/metric-store/dimensions', '/metric-store/charts'],
  },
  'X-Engine': {
    admin: [
      '/manage/admin/user',
      '/manage/admin/role',
      '/manage/admin/resource',
      '/manage/data-scene',
      '/manage/external-datasource',
      '/manage/file-datasource',
      '/manage/data-model',
      '/manage/metric-model',
      '/manage/er-management/er-management',
      '/manage/materialization',
      '/manage/sql-query/sql-query',
      '/manage/operation',
      '/manage/tenant',
    ],
    user: [
      '/manage/data-scene',
      '/manage/external-datasource',
      '/manage/file-datasource',
      '/manage/data-model',
      '/manage/metric-model',
      '/manage/er-management/er-management',
      '/manage/materialization',
      '/manage/sql-query/sql-query',
      '/manage/operation',
    ],
  },
}
