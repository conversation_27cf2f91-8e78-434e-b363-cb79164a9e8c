/* eslint-disable @typescript-eslint/naming-convention */
/**
 * @description 客户端和服务端共用的类型定义
 */
import { Datasource as PrismaDatasource, Prisma, Role, Conver } from '@prisma/client'
import { ConfigInfo } from './config-management'

export * from './types/chat'

/** 产品名称 */
export const AppNames = ['ask-bi', 'ask-doc'] as const
export type AppName = (typeof AppNames)[number]

export type ThemeType = 'light' | 'dark'

export interface BlobWithRatio extends Blob {
  ratio: number
}

/**
 * 图表大类，对应 4 中数据分析场景:
 * 对比：差异、排名
 * 分布：占比、比例
 * 关系：
 * 时间序列：变化、趋势、周期
 */
export type ChartGroup = 'Comparison' | 'Distribution' | 'Relationship' | 'TimeSeries' | 'Rank' | 'Others'

// 指标交互时 传递给后端的参数

export interface APIResponse<T> {
  code: number
  data?: T
  msg?: string
}

export const DateTypes = [
  {
    value: 'day',
    label: '日',
  },
  {
    value: 'month',
    label: '月',
  },
  {
    value: 'quarter',
    label: '季度',
  },
  {
    value: 'year',
    label: '年',
  },
] as const

export type DateType = (typeof DateTypes)[number]['value']
// 最小的时间粒度选项：只支持日、月、年
export const TimeGranularityMinDateOptions = [
  {
    value: 'day',
    label: '日',
  },
  {
    value: 'month',
    label: '月',
  },
  {
    value: 'year',
    label: '年',
  },
] as const
export const TimeDimensionFormats = [
  'yyyyMMDD',
  'yyyyMMdd',
  'yyyy',
  'yyyyMM',
  'yyyy-MM',
  'yyyy/MM',
  'yyyy_MM_DD',
  'yyyy_MM_dd',
  'yyyy/MM/DD',
  'yyyy/MM/dd',
  'yyyy-MM-DD',
  'yyyy-MM-dd',
] as const
export const TimeDimensionTypes = ['string', 'date', 'datetime'] as const
export type TimeDimensionFormat = (typeof TimeDimensionFormats)[number]
export type TimeDimensionType = (typeof TimeDimensionTypes)[number]
export type TimeGranularityMinType = (typeof TimeGranularityMinDateOptions)[number]['value']
export const TimeGranularityOptions = [
  {
    value: 'day',
    label: '日',
  },
  {
    value: 'month',
    label: '月',
  },
  {
    value: 'quarter',
    label: '季度',
  },
  {
    value: 'year',
    label: '年',
  },
  {
    value: 'total',
    label: '全部',
  },
] as const
export type TimeGranularityType = (typeof TimeGranularityOptions)[number]['value']

export interface LlmListByUsernameResponse {
  llmList: Llm[]
  defaultLlmType: LlmType
}

export interface Message {
  role: 'system' | 'assistant' | 'user' | 'function'
  content: string
  extra_info?: string
}

export interface Conversation {
  id: string
  /**
   * 用户的问题列表，方便其他地方使用。
   * 注意这个 asks 和 text2SqlMessages 中的 users 并不一定完全对应
   */
  asks: string[]
  /** text2TaskType 的会话历史，ChatGPT 支持多任务使用 */
  /** TODO: 让多任务支持多轮会话 */
  // text2TaskTypeMessages: Message[]
  /**
   * 当前选中的场景，目前只支持1个，以后需要扩展成多个
   * 修改后，需要清空当前的会话历史，也就是清空 asks，text2SqlMessages
   */
  sceneId: string
  /** text2sql 的会话历史，table 变更后会被清空 */
  text2SqlMessages: Message[]
  // TODO: 添加 text2ChartGroupMessages 让图表大类支持多轮会话
}

export type ExtraInfo = {
  // 新增的判断子公司参数↓
  groupbys_with_level: any[]
  // 追问tag
  intent_tags?: string[]
  // 是否连续提问,连续提问采用的实际问题
  sequential_first_question?: string
  // 指标评分
  metric_scores: { [key: string]: number }
  // timeQueryType
  timeQueryType: '日' | '月' | '季' | '年' | null
}

export type DatasetDatum = {
  projectId: string
  projectName: string
  sceneId: string
  sceneLabel: string
  tableName: string
} & ConfigInfo

export interface TableDatum {
  /** 对应的数据库名字，这里做一个数据冗余 */
  databaseName: string
  tableName: string
  /** columns 支持异步加载，所以可以为空 */
  columns?: ColumnDatum[]
}

export interface ColumnDatum {
  /** 对应的数据库名和表名，数据冗余方便使用 */
  databaseName: string | null
  tableName: string | null
  columnName: string | null
  columnType: string | null
  columnComment: string | null
}

export type ChartCreateInput = Prisma.ChartCreateInput

export type LlmTypeGPT =
  | 'gpt-3.5-turbo'
  | 'gpt-4-turbo-preview'
  | 'azure-gpt'
  | 'zhipu-glm-4'
  | 'zhipu-chatglm3_32b'
  | 'baichuan2'
  | 'yi_1.5_34b'
  | 'yi_1.5_34b_16k'
  | 'chat_law_7b'
  | 'pre-glm-4-9b'
  | 'deepseek-14b'

/** 图表推荐使用纯规则实现 */
export type MockChartRecommendType = 'chart-type-use-rule'

export type LlmType = LlmTypeGPT | 'vllm-mixtral-8x7b-chat' | MockChartRecommendType

export interface Llm {
  type: LlmType
  id: LlmType
  name: string
  abbrName: string
  tokenLimit: number
  logo: string
  /** 是否禁用模型 */
  disable: boolean
}

/** 导出文件类型 */
export const ExportFileTypes = {
  PNG: 'PNG',
  CSV: 'CSV',
  Excel: 'Excel',
} as const

export type ExportFileType = keyof typeof ExportFileTypes

/* ChatLog 日志的类型，对应包括 src/server/ai/prompts.ts 中的函数名 + 一些外部大模型的直接调用 */
export const ChatLogTypes = {
  nl2Sql: 'nl2Sql',
  chartInsight: 'chartInsight',
} as const

/** Winston日志级别，项目中仅用info和error */
export const WinstonLogLevels = {
  error: 'error', // 0
  warn: 'warn', // 1
  info: 'info', // 2
  http: 'http', // 3
  verbose: 'verbose', // 4 用于提供详细的信息，通常用于调试和诊断目的
  debug: 'debug', // 5 用于提供详细的调试信息，通常用于调试应用程序中的问题。
  silly: 'silly', // 6 用于提供冗长或无关紧要的信息，通常用于开发阶段的详细日志记录。
} as const

export type Datasource = PrismaDatasource

export type SystemInfoResponse = {
  startTime: string
}

export type RoleItem = {
  roleId: string
  role: Role
}

export type UserPermissionDatasource = { id: string; username: string; datasourceId: string }
export type UserPermissionLlmType = { id: string; username: string; llmType: string }
export type UserPermissionProject = { id: string; username: string; semanticProjectId: string }

export type FileMimeTypeInfo =
  | 'text/html'
  | 'application/pdf'
  | 'application/msword'
  | 'application/vnd.ms-excel'
  | 'text/csv'
  | 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  | 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  | 'application/vnd.openxmlformats-officedocument.presentationml.presentation'

// conver表返回给前端的每一个Conversation元素类型
export type ConverWithDataset = Conver & {
  dataset: DatasetDatum
}
export interface MenuItem {
  key: string
  label: React.ReactNode
  path?: string
  icon?: React.ReactNode
  children?: MenuItem[]
  type?: 'group' // 当type为group时，会作为分组处理
}

export type BotFeature = 'BI' | 'Doc' | 'Report'

export interface AccessPathsType {
  path: string
  allowed: boolean
}
export type CatalogType = {
  id: number
  name: string
  type: string
}
export type DatabaseType = {
  catalogName: string
  catalogType: string
  id: number
  name: string
}

/**提问输入框json格式 */
export type JsonContentItem = {
  type?: string
  'data-type'?: string
  'data-content'?: string
  'data-id'?: string
  'data-name'?: string
  children?: JsonContentItem[]
  [key: string]: string | JsonContentItem[] | number | undefined
}

export enum STRUCTURED_MESSAGE_DATA_TYPE {
  METRIC = 'metric',
  DIMENSION = 'dimension',
  SUB_DIMENSION = 'sub-dimension',
  TEXT = 'text',
}

export type MessageInputEditorRef = {
  setHtml: (data: JsonContentItem[] | string) => void
  updateCursorPosition?: () => void
}
export interface BrandInfoType {
  appId: string
  logo: string
  chatUserIcon: string
  companyName: string
  brandName: string
  // llm名字 用于替换Dipeak
  llmName?: string
  // 联合logo，用于显示公司logo以及客户公司logo
  jointLogo?: string
  // 头部信息配置，false代表不展示，undef代表默认展示
  header?:
    | boolean
    | {
        logo?: boolean
        tab?: boolean
        userInfo?: boolean
        themeToggle?: boolean
      }
}

export interface UserInfoType {
  admin: boolean
  token: string
  email: string
  pageAccessResultList: string[] | null
  groups: string[]
  register: boolean
  username: string
  lastLoginAt: string
  jwtToken: string
}

export interface UserInfo {
  id: string
  username: string
  isAdmin?: boolean
  nickname: string
  groups: { id: string; groupName: string }[]
}

// 统一的权限数据类型, 方便后续扩展其他的
export type AuthData = BaoWuAuthData

export type BaoWuAuthData = {
  username?: string
  userId?: string
  timestamp?: string
  appId?: string
  token?: string
}

export interface QueryState {
  username?: string
  password?: string
  proxy?: string
  appid?: string
  enableOnlyChat: boolean
  enableAutoLogin: boolean
  enableReloadQueryState: boolean
  enableLangfuse: boolean
  enableNextGeneration: boolean
  hideHeader?: boolean
}

export interface ReportOutlineItemType {
  allowChildren: boolean
  children: ReportOutlineItemType[]
  content: string
  dependsOn: number[]
  id: number
  maxChildrenCount: number
  title: string
}

export interface ReportTemplateType {
  createAt: string
  createUser: string
  id: number
  name: string
  outline: ReportOutlineItemType[]
  thumbnailPath: string
}

export interface ReportListType {
  updateAt: string
  createAt: string
  modelName: string
  reportIntention: string
  templateId: number
  reportTitle: string
  creator: string
  sceneId: string
  reportId: number
  status: string
}

export interface DataFilterParams {
  columnName: string
  columnCode: string
  operator: string
  values: string[]
}

export interface dataTimeParams {
  timeColumn: string
  timeRangeStart: string
  timeRangeEnd: string
}

// 报告详情
export interface ReportDetailType {
  outlineNodes: ReportOutlineItemType[]
  dataFilterParams: DataFilterParams[]
  dataTimeParams: dataTimeParams
  reportInfo: ReportListType
}

export interface OutlineItemType {
  id: number
  key: string
  title: string
  content: string
  allowChildren: boolean
  maxChildrenCount: number
  dependsOn: number[]
  children: OutlineItemType[]
}

export interface DataOpType {
  computeType: string
  dataDescTemplate: string
  dataFilter: string[]
  dataOpId: number
  groupBy: string
  metric: string
  name: string
  operator: string
  operatorDesc: string
  outputDataSectionParams: string[]
  outputLimit: number
  outputOrderBy: string
  reportId: number
  sectionId: number
  timeGranularity: string
  timeRangeEnd: string
  timeRangeStart: string
}

export interface TextOpType {
  name: string
  prompt: string
  reportId: number
  sectionId: number
  textOpId: number
}

export interface SectionConfigType {
  dataOp: DataOperatorType[]
  maxWordLen: number
  minWordLen: number
  reportId: number
  sectionId: number
  sectionIntention: string
  textOp: TextOperatorType[]
}

export type ConcatString<T, S extends string = ''> = T extends string
  ? `${S}${T}`
  : T extends (...args: infer P) => infer R
    ? (...args: P) => ConcatString<R, S>
    : T extends object
      ? {
          [key in keyof T]: ConcatString<T[key], S>
        }
      : T

export type ConcatBaseUrlReturnType<T> = ConcatString<T, '$BASE_URL'>

export interface TextOperatorType {
  name: string
  prompt: string
  templateId: number
  sectionId: number
  textOpId: number
  type: string
  result: string
  inputDataOpIds: number[]
  inputSectionIds: number[]
}

export interface DataFilterType {
  columnName: string
  columnCode: string
  operator: string
  filterType: number
  values: string[] | string
}

export interface DataOperatorType {
  computeType: string
  dataDescTemplate: string
  dataFilter: DataFilterType[]
  dataOpId: number
  groupBy: string
  metric: string
  name: string
  result: string
  operator: string
  operatorDesc: string
  outputDataSectionParams: string[]
  outputLimit: number
  outputOrderBy: string
  enumOrder: string
  segmentationOptions: string
  templateId: number
  sectionId: number
  timeGranularity: string
  timeRangeEnd: string
  timeRangeStart: string
  timeColumn: string
}

export interface IVirtualTable {
  catalogName: string
  columns: IVirtualTableColumn[]
  computeType: string
  databaseName: string
  displayList: string[]
  hot: number
  id: number
  joinVO: null
  like: null
  modification: null
  name: string
  query: string
  settings: null
  streamBatchVO: null
  tag: null
  timeColumn: string
  unionVO: null
  user: string
  virtualTableType: string
  [property: string]: any
}

export interface IVirtualTableColumn {
  columnPrecision: number
  columnScale: number
  columnType: string
  comment: string
  displayList: null
  id: number
  name: string
  not_allow_null: boolean
  [property: string]: any
}

export type ReportModuleType =
  | 'CHAT_INSIGHT_STREAMING'
  | 'CHAT_METRICS'
  | 'METRIC_TO_SQL_TO_DATA'
  | 'SQL_TO_DATA'
  | 'NODE_MIDDLEWARE'
  | 'MULTI_AGENT_AGENT_CHAT'
  | 'MULTI_AGENT_AFTER_MATCH'
  | 'MULTI_AGENT_CHAT_PROGRESS_CALLBACK'
  | 'MULTI_AGENT_STOP_CHAT'
  | 'docs-generate-report'
  | 'docs-query-document'
  | 'docs-upload-file'

export interface ReportLogResponse {
  timestamp: string
  user_id: string
  request_id: string
  host: string
  service_type: 'web_service'
  start_time: string
  end_time: string
  duration: number
  result_code: number
  module_type: ReportModuleType
  input: Record<string, any>
  output: Record<string, any> | string
  debug: Record<string, any>
  url: string
  semantic_project_id?: string
  semantic_scene_id?: string
  semantic_project_name?: string
  semantic_scene_name?: string
  cluster_id?: string
}

export interface VTableBaseInfo {
  catalog: string
  database: string
  table: string
}

export type MetricJobInfo = {
  projectId?: string
  scenesId?: string
  projectName?: string
  sceneName?: string
  jobId?: string
  metricsIds?: string[]
  metricsArr?: { metricId: string; metricName: string }[]
  publishId?: string
  publishTime?: string
  url?: string
  flinkUrl: string
  status: string
  mvName: string
  modelName: string
  checkpointLocation: string | null
}

export type VTStreamJob = {
  catalog: string
  config: string
  creator: string
  database: string
  flinkUrl: string
  jobId: string
  metricsIds: string | null
  modelName: string
  projectId: string | null
  publishId: string | null
  publishTimeStamp: number
  publisher: string
  scenesId: string | null
  status: string
  table: string
  taskType: string
  checkpointLocation: string | null
}

export type ColumnInfo = {
  name: string
  /**
   * 类型 precision
   */
  precision: number
  /**
   * 类型 scale
   */
  scale: number
  /**
   * 数据类型
   */
  type: string
}

export interface WidgetDesc {
  /**
   * 控件输入字段
   */
  inputs: WidgetColumnDesc[]
  /**
   * 根据type，按照对应的类型，目前只能填SOAP_LOOKUP，所以按照 SoapLookupConfig 序列化为json格式的字符串
   */
  jsonConfig: string
  /**
   * 控件输出字段
   */
  outputs: WidgetColumnDesc[]
  /**
   * 目前只能填 SOAP_LOOKUP
   */
  type: string
}

/**
 * WidgetColumnDesc
 */
export interface WidgetColumnDesc {
  name: string
  /**
   * 类型 precision
   */
  precision?: number
  /**
   * 类型 scale
   */
  scale?: number
  /**
   * 数据类型
   */
  type: string
}

export type VTable = {
  catalogName: string
  columns: VTableColumn[]
  computeType: string
  databaseName: string
  displayList: string[]
  hot: number
  id: number
  joinVO: null
  like: null
  modification: null
  name: string
  query: string
  settings: null
  /**
   * CREATE=未上线/ONLINE=已上线/OFFLINE=已下线/PAUSED=已暂停
   */
  status: string
  streamBatchVO: null
  tag: null
  timeColumn: string
  unionVO: null
  user: string
  virtualTableType: string
  [property: string]: any
}

export type VTableColumn = {
  columnPrecision: number
  columnScale: number
  columnType: string
  comment: string
  displayList: null
  id: number
  key: number
  name: string
  not_allow_null: boolean
  [property: string]: any
}

export type MetricStreamTaskQueryInfo = {
  /**
   * 创建人
   */
  creator?: string
  current: number
  /**
   * 指标Id
   */
  metricsId?: string
  pageSize: number
  /**
   * 项目Id
   */
  projectId?: string
  /**
   * 上线人
   */
  publisher?: string
  /**
   * 上线时间开始
   */
  publishTimeEnd?: string
  /**
   * 上线时间开始
   */
  publishTimeStart?: string
  /**
   * 场景Id
   */
  scenesId?: string
  /**
   * 任务状态，参考虚拟表流任务状态
   */
  status?: string
}

export type MVItem = {
  /**
   * 状态
   */
  active: boolean
  /**
   * 物化视图是否可用
   */
  available: boolean
  /**
   * 平均运行时间
   */
  averageRunTime: number
  /**
   * 数据目录
   */
  catalogName?: string
  /**
   * 压缩后大小
   */
  compressedSize: string
  /**
   * 压缩比
   */
  compressionRatio: string
  /**
   * cpu资源消耗量
   */
  cpuUsage: string
  /**
   * 创建时间，时间戳类型
   */
  createOn: number
  /**
   * 创建人
   */
  creator?: string
  /**
   * 物化关联模型
   */
  dataModelDesc: DataModelDesc
  /**
   * 数据库名称
   */
  dbName?: string
  displayName?: string
  /**
   * 事实表列表，2024-08-12新增
   */
  factTables: string[]
  /**
   * 总命中数
   */
  hitCount?: number
  /**
   * 最近一次运行时间，时间戳类型
   */
  latestRunTime: number
  /**
   * 内存资源消耗量
   */
  memoryUsage: string
  /**
   * 物化视图ID
   */
  mvId?: number
  /**
   * 物化视图名称
   */
  mvName?: string
  /**
   * 关联资产数量
   */
  relatedMetricsCount: number
  revenue?: number
  /**
   * 运行周期
   */
  scheduleFrequency?: string
  scheduleStartTime?: number
  scheduleStopTime?: number
  scheduleType?: string
  /**
   * 物化视图类型分组，2024-02-28新增
   */
  typeGroup: TypeGroup
  /**
   * 未压缩大小
   */
  uncompressedSize: string
  /**
   * 物化视图类型，其中STREAM为流式物化，其他为批量物化
   */
  viewType?: ViewType
  [property: string]: any
}

/**
 * 物化关联模型
 *
 * DataModelDesc
 */
export type DataModelDesc = {
  /**
   * 数据目录
   */
  catalog: string
  /**
   * 备注
   */
  comment?: string
  customSqlDesc: CustomSQLDesc
  /**
   * 模型表类型
   */
  dataModelDescType: DataModelDescType
  /**
   * 数据处理描述，2024-03-27 新增
   */
  dataProcessDesc?: DataProcessDesc
  /**
   * 事实表
   */
  factTable: string
  filter: FilterDesc
  having: FilterDesc
  joinDag: JoinDagDesc
  limit: LimitDesc
  /**
   * 度量列表
   */
  measures: MeasureDesc[]
  /**
   * 模型名称
   */
  modelName: string
  /**
   * 模型类型，2024-03-27 新增
   */
  modelType: ModelType
  /**
   * 计算结果输出描述，2024-03-27 更新
   */
  outputDesc?: OutputDesc
  /**
   * 分区描述
   */
  partitionDesc?: PartitionDesc
  /**
   * 物化关联项目，2024-07-24更新
   */
  project?: string
  /**
   * 资源描述
   */
  resourceDesc?: ResourceDesc
  /**
   * 排序描述
   */
  sort: SortDesc
  /**
   * DEFAULT，CUSTOM_SQL
   */
  sqlMode: string
  /**
   * 流描述
   */
  streamingDesc?: StreamingDesc
  timeColumnDesc: TimeColumnDesc
  /**
   * 窗口描述
   */
  windowDesc?: WindowDesc
  [property: string]: any
}

/**
 * CustomSqlDesc
 */
export type CustomSQLDesc = {
  /**
   * CEP，XENGINE
   */
  customSqlType: string
  sql: string
  [property: string]: any
}

/**
 * 模型表类型
 */
export enum DataModelDescType {
  Batch = 'BATCH',
  Stream = 'STREAM',
  StreamBatch = 'STREAM_BATCH',
}

/**
 * 数据处理描述，2024-03-27 新增
 *
 * DataProcessDesc
 */
export type DataProcessDesc = {
  strategyList?: StrategyList[]
  /**
   * 虚拟表可视化数据处理配置，2024-04-28新增
   */
  virtualTableProcessDesc?: VirtualTableProcessDesc
  /**
   * 虚拟表SQL数据处理配置，2024-04-28新增
   */
  virtualTableSqlDesc?: VirtualTableSQLDesc
  [property: string]: any
}

export type StrategyList = {
  primaryKeys: ColumnDesc[]
  strategyType: StrategyType
  version: ColumnDesc
  [property: string]: any
}

/**
 * ColumnDesc
 *
 * 度量字段信息
 *
 * 列
 *
 * 时间列
 *
 * 旧版使用
 *
 * windowType为TUMBLE时有效，旧版本使用
 */
export type ColumnDesc = {
  alias?: string
  /**
   * 字段描述
   */
  comment?: string
  /**
   * 字段名称，database.table.column
   */
  name: string
  /**
   * 字段类型
   */
  type?: string
  vertexId: string
  [property: string]: any
}

export enum StrategyType {
  Update = 'UPDATE',
}

/**
 * 虚拟表可视化数据处理配置，2024-04-28新增
 *
 * VirtualTableProcessDesc
 */
export type VirtualTableProcessDesc = {
  cases: CaseWhenDesc[]
  expressions: ExpressionDesc[]
  filterDesc: string
  type: string
  [property: string]: any
}

/**
 * CaseWhenDesc
 */
export type CaseWhenDesc = {
  caseValue: string
  whenFilter: VirtualTableFilter
  [property: string]: any
}

/**
 * VirtualTableFilter
 */
export type VirtualTableFilter = {
  column: ColumnDesc
  condition: AtomicCondition
  [property: string]: any
}

/**
 * AtomicCondition
 */
export type AtomicCondition = {
  operator: Operator
  params: ParameterDesc[]
  [property: string]: any
}

export enum Operator {
  Between = 'BETWEEN',
  Eq = 'EQ',
  Ge = 'GE',
  Gt = 'GT',
  In = 'IN',
  Is = 'IS',
  LE = 'LE',
  Like = 'LIKE',
  Lt = 'LT',
  Neq = 'NEQ',
  NotIn = 'NOT_IN',
}

/**
 * ParameterDesc
 */
export type ParameterDesc = {
  function: FunctionDesc
  type: PurpleType
  valueType: ValueType
  vertexId: string
  [property: string]: any
}

/**
 * FunctionDesc
 */
export type FunctionDesc = {
  function: FunctionEnum
  params: { [key: string]: any }[]
  [property: string]: any
}

export enum FunctionEnum {
  Avg = 'AVG',
  CaseWhen = 'CASE_WHEN',
  Count = 'COUNT',
  CountDistinct = 'COUNT_DISTINCT',
  Date = 'DATE',
  Left = 'LEFT',
  Max = 'MAX',
  Min = 'MIN',
  SubString = 'SUB_STRING',
  Sum = 'SUM',
}

export enum PurpleType {
  Column = 'COLUMN',
  ConditionValue = 'CONDITION_VALUE',
  Constant = 'CONSTANT',
  Function = 'FUNCTION',
}

export enum ValueType {
  Boolean = 'BOOLEAN',
  Date = 'DATE',
  Numeric = 'NUMERIC',
  String = 'STRING',
}

/**
 * ExpressionDesc
 */
export type ExpressionDesc = {
  column: ColumnDesc
  expression: string
  unit: string
  [property: string]: any
}

/**
 * 虚拟表SQL数据处理配置，2024-04-28新增
 *
 * VirtualTableSqlDesc
 */
export type VirtualTableSQLDesc = {
  sql: string
  [property: string]: any
}

/**
 * FilterDesc
 *
 * 2024-09-05新增
 */
export type FilterDesc = {
  conditions: Condition[]
  filterExpr?: string
  [property: string]: any
}

/**
 * 条件对象
 */
export type PurpleCondition = {
  /**
   * AtomicCondition或者CombinedCondition
   */
  condition: Condition
  functionDesc: FunctionDesc
  [property: string]: any
}

/**
 * ParamDesc
 */
export type ParamDesc = {
  /**
   * 条件对象
   */
  condition?: PurpleCondition
  function?: { [key: string]: any }
  /**
   * 如果type是TIME_INTERVAL_EXPR时，不为空
   */
  timeIntervalExprDesc?: string
  type: FluffyType
  /**
   * 如果type是COLUMN，value为字段值；如果type是CONSTANT，value为常量
   */
  value?: string
  valueType?: ValueType
  /**
   * 主要是ER模型使用
   */
  vertexId?: string
  [property: string]: any
}

/**
 * Condition
 *
 * AtomicCondition或者CombinedCondition
 */
export type Condition = {
  /**
   * Atomic条件的属性
   */
  alias?: string
  /**
   * COMBINED条件的属性
   */
  conditions?: { [key: string]: any }[]
  /**
   * 公共属性
   */
  operator: string
  /**
   * Atomic条件的属性
   */
  params?: ParamDesc[]
  [property: string]: any
}

export enum FluffyType {
  Alias = 'ALIAS',
  Column = 'COLUMN',
  ConditionValue = 'CONDITION_VALUE',
  Constant = 'CONSTANT',
  Function = 'FUNCTION',
  TimeIntervalExpr = 'TIME_INTERVAL_EXPR',
}

/**
 * JoinDagDesc
 */
export type JoinDagDesc = {
  edges: Edge[]
  vertices: Vertex[]
  [property: string]: any
}

export type Edge = {
  foreignKeys: ForeignKey[]
  from: string
  id: string
  /**
   * 2024-09-05新增
   */
  joinConditions?: FilterDesc
  joinType: string
  primaryKeys: PrimaryKey[]
  properties?: { [key: string]: any }
  to: string
  [property: string]: any
}

export type ForeignKey = {
  alias?: string
  /**
   * 字段描述
   */
  comment?: string
  /**
   * 字段名称，database.table.column
   */
  name: string
  /**
   * 字段类型
   */
  type?: string
  vertexId: string
  [property: string]: any
}

export type PrimaryKey = {
  alias?: string
  /**
   * 字段描述
   */
  comment?: string
  /**
   * 字段名称，database.table.column
   */
  name: string
  /**
   * 字段类型
   */
  type?: string
  vertexId: string
  [property: string]: any
}

export type Vertex = {
  dimensionsColumns?: DimensionsColumn[]
  dummy: boolean
  /**
   * TABLE
   */
  id: string
  kind: string
  metricsColumns?: MetricsColumn[]
  table: string
  tableType?: string
  /**
   * BATCH,STREAM,BATCH_STREAM
   */
  vertexType?: string
  [property: string]: any
}

export type DimensionsColumn = {
  alias?: string
  /**
   * 字段描述
   */
  comment?: string
  /**
   * 字段名称，database.table.column
   */
  name: string
  /**
   * 字段类型
   */
  type?: string
  vertexId: string
  [property: string]: any
}

export type MetricsColumn = {
  alias?: string
  /**
   * 字段描述
   */
  comment?: string
  /**
   * 字段名称，database.table.column
   */
  name: string
  /**
   * 字段类型
   */
  type?: string
  vertexId: string
  [property: string]: any
}

/**
 * LimitDesc
 */
export type LimitDesc = {
  limit: number
  offset: number
  [property: string]: any
}

/**
 * MeasureDesc
 */
export type MeasureDesc = {
  /**
   * 别名
   */
  alias: string
  /**
   * 度量字段信息
   */
  columnDesc?: ColumnDesc
  /**
   * 备注
   */
  comment?: string
  /**
   * 是否创建指标，AskBI专用
   */
  createMetric: boolean
  /**
   * 表达式，AskBI专用
   */
  expression?: string
  /**
   * 度量格式，AskBI专用
   */
  formatTemplate: string
  /**
   * 算子
   */
  function: FunctionObject
  /**
   * 唯一标识，AskBI使用时，对应ID
   */
  name: string
  /**
   * 中文名，AskBI专用
   */
  nameZh?: string
  /**
   * 同义词列表
   */
  synonyms?: string[]
  /**
   * 单位
   */
  unit?: string
  [property: string]: any
}

/**
 * 算子
 */
export type FunctionObject = {
  function: FunctionEnum
  params: Param[]
  [property: string]: any
}

export type Param = {
  /**
   * 条件对象
   */
  condition?: FluffyCondition
  function?: { [key: string]: any }
  /**
   * 如果type是TIME_INTERVAL_EXPR时，不为空
   */
  timeIntervalExprDesc?: string
  type: FluffyType
  /**
   * 如果type是COLUMN，value为字段值；如果type是CONSTANT，value为常量
   */
  value?: string
  valueType?: ValueType
  /**
   * 主要是ER模型使用
   */
  vertexId?: string
}

/**
 * 条件对象
 */
export type FluffyCondition = {
  /**
   * AtomicCondition或者CombinedCondition
   */
  condition: Condition
  functionDesc: FunctionDesc
}

/**
 * 模型类型，2024-03-27 新增
 */
export enum ModelType {
  DetailModel = 'DETAIL_MODEL',
  MetricModel = 'METRIC_MODEL',
}

/**
 * 计算结果输出描述，2024-03-27 更新
 *
 * OutputDesc
 */
export type OutputDesc = {
  /**
   * Kafka 专用
   */
  brokers: string
  /**
   * AskDI 专用
   */
  catalog: string
  /**
   * AskDI 专用
   */
  database: string
  indexes: Index[]
  kafkaCatalogName: string
  /**
   * Kafka 专用
   */
  outputFormat: string
  /**
   * 计算结果输出目标
   */
  outputTarget: OutputTarget
  /**
   * 主键
   */
  primaryKeys: string[]
  /**
   * AskDI 专用
   */
  table: string
  /**
   * Kafka 专用
   */
  topic: string
  /**
   * 更新策略默认为NEW
   */
  updateStrategy: string
}

export type Index = {
  /**
   * 索引列
   */
  indexColumns: string[]
  [property: string]: any
}

/**
 * 计算结果输出目标
 */
export enum OutputTarget {
  Askdi = 'ASKDI',
  Kafka = 'KAFKA',
}

/**
 * 分区描述
 *
 * PartitionDesc
 */
export type PartitionDesc = {
  partitionMetas: { [key: string]: any }
}

/**
 * 资源描述
 *
 * ResourceDesc
 */
export type ResourceDesc = {
  /**
   * Core
   */
  cpu: number
  /**
   * GB
   */
  disk: number
  /**
   * MB
   */
  memory: number
}

/**
 * 排序描述
 *
 * SortDesc
 */
export type SortDesc = {
  collations: Collation[]
}

/**
 * Collation
 */
export type Collation = {
  /**
   * 列
   */
  column: ColumnDesc
  /**
   * 排序方式
   */
  order: Order
}

/**
 * 排序方式
 */
export enum Order {
  Asc = 'ASC',
}

/**
 * 流描述
 *
 * StreamingDesc
 */
export type StreamingDesc = {
  /**
   * EARLIEST,LATEST,SPECIFIC
   */
  scanStartupMode?: string
  specificTime?: number
  timeBoundary: number
  /**
   * STREAM_FIRST,BATCH_FIRST,SPECIFIC
   */
  timeBoundaryStrategy?: string
}

/**
 * TimeColumnDesc
 */
export type TimeColumnDesc = {
  /**
   * 时间列
   */
  column: ColumnDesc
  /**
   * 时间格式
   */
  formatPattern: string
  /**
   * 时间粒度
   */
  granularity?: string
  /**
   * 是否分区键
   */
  partition?: boolean
}

/**
 * 窗口描述
 *
 * WindowDesc
 */
export type WindowDesc = {
  offset?: number
  /**
   * windowType为OVER_AGG时不为空
   */
  overAggDesc?: OverAggDesc
  /**
   * windowType为TUMBLE或HOP时有效
   */
  size?: number
  /**
   * windowType为HOP时有效
   */
  slide?: number
  /**
   * windowType为TUMBLE时有效
   */
  timeUnit?: string
  /**
   * windowType为HOP时有效
   */
  timeUnitOfSlide?: string
  /**
   * windowType为TUMBLE时有效，旧版本使用
   */
  windowTimeColumn?: ColumnDesc
  /**
   * windowType为TUMBLE或HOP时有效，新版使用
   */
  windowTimeDim?: DimensionDesc
  /**
   * 窗口类型
   */
  windowType: WindowType
}

/**
 * windowType为OVER_AGG时不为空
 *
 * OverAggDesc
 */
export type OverAggDesc = {
  /**
   * 旧版使用
   */
  orderBy: ColumnDesc
  /**
   * 新版使用
   */
  orderByDim?: DimensionDesc
  /**
   * 新版使用
   */
  partitionByDims?: DimensionDesc[]
  /**
   * 旧版使用
   */
  partitionBys?: ColumnDesc[]
  rangeIntervalDesc: OverAggRangeIntervalDesc
}

/**
 * 新版使用
 *
 * DimensionDesc
 *
 * windowType为TUMBLE或HOP时有效，新版使用
 */
export type DimensionDesc = {
  /**
   * 别名
   */
  alias: string
  columnDesc: ColumnDesc
  /**
   * 备注
   */
  comment?: string
  /**
   * 维度类型，类目，默认时间，其他时间
   */
  dimensionType: string
  /**
   * 表达式，AskBI专用
   */
  expression?: string
  function: FunctionDesc
  /**
   * 时间粒度
   */
  granularity?: string
  /**
   * AskBI使用时，对应ID
   */
  name: string
  /**
   * 中文名，AskBI专用
   */
  nameZh?: string
  /**
   * 同义词列表
   */
  synonyms?: string[]
  /**
   * 时间格式
   */
  timeFormatPattern?: string
  /**
   * 单位
   */
  unit?: string
  /**
   * 码值，AskBI专用
   */
  values?: string[]
}

/**
 * OverAggRangeIntervalDesc
 */
export type OverAggRangeIntervalDesc = {
  rangeIntervalSize?: number
  rangeIntervalType: RangeIntervalType
  /**
   * rangeIntervalType为RANGE_INTERVAL时有效
   */
  rangeTimeIntervalUint?: string
  [property: string]: any
}

export enum RangeIntervalType {
  RangeInterval = 'RANGE_INTERVAL',
  RowInterval = 'ROW_INTERVAL',
}

/**
 * 窗口类型
 */
export enum WindowType {
  Hop = 'HOP',
  OverAgg = 'OVER_AGG',
  Tumble = 'TUMBLE',
}

/**
 * 物化视图类型分组，2024-02-28新增
 */
export enum TypeGroup {
  Batch = 'BATCH',
  Stream = 'STREAM',
}

/**
 * 物化视图类型，其中STREAM为流式物化，其他为批量物化
 */
export enum ViewType {
  Auto = 'AUTO',
  Custom = 'CUSTOM',
  Import = 'IMPORT',
  Load = 'LOAD',
  Recommend = 'RECOMMEND',
  Stream = 'STREAM',
  ToTable = 'TO_TABLE',
}
