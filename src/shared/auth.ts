import axios from 'axios'
import { z } from 'zod'
import tapable from 'tapable'
import { askBIApiUrls } from './url-map'

export function extractInfoFromEnforcer(encodedData: string) {
  const [type, id] = encodedData.split(':')
  return { type, id }
}

export const resourceTypeList = [
  { label: '项目', value: 'project' },
  { label: '场景', value: 'scene' },
  { label: 'LLM', value: 'llm' },
  { label: '页面', value: 'page' },
  { label: '关键字', value: 'key' },
] as const

export type ResourceTypes = (typeof resourceTypeList)[number]['value']

export const resourceTypeMap = resourceTypeList.reduce(
  (o, cur) => {
    o[cur.value] = cur
    return o
  },
  {} as Record<ResourceTypes, (typeof resourceTypeList)[number]>,
)

export const ruleTypeList = [
  { label: '用户', value: 'user' },
  { label: '角色', value: 'role' },
  // { label: '用户组', value: 'group' },
] as const

export type RuleTypes = (typeof ruleTypeList)[number]['value']

export const ruleTypeMap = ruleTypeList.reduce(
  (o, cur) => {
    o[cur.value] = cur
    return o
  },
  {} as Record<RuleTypes, (typeof ruleTypeList)[number]>,
)

export const actionOptions = [
  { label: '读', value: 'read' },
  { label: '写', value: 'write' },
] as const

export type ActionTypes = (typeof actionOptions)[number]['value']

export const actionTypeMap = actionOptions.reduce(
  (o, cur) => {
    o[cur.value] = cur
    return o
  },
  {} as Record<ActionTypes, (typeof actionOptions)[number]>,
)

export const SPLIT_CHAR = ':'

export function contactData(type: string, id: string) {
  return `${type}${SPLIT_CHAR}${id}`
}

export function contactUserData(id: string) {
  return contactData('user', id)
}

export function encodeResourceRule(type: ResourceTypes, typeData: any): string[] {
  if (Array.isArray(typeData)) {
    return typeData.map((v) => encodeResourceRule(type, v)).flat()
  }
  switch (type) {
    case 'page':
    case 'project':
    case 'llm':
    case 'scene':
    case 'key':
      return [contactData(type, typeData)]
    default:
      return []
  }
}

export const AuthAdminUserListPostRequestSchema = z
  .object({
    pageSize: z.coerce.number().min(1).readonly(),
    current: z.coerce.number().min(1).readonly(),
    name: z.string().optional().readonly(),
  })
  .readonly()

export type AuthAdminUserListPostRequest = z.infer<typeof AuthAdminUserListPostRequestSchema>

/**
 * 全局的ac管理，便于在一些特殊的场景下（比如登出）终止所有的请求
 */
export const abortControllerManager = new Set<AbortController>()

/**
 * 清除前端所有的 Cookie
 */
export function clearAllCookies() {
  // 获取所有 Cookie 的名称
  const cookies = document.cookie.split(';')

  // 遍历所有 Cookie
  cookies.forEach((cookie) => {
    const eqPos = cookie.indexOf('=')
    const name = eqPos > -1 ? cookie.substring(0, eqPos).trim() : cookie.trim()

    // 清除 Cookie，考虑域名和路径
    document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`

    // 如果有特定域名的 Cookie，需要明确指定域名进行清除
    const domainParts = window.location.hostname.split('.')
    while (domainParts.length > 0) {
      const domain = domainParts.join('.')
      document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=.${domain}`
      domainParts.shift()
    }
  })
}

export const beforeLogout = async () => {
  const lastTenantSelection = localStorage.getItem('lastTenantSelection')

  // 在退出登录前终止所有可追溯的请求，防止阻塞下面的接口
  abortControllerManager.forEach((ac) => ac.abort())
  abortControllerManager.clear()
  await axios.post(askBIApiUrls.auth.logout)
  localStorage.clear()
  sessionStorage.clear()
  clearAllCookies()

  if (lastTenantSelection !== null) {
    localStorage.setItem('lastTenantSelection', lastTenantSelection)
  }
}

// 预留，后期适配
export class AuthAdapter {
  hooks = Object.freeze({
    beforeLogin: new tapable.AsyncSeriesWaterfallHook<[any]>(['data']),
    onLogin: new tapable.AsyncSeriesWaterfallHook<[any]>(['data']),
    afterLogin: new tapable.AsyncParallelHook<[any]>(['data']),
  })
  async login(data: any) {
    data = await this.hooks.beforeLogin.promise(data)
    const result = await this.hooks.onLogin.promise(data)
    await this.hooks.afterLogin.promise(result)
    return result
  }
  async enforce(_: any) {}
  async passport(_: any) {}
}

export class BiAuthAdapter extends AuthAdapter {
  constructor() {
    super()
    this.hooks.onLogin.tapPromise(BiAuthAdapter.name, async (data) => data)
  }
}

export class RangerAuthAdapter extends AuthAdapter {
  constructor() {
    super()
    this.hooks.onLogin.tapPromise(RangerAuthAdapter.name, async (data) => data)
  }
}

export const AUTH_USER = 'v0'
export const AUTH_DOMAIN = 'v1'
export const AUTH_RESOURCE = 'v2'
export const AUTH_ACTION = 'v3'
// export const DEFAULT_DOMAIN = '/default_tenant/lyg_project_0713_1647'
export const DEFAULT_DOMAIN = '/default_tenant/default_project'

export function getInfoFromPolicy(policy: string[], index: number) {
  return policy[index]
}

export function getUserFromPolicy(policy: string[]) {
  return getInfoFromPolicy(policy, 0)
}

export function getDomainFromPolicy(policy: string[]) {
  return getInfoFromPolicy(policy, 1)
}

export function getResourceFromPolicy(policy: string[]) {
  return getInfoFromPolicy(policy, 2)
}

export function getActionFromPolicy(policy: string[]) {
  return getInfoFromPolicy(policy, 3)
}

export function getUserFromGroup(policy: string[]) {
  return getInfoFromPolicy(policy, 0)
}

export function getRoleFromGroup(policy: string[]) {
  return getInfoFromPolicy(policy, 1)
}

export function getDomainFromGroup(policy: string[]) {
  return getInfoFromPolicy(policy, 2)
}

export function getInfoFromGroup2(policy: string[]) {
  return getInfoFromPolicy(policy, 0)
}

export function getDomainFromGroup2(policy: string[]) {
  return getInfoFromPolicy(policy, 1)
}

export const TENANT_ADMIN = 'tenant-admin'
export const TENANT_USER = 'tenant-user'

export const BUILTIN_ROLES = [
  {
    label: '管理员',
    value: TENANT_ADMIN,
  },
  {
    label: '普通用户',
    value: TENANT_USER,
  },
] as const

export const BUILTIN_ROLE_VALUES = BUILTIN_ROLES.map((v) => v.value)
