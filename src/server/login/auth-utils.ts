import { NextFunction, Request, Response } from 'express'
import chalk from 'chalk'
import axios from 'axios'
import { askBIApiUrls, askBIPageUrls, loginWhitelist } from 'src/shared/url-map'
import { encryptUserId, getQueryState } from 'src/shared/common-utils'
import memoryCache from '../dao/memory-cache'
import { prisma } from '../dao/prisma-init'
import {
  getAccessResourceUrl,
  PROCESS_ENV,
  rangerLoginUrl,
  registerUserUrl,
  logoutUrl,
  checkToken,
} from '../server-constants'
import { getAllResourceListByAdmin } from '../utils'
import { enforcer, getTokenFromRanger } from '../auth'
import { AppAuth } from './api'

// const COOKIE_NAME = 'set-cookie'

export const SESSION_ID = 'JSESSIONID'
export const JWT_ID = 'jwt_token'
export const COOKIE_MAX_AGE = 24 * 60 * 60 * 1000

const rangerEnable = PROCESS_ENV.RANGER_LOGIN_ENABLE

const LLM_SERVICE_NAME = 'askbi_llm'
const DATASOURCE_SERVICE_NAME = 'askbi_datasource'
const PROJECT_SERVICE_NAME = 'askbi_project'

/** 检查是否登录 */
export async function verifyLogin(req: Request, res: Response, next: NextFunction) {
  // 忽略 /assets（js和css） 和 /img（图片）开头、白名单内的 URL
  const queryState = getQueryState(false, req.query as Record<string, string>)
  if (
    req.url.startsWith('/assets') ||
    req.url.startsWith('/img') ||
    loginWhitelist.some((whitelistedUrl) => req.url.startsWith(whitelistedUrl)) ||
    req.url.startsWith(askBIApiUrls.diProxyLogin.namespace) ||
    req.url.startsWith(askBIApiUrls.diProxyLogin.service) ||
    req.url.startsWith(askBIApiUrls.diProxyLogin.pod) ||
    req.url.startsWith(askBIApiUrls.diProxyLogin.diDefaultProxy) ||
    queryState.enableAutoLogin
  ) {
    return next()
  }

  // 如果没有 username 表示没有登录过
  if (!req.cookies.u_info && !req.cookies.JSESSIONID) {
    const authorization = req.header('authorization') as string
    if (authorization && authorization.startsWith('Bearer ') && authorization.slice(7).trim()) {
      try {
        const { isValid, username } = await verifyToken(authorization.slice(7).trim())
        if (isValid && username) {
          req.session.username = username
          return next()
        } else {
          return res.json({ code: 401, data: {}, msg: 'token失效，请重新登录' })
        }
      } catch (err: any) {
        console.error('检测token失败：', err.message)
        return res.json({ code: 401, data: {}, msg: '校验token失败，请重新登录' })
      }
    }
    // 如果是 /api 的请求，返回  401
    if (req.url.startsWith('/api')) {
      // 如果验证失败，直接return 401结束
      const domain = getSecondDomainOrIP(req.hostname)
      clearAuthCookies(req, res, domain)
      return res.json({ code: 401, data: {}, msg: '登录已过期，请重新登录' })
    } else {
      const path = req.path.includes(askBIPageUrls.login)
        ? `${askBIPageUrls.login}`
        : `${askBIPageUrls.login}?${new URLSearchParams({ ...(req.query || {}), path: req.path }).toString()}`
      // 其他的情况为页面请求，重定向到登录页
      return res.redirect(path)
    }
  } else {
    const { username: sessionUsername } = req.session
    const { u_info: cookieUsername, JSESSIONID: jsessionid } = req.cookies

    // 本地运行server重启的时候 session和memoryCache的数据会清空，所以需要在赋值一遍
    if (!sessionUsername) {
      req.session.username = cookieUsername
      memoryCache.set(String(jsessionid), cookieUsername, COOKIE_MAX_AGE)
    }

    const cachedUsername = memoryCache.get(String(cookieUsername))
    if (cachedUsername) {
      return next()
    }

    try {
      const { isValid, username } = await verifyToken(jsessionid)
      if (isValid && username) {
        req.session.username = username
        memoryCache.set(String(cookieUsername), jsessionid, COOKIE_MAX_AGE)
        return next()
      } else {
        return res.json({ code: 401, data: {}, msg: '登录态校验失败' })
      }
    } catch (error: any) {
      // const domain = getSecondDomainOrIP(req.hostname)
      // clearAuthCookies(req, res, domain)
      console.error('Error verifying session:', error.message)
      return res.json({ code: 401, data: {}, msg: '服务器内部错误，校验token失败' })
    }
  }
}

export async function verifyToken(token: string): Promise<{ isValid: boolean; username?: string; isAdmin?: boolean }> {
  try {
    const checkResult = await axios.get(`${checkToken}/${token}`)
    const username = checkResult.data.data?.userName
    const isAdmin = checkResult.data.data?.admin
    // 返回 isValid 和 username
    return {
      isValid: Boolean(username),
      username,
      isAdmin,
    }
  } catch (error: any) {
    console.error('Error verifying token:', error.message)
    throw new Error('Token verification failed')
  }
}

/** 检查是否登录，且必须为管理员 */
export async function verifyAdminLogin(req: Request, res: Response, next: NextFunction) {
  try {
    // TODO 按照历史逻辑 全都是管理员，加上浦发的控制， 只有 admin 才是管理员。以后权限管理修改完成以后 估计这里还会修改管理员的判断方式
    if (rangerEnable) {
      const CURRENT_CUSTOMER = process.env['VITE_CUSTOMER'] || ''
      if (CURRENT_CUSTOMER === 'PU_FA') {
        const authorization = req.header('authorization') as string
        if (authorization && authorization.startsWith('Bearer ') && authorization.slice(7).trim()) {
          try {
            const { isAdmin } = await verifyToken(authorization.slice(7).trim())
            //TODO(zhaoyang) Check if the user's role is administrator.
            if (isAdmin) {
              // 确保next不多次调用
              return next()
            } else {
              throw new Error('非管理员禁止访问，请联系管理员开通权限')
            }
          } catch (error: any) {
            return res.json({ code: 403, data: {}, msg: '非管理员禁止访问，请联系管理员开通权限' })
          }
        }
      }
      next()
    } else {
      const { username } = req.session

      if (!username) {
        return res.json({ code: 401, data: {}, msg: '未检测到用户名，请重新登录' })
      }

      // 关联查询用户和角色
      const userInfo = await prisma.user.findUnique({
        where: {
          username,
        },
        include: {
          userRoles: {
            where: {
              roleId: 'admin',
            },
          },
        },
      })

      if (!userInfo || !userInfo.userRoles.length) {
        return res.json({ code: 403, data: {}, msg: '非管理员禁止访问，请联系管理员开通权限' })
      }

      next()
    }
  } catch (error) {
    console.error('verifyAdminLogin-error', error)
    return res.json({ code: 500, data: {}, msg: '服务器错误，请联系管理员' })
  }
}

export async function authLogOut(token: string) {
  console.info('Logout token=' + token)
  try {
    await axios.get(logoutUrl, { headers: { Cookie: 'JSESSIONID=' + token } })
  } catch (error: any) {
    console.error('Logout error', error.message)
  }
}

export async function getLLMList(user: string) {
  return await getAccessResource(LLM_SERVICE_NAME, user)
}

// 验证域名
const isValidDomain = (domain: string) => {
  const domainRegex = /^(?!-)[A-Za-z0-9-]{1,63}(?<!-)\.(?!-)(?:[A-Za-z0-9-]{1,63}\.)*[A-Za-z]{2,6}$/
  return domainRegex.test(domain)
}

/**
 * 获取二级域名或者IP地址
 * @param {String} host IP|HostName
 * @returns secondHost| IP
 */
export const getSecondDomainOrIP = (host: string) => {
  // 特殊处理 localhost 和 0.0.0.0
  if (host === 'localhost' || host === '0.0.0.0') {
    return 'localhost'
  }

  const isIP = /^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/.test(host)
  if (isIP) {
    return host
  }

  // 检查是否是有效的域名
  if (isValidDomain(host)) {
    const domainArr = host.split('.')
    if (domainArr.length > 2) {
      return domainArr.slice(1).join('.')
    }
    return host
  }
  // 如果都不是，返回一个默认值或者抛出错误
  throw new Error('无效的hostname')
}

export async function getProjectList(user: string) {
  const allProjectList = await getAllResourceListByAdmin(String(user), 'Project')
  const projectIds = allProjectList.map((item) => item.semanticProjectId)
  const projectList = await prisma.semanticProject.findMany({
    where: {
      id: {
        in: projectIds,
      },
    },
    select: {
      id: true,
      name: true,
    },
  })

  const userProjectList = await getAccessResource(PROJECT_SERVICE_NAME, user)
  const filed = projectList.filter((item) => userProjectList?.includes(item.name)).map((item) => item.id)
  return allProjectList.filter((item) => filed.includes(item.id))
}

export async function getDatasourceList(user: string) {
  const allDatasourceList = await getAllResourceListByAdmin(user, 'Datasource')
  const datasourceIds = allDatasourceList.map((item) => item.id)
  const datasourceList = prisma.datasource.findMany({
    where: {
      id: {
        in: datasourceIds,
      },
    },
    select: {
      id: true,
      name: true,
    },
  })
  const userDatasourceList = await getAccessResource(DATASOURCE_SERVICE_NAME, user)
  const fileted = (await datasourceList)
    .filter((item) => userDatasourceList?.includes(item.name))
    .map((item) => item.id)
  return allDatasourceList.filter((item) => fileted.includes(item.datasourceId))
}

export async function registerRangerUser(thUserId: string) {
  const addUserData = {
    password: thUserId,
    username: thUserId,
  }
  const response = await axios.post(registerUserUrl, addUserData, {
    headers: {
      'Content-Type': 'application/json',
    },
  })
  const httpStatus = response.status
  if (httpStatus !== 200) {
    console.error(`Request ranger register error user = ${thUserId}.`)
    throw new Error(`Request ranger login failed, status = ${httpStatus} user = ${thUserId}.`)
  }
  return await loginGetToken(thUserId, thUserId)
}

async function loginGetToken(username: string, password: string, authToken?: string) {
  const buffer = Buffer.from(password)
  const base64Encoded = buffer.toString('base64')
  const jsonData = {
    userName: username,
    password: base64Encoded,
  }
  // Call ranger user login.
  return await axios
    .post(rangerLoginUrl, jsonData, {
      headers: {
        'unique-token': authToken, // 将 token 添加到 header
      },
    })
    .then((response) => {
      const res = response.data
      if (res.code === 0) {
        return res.data
      } else {
        console.error('Login with range does not have cookie.')
        return null
      }
    })
    .catch((error) => {
      if (error.response) {
        if (error.response.code === 273) {
          throw new Error('账号或密码错误')
        }
        throw new Error(error.response.data.msgDesc)
      } else {
        console.error('Server error.', error.message)
        throw new Error(error.message === '请求包含非法字符' ? error.message : 'Server error.')
      }
    })
}

export async function getAccessResource(serviceName: string, user: string) {
  try {
    const urlFormatted = getAccessResourceUrl + user + '/' + serviceName
    console.info(chalk.yellow('GET Access Resource URL: ', urlFormatted))
    const response = await axios.get(urlFormatted)
    const allowedResources: string[] = []
    const pageAccessResult = response.data.data
    if (pageAccessResult == null || pageAccessResult.length === 0) {
      return allowedResources
    }
    pageAccessResult.forEach((item: { allowed: boolean; path: string }) => {
      if (item.allowed && item.path !== '/') {
        allowedResources.push(item.path.split('/')[1])
      }
    })
    return allowedResources
  } catch (error: any) {
    console.error('Get access resource error:', error.message, error.response?.data)
  }
}

// 封装清除Cookies的函数
export async function clearAuthCookies(req: Request, res: Response, domain: string) {
  try {
    //   TODO merge
    const jsessionid = req.cookies.JSESSIONID
    req.session.username = undefined
    const cookieOptions = {
      domain: domain,
      path: '/',
      httpOnly: true,
    }

    res.clearCookie('u_token', cookieOptions)
    res.clearCookie('u_info', cookieOptions)
    res.clearCookie('bw_org_code', cookieOptions)
    res.clearCookie(SESSION_ID, cookieOptions)
    res.clearCookie(JWT_ID, cookieOptions)
    jsessionid && (await authLogOut(jsessionid))
  } catch (error: any) {
    console.error('clearAuthCookies - error', error.message)
  }
}

export async function createUserInRanger(
  username: string,
  firstName: string,
  appAuth: AppAuth,
): Promise<{ username: string; token: string; jwtToken: string }> {
  const password = '#PASS22ds.322#'
  const reqData = {
    username,
    password,
    firstName: firstName,
    projects: appAuth.projectNames,
    llmList: appAuth.defaultLlms,
    groups: appAuth.rangerGroups,
  }
  console.info('createUserInRanger-调用 ranger 创建用户 ====', reqData)
  const result = await axios
    .post(PROCESS_ENV.AUTH_LOGIN_HOST + '/api/auth/createAndAuthorize', reqData)
    .catch((error) => {
      console.error('createUserInRanger - error', error.message)
      throw new Error(error.message)
    })
  const authorizeResult = result.data.data
  console.info('createUserInRanger--authorizeResult', authorizeResult)
  const userInfo = authorizeResult?.jwtToken ? authorizeResult : await getTokenFromRanger(username, password)
  console.info('createUserInRanger--userInfo', userInfo)
  return { ...authorizeResult, jwtTokenToken: userInfo.jwtToken }
}

export async function createUserInNode(
  username: string,
  nickname: string,
  appAuth: AppAuth,
): Promise<{ username: string; token: string; jwtToken: string }> {
  const tag = PROCESS_ENV.BI_AES_KEY || ''
  const code = PROCESS_ENV.BI_AES_IV || ''

  const password = '#PASS22ds.322#'
  const reqData = {
    username,
    password: encryptUserId(password, tag, code),
    roleNames: appAuth.rangerGroups,
    rangerUsername: username,
    rangerPassword: encryptUserId(password, tag, code),
    groups: [],
    nickname: nickname || username, // 如果nickname为空，则使用username
    projects: appAuth.projectNames,
    llmList: appAuth.defaultLlms,
  }
  const result = await createUser(reqData)
  const finalResult = result as any
  console.info('createUserInNode-finalResult', finalResult)
  return finalResult
}

export async function createUser(params: any) {
  const { username, password, roles, groups, nickname, roleNames, groupNames, rangerUsername, rangerPassword } = params
  const isExist = await prisma.xUser.findFirst({ where: { username } })
  if (isExist) {
    return await prisma.xUser.update({
      data: {
        username,
        password,
        nickname,
        rangerUsername,
        rangerPassword,
      },
      where: {
        id: isExist.id,
      },
    })
  }
  const user = await prisma.xUser.create({
    data: {
      username,
      password,
      nickname,
      rangerUsername,
      rangerPassword,
    },
  })
  if (Array.isArray(roles)) {
    await enforcer.unionUserRole({ user: { id: user.id, type: 'user' }, role: { type: 'roles', ids: roles } })
  }
  if (Array.isArray(groups)) {
    await enforcer.unionUserRole({ user: { id: user.id, type: 'user' }, role: { type: 'groups', ids: groups } })
  }
  if (Array.isArray(roleNames)) {
    const idListFromNames = (
      await prisma.xRole.findMany({ where: { roleName: { in: roleNames } }, select: { id: true } })
    ).map((v) => v.id)
    await enforcer.unionUserRole({
      user: { id: user.id, type: 'user' },
      role: { type: 'roles', ids: idListFromNames },
    })
  }
  if (Array.isArray(groupNames)) {
    const idListFromNames = (
      await prisma.xGroup.findMany({ where: { groupName: { in: groupNames } }, select: { id: true } })
    ).map((v) => v.id)
    await enforcer.unionUserRole({
      user: { id: user.id, type: 'user' },
      role: { type: 'groups', ids: idListFromNames },
    })
  }

  return user
}
