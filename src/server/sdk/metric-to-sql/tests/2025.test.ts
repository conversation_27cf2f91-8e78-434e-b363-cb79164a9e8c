/* eslint-disable no-console */
import { describe, it } from 'vitest'
import { QueryParams } from 'src/shared/metric-types'
import { MetricToSql } from '../metric-to-sql'
import { metricConfigRecord } from './baowu'
import { compare } from './utils'

// npx cross-env NODE_ENV=test vitest watch src/server/sdk/metric-to-sql/tests/2025.test.ts

describe('非total情况下 求不可累加指标 mode=max', async () => {
  it('宝武共享1月份的 两金情况', async () => {
    const metricToSql = new MetricToSql({
      metricConfig: metricConfigRecord['chvkZ9hCPz0xd6HC'],
      version: '2025',
    })
    const queryParams = {
      extraInfo: {
        groupbys_with_level: [
          {
            groupByLevel: 0,
            groupBys: 'COMPANY_INNER_CODE_DES',
          },
        ],
        is_sub: false,
        metric_ner: ['资产负债率'],
        metric_scores: {
          SUMIA070040: 1,
        },
        timeQueryType: '年',
        where_ner: ['宝地资产'],
      },
      groupBys: ['COMPANY_INNER_CODE_DES'],
      isMetricNamesExactMatch: true,
      isWhereExactMatch: true,
      limit: null,
      metricNames: ['SUMIA070040'],
      notExistGroupBys: null,
      notExistMetricNames: null,
      notExistOrderBys: null,
      orderBys: ['V_DATE_ ASC'],
      originGroupBys: ['COMPANY_INNER_CODE_DES'],
      originWhere: "COMPANY_INNER_CODE_DES IN ('上海宝地不动产资产管理有限公司-管理合并')",
      timeQueryParams: {
        timeDimensionName: null,
        timeEndFunction: {
          day: 31,
          month: 12,
          quarter: null,
          type: 'specificDate',
          year: 2024,
        },
        timeGranularity: 'year',
        timeStartFunction: {
          day: 1,
          month: 1,
          quarter: null,
          type: 'specificDate',
          year: 2022,
        },
      },
      where: "COMPANY_INNER_CODE_DES IN ('上海宝地不动产资产管理有限公司-管理合并')",
    } as any as QueryParams

    const { sql: currentSql } = await metricToSql.toSql(queryParams)
    const expectSql = `
with      SNAPSHOT_LATEST_TIME_TBL as (
          select    SUBSTRING(
                    dipeak.baowu_data.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO,
                    1,
                    4
                    ) as V_DATE_,
                    T_ADS_FACT_WSSJ_TOTAL_INDEX.COMPANY_INNER_CODE_DES as COMPANY_INNER_CODE_DES,
                    max(
                    dipeak.baowu_data.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO
                    ) as LAST_DATE
          from      dipeak.baowu_data.T_ADS_FACT_WSSJ_TOTAL_INDEX
          where     SUBSTRING(
                    dipeak.baowu_data.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO,
                    1,
                    4
                    ) >= '2022'
          AND       SUBSTRING(
                    dipeak.baowu_data.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO,
                    1,
                    4
                    ) <= '2024'
          and       SUBSTR (
                    dipeak.baowu_data.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO,
                    5,
                    2
                    ) BETWEEN '01' AND '12'
          and       (
                    T_ADS_FACT_WSSJ_TOTAL_INDEX.COMPANY_INNER_CODE_DES IN ('上海宝地不动产资产管理有限公司-管理合并')
                    )
          group by  V_DATE_,
                    COMPANY_INNER_CODE_DES
          )
select    SNAPSHOT_LATEST_TIME_TBL.V_DATE_ as V_DATE_,
          SNAPSHOT_LATEST_TIME_TBL.COMPANY_INNER_CODE_DES as COMPANY_INNER_CODE_DES,
          sum(
          (
          CASE
                    WHEN REPORT_ITEM = 'IA070040' then month_amt
                    ELSE NULL
          END
          )
          ) as SUMIA070040
from      SNAPSHOT_LATEST_TIME_TBL
join      dipeak.baowu_data.T_ADS_FACT_WSSJ_TOTAL_INDEX on dipeak.baowu_data.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO = SNAPSHOT_LATEST_TIME_TBL.LAST_DATE
and       SUBSTRING(
          dipeak.baowu_data.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO,
          1,
          4
          ) = SNAPSHOT_LATEST_TIME_TBL.V_DATE_
and       T_ADS_FACT_WSSJ_TOTAL_INDEX.COMPANY_INNER_CODE_DES = SNAPSHOT_LATEST_TIME_TBL.COMPANY_INNER_CODE_DES
where     SUBSTRING(
          dipeak.baowu_data.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO,
          1,
          4
          ) >= '2022'
AND       SUBSTRING(
          dipeak.baowu_data.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO,
          1,
          4
          ) <= '2024'
and       SUBSTR (
          dipeak.baowu_data.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO,
          5,
          2
          ) BETWEEN '01' AND '12'
and       (
          T_ADS_FACT_WSSJ_TOTAL_INDEX.COMPANY_INNER_CODE_DES IN ('上海宝地不动产资产管理有限公司-管理合并')
          )
group by  V_DATE_,
          COMPANY_INNER_CODE_DES
order by  V_DATE_ ASC`.trim()
    await compare({
      currentSql,
      expectSql,
      oldSql:
        "WITH DIM_TBL AS (\n SELECT DISTINCT SUBSTRING(dipeak.baowu_data.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO, 1, 4) AS V_DATE_, T_ADS_FACT_WSSJ_TOTAL_INDEX.COMPANY_INNER_CODE_DES AS COMPANY_INNER_CODE_DES FROM dipeak.baowu_data.T_ADS_FACT_WSSJ_TOTAL_INDEX WHERE SUBSTRING(dipeak.baowu_data.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO, 1, 4) >= '2022' AND SUBSTRING(dipeak.baowu_data.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO, 1, 4) <= '2024' AND (T_ADS_FACT_WSSJ_TOTAL_INDEX.COMPANY_INNER_CODE_DES IN ('上海宝地不动产资产管理有限公司-管理合并'))\n), SUMIA070040_TBL AS (\n SELECT sum(t_ads_fact_wssj_total_index.month_amt) as SUMIA070040, SUBSTRING(dipeak.baowu_data.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO, 1, 4) AS V_DATE_, T_ADS_FACT_WSSJ_TOTAL_INDEX.COMPANY_INNER_CODE_DES AS COMPANY_INNER_CODE_DES FROM dipeak.baowu_data.T_ADS_FACT_WSSJ_TOTAL_INDEX WHERE SUBSTRING(dipeak.baowu_data.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO, 1, 4) >= '2022' AND SUBSTRING(dipeak.baowu_data.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO, 1, 4) <= '2024' AND (T_ADS_FACT_WSSJ_TOTAL_INDEX.COMPANY_INNER_CODE_DES IN ('上海宝地不动产资产管理有限公司-管理合并')) AND REPORT_ITEM = 'IA070040' GROUP BY V_DATE_, COMPANY_INNER_CODE_DES\n)\nSELECT DIM_TBL.V_DATE_, DIM_TBL.COMPANY_INNER_CODE_DES, SUMIA070040_TBL.SUMIA070040 FROM DIM_TBL LEFT JOIN SUMIA070040_TBL ON DIM_TBL.COMPANY_INNER_CODE_DES = SUMIA070040_TBL.COMPANY_INNER_CODE_DES AND DIM_TBL.V_DATE_ = SUMIA070040_TBL.V_DATE_ ORDER BY V_DATE_ ASC",
    })
  })

  it('集团各年资产负债率', async () => {
    const metricToSql = new MetricToSql({
      metricConfig: metricConfigRecord['fS0U86Sz9l2k3AH7'],
      version: '2025',
    })
    const queryParams = {
      metricNames: ['SUMIA070040'],
      groupBys: ['COMPANY_INNER_CODE_DES'],
      originGroupBys: ['COMPANY_INNER_CODE_DES'],
      where: "COMPANY_INNER_CODE_DES IN ('中国宝武钢铁集团有限公司-法人','中国宝武钢铁集团有限公司-资产合并')",
      originWhere: "COMPANY_INNER_CODE_DES IN ('中国宝武钢铁集团有限公司-资产合并')",
      orderBys: ['V_DATE_ ASC'],
      limit: null,
      timeQueryParams: {
        timeStartFunction: {
          type: 'specificDate',
          year: 2015,
          month: 1,
          day: 1,
          quarter: null,
        },
        timeEndFunction: {
          type: 'specificDate',
          year: 2025,
          month: 7,
          day: 14,
          quarter: null,
        },
        timeGranularity: 'year',
        timeDimensionName: null,
      },
      isMetricNamesExactMatch: true,
      isWhereExactMatch: false,
      extraInfo: {
        is_sub: false,
        groupbys_with_level: [
          {
            groupBys: 'COMPANY_INNER_CODE_DES',
            groupByLevel: 0,
          },
        ],
        metric_ner: ['资产负债率'],
        where_ner: ['集团'],
        timeQueryType: '年',
        metric_scores: {
          SUMIA070040: 1,
        },
      },
      notExistMetricNames: null,
      notExistGroupBys: null,
      notExistOrderBys: null,
    } as any as QueryParams

    const { sql: currentSql } = await metricToSql.toSql(queryParams)
    const expectSql = `
with      SNAPSHOT_LATEST_TIME_TBL as (
          select    SUBSTRING(
                    dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO,
                    1,
                    4
                    ) as V_DATE_,
                    T_ADS_FACT_WSSJ_TOTAL_INDEX.COMPANY_INNER_CODE_DES as COMPANY_INNER_CODE_DES,
                    max(
                    dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO
                    ) as LAST_DATE
          from      dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX
          where     SUBSTRING(
                    dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO,
                    1,
                    4
                    ) >= '2015'
          AND       SUBSTRING(
                    dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO,
                    1,
                    4
                    ) <= '2025'
          and       SUBSTR (
                    dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO,
                    5,
                    2
                    ) BETWEEN '01' AND '12'
          and       (
                    T_ADS_FACT_WSSJ_TOTAL_INDEX.COMPANY_INNER_CODE_DES IN ('中国宝武钢铁集团有限公司-法人', '中国宝武钢铁集团有限公司-资产合并')
                    )
          group by  V_DATE_,
                    COMPANY_INNER_CODE_DES
          )
select    SNAPSHOT_LATEST_TIME_TBL.V_DATE_ as V_DATE_,
          SNAPSHOT_LATEST_TIME_TBL.COMPANY_INNER_CODE_DES as COMPANY_INNER_CODE_DES,
          sum(
          (
          CASE
                    WHEN REPORT_ITEM = 'IA070040' then month_amt
                    ELSE NULL
          END
          )
          ) as SUMIA070040
from      SNAPSHOT_LATEST_TIME_TBL
join      dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX on dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO = SNAPSHOT_LATEST_TIME_TBL.LAST_DATE
and       SUBSTRING(
          dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO,
          1,
          4
          ) = SNAPSHOT_LATEST_TIME_TBL.V_DATE_
and       T_ADS_FACT_WSSJ_TOTAL_INDEX.COMPANY_INNER_CODE_DES = SNAPSHOT_LATEST_TIME_TBL.COMPANY_INNER_CODE_DES
where     SUBSTRING(
          dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO,
          1,
          4
          ) >= '2015'
AND       SUBSTRING(
          dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO,
          1,
          4
          ) <= '2025'
and       SUBSTR (
          dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO,
          5,
          2
          ) BETWEEN '01' AND '12'
and       (
          T_ADS_FACT_WSSJ_TOTAL_INDEX.COMPANY_INNER_CODE_DES IN ('中国宝武钢铁集团有限公司-法人', '中国宝武钢铁集团有限公司-资产合并')
          )
group by  V_DATE_,
          COMPANY_INNER_CODE_DES
order by  V_DATE_ ASC`.trim()

    await compare({
      currentSql,
      expectSql,
      oldSql:
        "WITH DIM_TBL AS (\n  SELECT DISTINCT SUBSTRING(dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO, 1, 4) AS V_DATE_, T_ADS_FACT_WSSJ_TOTAL_INDEX.COMPANY_INNER_CODE_DES AS COMPANY_INNER_CODE_DES FROM dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX WHERE SUBSTRING(dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO, 1, 4) >= '2015' AND SUBSTRING(dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO, 1, 4) <= '2025' AND (T_ADS_FACT_WSSJ_TOTAL_INDEX.COMPANY_INNER_CODE_DES IN ('中国宝武钢铁集团有限公司-资产合并'))\n), SUMIA070040_TBL AS (\n  SELECT sum(t_ads_fact_wssj_total_index.month_amt) as SUMIA070040, SUBSTRING(dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO, 1, 4) AS V_DATE_, T_ADS_FACT_WSSJ_TOTAL_INDEX.COMPANY_INNER_CODE_DES AS COMPANY_INNER_CODE_DES FROM dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX WHERE SUBSTRING(dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO, 1, 4) >= '2015' AND SUBSTRING(dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO, 1, 4) <= '2025' AND (T_ADS_FACT_WSSJ_TOTAL_INDEX.COMPANY_INNER_CODE_DES IN ('中国宝武钢铁集团有限公司-资产合并')) AND REPORT_ITEM = 'IA070040' GROUP BY V_DATE_, COMPANY_INNER_CODE_DES\n)\nSELECT DIM_TBL.V_DATE_, DIM_TBL.COMPANY_INNER_CODE_DES, SUMIA070040_TBL.SUMIA070040 FROM DIM_TBL LEFT JOIN SUMIA070040_TBL ON DIM_TBL.COMPANY_INNER_CODE_DES = SUMIA070040_TBL.COMPANY_INNER_CODE_DES AND DIM_TBL.V_DATE_ = SUMIA070040_TBL.V_DATE_ ORDER BY V_DATE_ ASC",
    })
  })
})

describe('非total情况下 求不可累加指标 mode=fixed', async () => {
  it('宝武共享1月份的 两金情况', async () => {
    const metricToSql = new MetricToSql({
      metricConfig: metricConfigRecord['chvkZ9hCPz0xd6HC'],
      version: '2025',
      snapshotDateMode: 'fixed',
    })
    const queryParams = {
      extraInfo: {
        groupbys_with_level: [
          {
            groupByLevel: 0,
            groupBys: 'COMPANY_INNER_CODE_DES',
          },
        ],
        is_sub: false,
        metric_ner: ['资产负债率'],
        metric_scores: {
          SUMIA070040: 1,
        },
        timeQueryType: '年',
        where_ner: ['宝地资产'],
      },
      groupBys: ['COMPANY_INNER_CODE_DES'],
      isMetricNamesExactMatch: true,
      isWhereExactMatch: true,
      limit: null,
      metricNames: ['SUMIA070040'],
      notExistGroupBys: null,
      notExistMetricNames: null,
      notExistOrderBys: null,
      orderBys: ['V_DATE_ ASC'],
      originGroupBys: ['COMPANY_INNER_CODE_DES'],
      originWhere: "COMPANY_INNER_CODE_DES IN ('上海宝地不动产资产管理有限公司-管理合并')",
      timeQueryParams: {
        timeDimensionName: null,
        timeEndFunction: {
          day: 31,
          month: 12,
          quarter: null,
          type: 'specificDate',
          year: 2024,
        },
        timeGranularity: 'year',
        timeStartFunction: {
          day: 1,
          month: 1,
          quarter: null,
          type: 'specificDate',
          year: 2022,
        },
      },
      where: "COMPANY_INNER_CODE_DES IN ('上海宝地不动产资产管理有限公司-管理合并')",
    } as any as QueryParams

    const { sql: currentSql } = await metricToSql.toSql(queryParams)
    const expectSql = `
select    SUBSTRING(
          dipeak.baowu_data.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO,
          1,
          4
          ) as V_DATE_,
          T_ADS_FACT_WSSJ_TOTAL_INDEX.COMPANY_INNER_CODE_DES as COMPANY_INNER_CODE_DES,
          sum(
          (
          CASE
                    WHEN REPORT_ITEM = 'IA070040' then month_amt
                    ELSE NULL
          END
          )
          ) as SUMIA070040
from      dipeak.baowu_data.T_ADS_FACT_WSSJ_TOTAL_INDEX
where     SUBSTRING(
          dipeak.baowu_data.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO,
          1,
          4
          ) >= '2022'
AND       SUBSTRING(
          dipeak.baowu_data.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO,
          1,
          4
          ) <= '2024'
and       SUBSTR (
          dipeak.baowu_data.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO,
          5,
          2
          ) BETWEEN '01' AND '12'
and       (
          T_ADS_FACT_WSSJ_TOTAL_INDEX.COMPANY_INNER_CODE_DES IN ('上海宝地不动产资产管理有限公司-管理合并')
          )
and       dipeak.baowu_data.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO IN ('202212', '202312', '202412')
group by  V_DATE_,
          COMPANY_INNER_CODE_DES
order by  V_DATE_ ASC`.trim()
    await compare({
      currentSql,
      expectSql,
      oldSql:
        "WITH DIM_TBL AS (\n SELECT DISTINCT SUBSTRING(dipeak.baowu_data.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO, 1, 4) AS V_DATE_, T_ADS_FACT_WSSJ_TOTAL_INDEX.COMPANY_INNER_CODE_DES AS COMPANY_INNER_CODE_DES FROM dipeak.baowu_data.T_ADS_FACT_WSSJ_TOTAL_INDEX WHERE SUBSTRING(dipeak.baowu_data.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO, 1, 4) >= '2022' AND SUBSTRING(dipeak.baowu_data.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO, 1, 4) <= '2024' AND (T_ADS_FACT_WSSJ_TOTAL_INDEX.COMPANY_INNER_CODE_DES IN ('上海宝地不动产资产管理有限公司-管理合并'))\n), SUMIA070040_TBL AS (\n SELECT sum(t_ads_fact_wssj_total_index.month_amt) as SUMIA070040, SUBSTRING(dipeak.baowu_data.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO, 1, 4) AS V_DATE_, T_ADS_FACT_WSSJ_TOTAL_INDEX.COMPANY_INNER_CODE_DES AS COMPANY_INNER_CODE_DES FROM dipeak.baowu_data.T_ADS_FACT_WSSJ_TOTAL_INDEX WHERE SUBSTRING(dipeak.baowu_data.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO, 1, 4) >= '2022' AND SUBSTRING(dipeak.baowu_data.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO, 1, 4) <= '2024' AND (T_ADS_FACT_WSSJ_TOTAL_INDEX.COMPANY_INNER_CODE_DES IN ('上海宝地不动产资产管理有限公司-管理合并')) AND REPORT_ITEM = 'IA070040' GROUP BY V_DATE_, COMPANY_INNER_CODE_DES\n)\nSELECT DIM_TBL.V_DATE_, DIM_TBL.COMPANY_INNER_CODE_DES, SUMIA070040_TBL.SUMIA070040 FROM DIM_TBL LEFT JOIN SUMIA070040_TBL ON DIM_TBL.COMPANY_INNER_CODE_DES = SUMIA070040_TBL.COMPANY_INNER_CODE_DES AND DIM_TBL.V_DATE_ = SUMIA070040_TBL.V_DATE_ ORDER BY V_DATE_ ASC",
    })
  })

  it('集团各年资产负债率', async () => {
    const metricToSql = new MetricToSql({
      metricConfig: metricConfigRecord['fS0U86Sz9l2k3AH7'],
      version: '2025',
      snapshotDateMode: 'fixed',
    })
    const queryParams = {
      metricNames: ['SUMIA070040'],
      groupBys: ['COMPANY_INNER_CODE_DES'],
      originGroupBys: ['COMPANY_INNER_CODE_DES'],
      where: "COMPANY_INNER_CODE_DES IN ('中国宝武钢铁集团有限公司-法人','中国宝武钢铁集团有限公司-资产合并')",
      originWhere: "COMPANY_INNER_CODE_DES IN ('中国宝武钢铁集团有限公司-资产合并')",
      orderBys: ['V_DATE_ ASC'],
      limit: null,
      timeQueryParams: {
        timeStartFunction: {
          type: 'specificDate',
          year: 2015,
          month: 1,
          day: 1,
          quarter: null,
        },
        timeEndFunction: {
          type: 'specificDate',
          year: 2025,
          month: 7,
          day: 14,
          quarter: null,
        },
        timeGranularity: 'year',
        timeDimensionName: null,
      },
      isMetricNamesExactMatch: true,
      isWhereExactMatch: false,
      extraInfo: {
        is_sub: false,
        groupbys_with_level: [
          {
            groupBys: 'COMPANY_INNER_CODE_DES',
            groupByLevel: 0,
          },
        ],
        metric_ner: ['资产负债率'],
        where_ner: ['集团'],
        timeQueryType: '年',
        metric_scores: {
          SUMIA070040: 1,
        },
      },
      notExistMetricNames: null,
      notExistGroupBys: null,
      notExistOrderBys: null,
    } as any as QueryParams

    const { sql: currentSql } = await metricToSql.toSql(queryParams)
    const expectSql = `
select    SUBSTRING(
          dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO,
          1,
          4
          ) as V_DATE_,
          T_ADS_FACT_WSSJ_TOTAL_INDEX.COMPANY_INNER_CODE_DES as COMPANY_INNER_CODE_DES,
          sum(
          (
          CASE
                    WHEN REPORT_ITEM = 'IA070040' then month_amt
                    ELSE NULL
          END
          )
          ) as SUMIA070040
from      dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX
where     SUBSTRING(
          dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO,
          1,
          4
          ) >= '2015'
AND       SUBSTRING(
          dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO,
          1,
          4
          ) <= '2025'
and       SUBSTR (
          dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO,
          5,
          2
          ) BETWEEN '01' AND '12'
and       (
          T_ADS_FACT_WSSJ_TOTAL_INDEX.COMPANY_INNER_CODE_DES IN ('中国宝武钢铁集团有限公司-法人', '中国宝武钢铁集团有限公司-资产合并')
          )
and       dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO IN (
          '201512',
          '201612',
          '201712',
          '201812',
          '201912',
          '202012',
          '202112',
          '202212',
          '202312',
          '202412',
          '202507'
          )
group by  V_DATE_,
          COMPANY_INNER_CODE_DES
order by  V_DATE_ ASC`.trim()
    await compare({
      currentSql,
      expectSql,
      oldSql:
        "WITH DIM_TBL AS (\n  SELECT DISTINCT SUBSTRING(dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO, 1, 4) AS V_DATE_, T_ADS_FACT_WSSJ_TOTAL_INDEX.COMPANY_INNER_CODE_DES AS COMPANY_INNER_CODE_DES FROM dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX WHERE SUBSTRING(dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO, 1, 4) >= '2015' AND SUBSTRING(dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO, 1, 4) <= '2025' AND (T_ADS_FACT_WSSJ_TOTAL_INDEX.COMPANY_INNER_CODE_DES IN ('中国宝武钢铁集团有限公司-资产合并'))\n), SUMIA070040_TBL AS (\n  SELECT sum(t_ads_fact_wssj_total_index.month_amt) as SUMIA070040, SUBSTRING(dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO, 1, 4) AS V_DATE_, T_ADS_FACT_WSSJ_TOTAL_INDEX.COMPANY_INNER_CODE_DES AS COMPANY_INNER_CODE_DES FROM dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX WHERE SUBSTRING(dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO, 1, 4) >= '2015' AND SUBSTRING(dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO, 1, 4) <= '2025' AND (T_ADS_FACT_WSSJ_TOTAL_INDEX.COMPANY_INNER_CODE_DES IN ('中国宝武钢铁集团有限公司-资产合并')) AND REPORT_ITEM = 'IA070040' GROUP BY V_DATE_, COMPANY_INNER_CODE_DES\n)\nSELECT DIM_TBL.V_DATE_, DIM_TBL.COMPANY_INNER_CODE_DES, SUMIA070040_TBL.SUMIA070040 FROM DIM_TBL LEFT JOIN SUMIA070040_TBL ON DIM_TBL.COMPANY_INNER_CODE_DES = SUMIA070040_TBL.COMPANY_INNER_CODE_DES AND DIM_TBL.V_DATE_ = SUMIA070040_TBL.V_DATE_ ORDER BY V_DATE_ ASC",
    })
  })

  it('场景颗粒度日，查询颗粒度月', async () => {
    const metricToSql = new MetricToSql({
      metricConfig: metricConfigRecord['c_ompZrkNw2QNBQgzRRRI'],
      version: '2025',
      snapshotDateMode: 'fixed',
    })
    const queryParams = {
      metricNames: ['CB00JD0010'],
      groupBys: ['COMPANY_INNER_CODE_DES'],
      originGroupBys: ['COMPANY_INNER_CODE_DES'],
      where: "COMPANY_INNER_CODE_DES IN ('中国宝武钢铁集团有限公司-法人','中国宝武钢铁集团有限公司-资产合并')",
      originWhere: "COMPANY_INNER_CODE_DES IN ('中国宝武钢铁集团有限公司-资产合并')",
      orderBys: ['V_DATE_ ASC'],
      limit: null,
      timeQueryParams: {
        timeStartFunction: {
          type: 'specificDate',
          year: 2015,
          month: 1,
          day: 1,
          quarter: null,
        },
        timeEndFunction: {
          type: 'specificDate',
          year: 2025,
          month: 7,
          day: 14,
          quarter: null,
        },
        timeGranularity: 'month',
        timeDimensionName: null,
      },
      isMetricNamesExactMatch: true,
      isWhereExactMatch: false,
      extraInfo: {
        is_sub: false,
        groupbys_with_level: [
          {
            groupBys: 'COMPANY_INNER_CODE_DES',
            groupByLevel: 0,
          },
        ],
        metric_ner: ['资产负债率'],
        where_ner: ['集团'],
        timeQueryType: '年',
        metric_scores: {
          SUMIA070040: 1,
        },
      },
      notExistMetricNames: null,
      notExistGroupBys: null,
      notExistOrderBys: null,
    } as any as QueryParams

    const { sql: currentSql } = await metricToSql.toSql(queryParams)
    const expectSql = `
select    SUBSTRING(
          dipeak.baowu_data.T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.ACCT_PERIOD_NO,
          1,
          6
          ) as V_DATE_,
          T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.COMPANY_INNER_CODE_DES as COMPANY_INNER_CODE_DES,
          sum(
          (
          CASE
                    WHEN REPORT_ITEM IN ('CB00JD0010') then month_amt
                    ELSE NULL
          END
          )
          ) as CB00JD0010
from      dipeak.baowu_data.T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417
where     SUBSTRING(
          dipeak.baowu_data.T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.ACCT_PERIOD_NO,
          1,
          6
          ) >= '201501'
AND       SUBSTRING(
          dipeak.baowu_data.T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.ACCT_PERIOD_NO,
          1,
          6
          ) <= '202507'
and       SUBSTR (
          dipeak.baowu_data.T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.ACCT_PERIOD_NO,
          5,
          2
          ) BETWEEN '01' AND '12'
and       (
          T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.COMPANY_INNER_CODE_DES IN ('中国宝武钢铁集团有限公司-法人', '中国宝武钢铁集团有限公司-资产合并')
AND       T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.GRANULARITY = 'month'
          )
and       dipeak.baowu_data.T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.ACCT_PERIOD_NO IN (
          '20150131',
          '20150228',
          '20150331',
          '20150430',
          '20150531',
          '20150630',
          '20150731',
          '20150831',
          '20150930',
          '20151031',
          '20151130',
          '20151231',
          '20160131',
          '20160229',
          '20160331',
          '20160430',
          '20160531',
          '20160630',
          '20160731',
          '20160831',
          '20160930',
          '20161031',
          '20161130',
          '20161231',
          '20170131',
          '20170228',
          '20170331',
          '20170430',
          '20170531',
          '20170630',
          '20170731',
          '20170831',
          '20170930',
          '20171031',
          '20171130',
          '20171231',
          '20180131',
          '20180228',
          '20180331',
          '20180430',
          '20180531',
          '20180630',
          '20180731',
          '20180831',
          '20180930',
          '20181031',
          '20181130',
          '20181231',
          '20190131',
          '20190228',
          '20190331',
          '20190430',
          '20190531',
          '20190630',
          '20190731',
          '20190831',
          '20190930',
          '20191031',
          '20191130',
          '20191231',
          '20200131',
          '20200229',
          '20200331',
          '20200430',
          '20200531',
          '20200630',
          '20200731',
          '20200831',
          '20200930',
          '20201031',
          '20201130',
          '20201231',
          '20210131',
          '20210228',
          '20210331',
          '20210430',
          '20210531',
          '20210630',
          '20210731',
          '20210831',
          '20210930',
          '20211031',
          '20211130',
          '20211231',
          '20220131',
          '20220228',
          '20220331',
          '20220430',
          '20220531',
          '20220630',
          '20220731',
          '20220831',
          '20220930',
          '20221031',
          '20221130',
          '20221231',
          '20230131',
          '20230228',
          '20230331',
          '20230430',
          '20230531',
          '20230630',
          '20230731',
          '20230831',
          '20230930',
          '20231031',
          '20231130',
          '20231231',
          '20240131',
          '20240229',
          '20240331',
          '20240430',
          '20240531',
          '20240630',
          '20240731',
          '20240831',
          '20240930',
          '20241031',
          '20241130',
          '20241231',
          '20250131',
          '20250228',
          '20250331',
          '20250430',
          '20250531',
          '20250630',
          '20250714'
          )
group by  V_DATE_,
          COMPANY_INNER_CODE_DES
order by  V_DATE_ ASC`.trim()
    await compare({
      currentSql,
      expectSql,
    })
  })

  it('场景颗粒度日，查询颗粒度年', async () => {
    const metricToSql = new MetricToSql({
      metricConfig: metricConfigRecord['c_ompZrkNw2QNBQgzRRRI'],
      version: '2025',
      snapshotDateMode: 'fixed',
    })
    const queryParams = {
      metricNames: ['CB00JD0010'],
      groupBys: ['COMPANY_INNER_CODE_DES'],
      originGroupBys: ['COMPANY_INNER_CODE_DES'],
      where: "COMPANY_INNER_CODE_DES IN ('中国宝武钢铁集团有限公司-法人','中国宝武钢铁集团有限公司-资产合并')",
      originWhere: "COMPANY_INNER_CODE_DES IN ('中国宝武钢铁集团有限公司-资产合并')",
      orderBys: ['V_DATE_ ASC'],
      limit: null,
      timeQueryParams: {
        timeStartFunction: {
          type: 'specificDate',
          year: 2015,
          month: 1,
          day: 1,
          quarter: null,
        },
        timeEndFunction: {
          type: 'specificDate',
          year: 2025,
          month: 7,
          day: 14,
          quarter: null,
        },
        timeGranularity: 'year',
        timeDimensionName: null,
      },
      isMetricNamesExactMatch: true,
      isWhereExactMatch: false,
      extraInfo: {
        is_sub: false,
        groupbys_with_level: [
          {
            groupBys: 'COMPANY_INNER_CODE_DES',
            groupByLevel: 0,
          },
        ],
        metric_ner: ['资产负债率'],
        where_ner: ['集团'],
        timeQueryType: '年',
        metric_scores: {
          SUMIA070040: 1,
        },
      },
      notExistMetricNames: null,
      notExistGroupBys: null,
      notExistOrderBys: null,
    } as any as QueryParams

    const { sql: currentSql } = await metricToSql.toSql(queryParams)
    const expectSql = `
select    SUBSTRING(
          dipeak.baowu_data.T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.ACCT_PERIOD_NO,
          1,
          4
          ) as V_DATE_,
          T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.COMPANY_INNER_CODE_DES as COMPANY_INNER_CODE_DES,
          sum(
          (
          CASE
                    WHEN REPORT_ITEM IN ('CB00JD0010') then month_amt
                    ELSE NULL
          END
          )
          ) as CB00JD0010
from      dipeak.baowu_data.T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417
where     SUBSTRING(
          dipeak.baowu_data.T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.ACCT_PERIOD_NO,
          1,
          4
          ) >= '2015'
AND       SUBSTRING(
          dipeak.baowu_data.T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.ACCT_PERIOD_NO,
          1,
          4
          ) <= '2025'
and       SUBSTR (
          dipeak.baowu_data.T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.ACCT_PERIOD_NO,
          5,
          2
          ) BETWEEN '01' AND '12'
and       (
          T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.COMPANY_INNER_CODE_DES IN ('中国宝武钢铁集团有限公司-法人', '中国宝武钢铁集团有限公司-资产合并')
AND       T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.GRANULARITY = 'month'
          )
and       dipeak.baowu_data.T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.ACCT_PERIOD_NO IN (
          '20151231',
          '20161231',
          '20171231',
          '20181231',
          '20191231',
          '20201231',
          '20211231',
          '20221231',
          '20231231',
          '20241231',
          '20250714'
          )
group by  V_DATE_,
          COMPANY_INNER_CODE_DES
order by  V_DATE_ ASC`.trim()
    await compare({
      currentSql,
      expectSql,
    })
  })

  it('场景颗粒度日，查询颗粒度TOTAL', async () => {
    const metricToSql = new MetricToSql({
      metricConfig: metricConfigRecord['c_ompZrkNw2QNBQgzRRRI'],
      version: '2025',
      snapshotDateMode: 'fixed',
    })
    const queryParams = {
      metricNames: ['CB00JD0010'],
      groupBys: ['COMPANY_INNER_CODE_DES'],
      originGroupBys: ['COMPANY_INNER_CODE_DES'],
      where: "COMPANY_INNER_CODE_DES IN ('中国宝武钢铁集团有限公司-法人','中国宝武钢铁集团有限公司-资产合并')",
      originWhere: "COMPANY_INNER_CODE_DES IN ('中国宝武钢铁集团有限公司-资产合并')",
      orderBys: ['V_DATE_ ASC'],
      limit: null,
      timeQueryParams: {
        timeStartFunction: {
          type: 'specificDate',
          year: 2015,
          month: 1,
          day: 1,
          quarter: null,
        },
        timeEndFunction: {
          type: 'specificDate',
          year: 2025,
          month: 7,
          day: 14,
          quarter: null,
        },
        timeGranularity: 'total',
        timeDimensionName: null,
      },
      isMetricNamesExactMatch: true,
      isWhereExactMatch: false,
      extraInfo: {
        is_sub: false,
        groupbys_with_level: [
          {
            groupBys: 'COMPANY_INNER_CODE_DES',
            groupByLevel: 0,
          },
        ],
        metric_ner: ['资产负债率'],
        where_ner: ['集团'],
        timeQueryType: '年',
        metric_scores: {
          SUMIA070040: 1,
        },
      },
      notExistMetricNames: null,
      notExistGroupBys: null,
      notExistOrderBys: null,
    } as any as QueryParams

    const { sql: currentSql } = await metricToSql.toSql(queryParams)
    const expectSql = `
select    T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.COMPANY_INNER_CODE_DES as COMPANY_INNER_CODE_DES,
          sum(
          (
          CASE
                    WHEN REPORT_ITEM IN ('CB00JD0010') then month_amt
                    ELSE NULL
          END
          )
          ) as CB00JD0010
from      dipeak.baowu_data.T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417
where     dipeak.baowu_data.T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.ACCT_PERIOD_NO = '20250714'
and       SUBSTR (
          dipeak.baowu_data.T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.ACCT_PERIOD_NO,
          5,
          2
          ) BETWEEN '01' AND '12'
and       (
          T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.COMPANY_INNER_CODE_DES IN ('中国宝武钢铁集团有限公司-法人', '中国宝武钢铁集团有限公司-资产合并')
AND       T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.GRANULARITY = 'month'
          )
and       dipeak.baowu_data.T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.ACCT_PERIOD_NO IN ('20250714')
group by  COMPANY_INNER_CODE_DES`.trim()
    await compare({
      currentSql,
      expectSql,
    })
  })
})

describe('改写where', async () => {
  it('查询鄂钢5月份的铁水成本', async () => {
    const metricToSql = new MetricToSql({
      metricConfig: metricConfigRecord['c_ompZrkNw2QNBQgzRRRI'],
      version: '2025',
    })
    const queryParams = {
      metricNames: ['CB00JD0001MIX'],
      groupBys: ['COMPANY_INNER_CODE_DES'],
      originGroupBys: ['COMPANY_INNER_CODE_DES'],
      where: "COMPANY_INNER_CODE_DES IN ('宝武集团鄂城钢铁有限公司-法人')",
      originWhere: "COMPANY_INNER_CODE_DES IN ('宝武集团鄂城钢铁有限公司-法人')",
      orderBys: [],
      limit: null,
      timeQueryParams: {
        timeStartFunction: {
          type: 'specificDate',
          year: 2025,
          month: 5,
          day: 31,
          quarter: null,
        },
        timeEndFunction: {
          type: 'specificDate',
          year: 2025,
          month: 5,
          day: 31,
          quarter: null,
        },
        timeGranularity: 'total',
      },
      isMetricNamesExactMatch: true,
      isWhereExactMatch: true,
      extraInfo: {
        is_sub: false,
        groupbys_with_level: [
          {
            groupBys: 'COMPANY_INNER_CODE_DES',
            groupByLevel: 0,
          },
        ],
        metric_ner: ['铁水成本'],
        where_ner: ['鄂钢'],
        timeQueryType: '月',
        metric_scores: {
          CB00JD0001MIX: 1,
        },
      },
      notExistMetricNames: null,
      notExistGroupBys: null,
      notExistOrderBys: null,
    } as any as QueryParams

    const { sql: currentSql } = await metricToSql.toSql(queryParams)
    const expectSql = `
with      SNAPSHOT_LATEST_TIME_TBL as (
          select    T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.COMPANY_INNER_CODE_DES as COMPANY_INNER_CODE_DES,
                    max(
                    dipeak.baowu_data.T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.ACCT_PERIOD_NO
                    ) as LAST_DATE
          from      dipeak.baowu_data.T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417
          where     dipeak.baowu_data.T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.ACCT_PERIOD_NO = '20250531'
          and       SUBSTR (
                    dipeak.baowu_data.T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.ACCT_PERIOD_NO,
                    5,
                    2
                    ) BETWEEN '01' AND '12'
          and       (
                    T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.COMPANY_INNER_CODE_DES IN ('宝武集团鄂城钢铁有限公司-法人')
          AND       T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.GRANULARITY = 'month'
                    )
          group by  COMPANY_INNER_CODE_DES
          )
select    SNAPSHOT_LATEST_TIME_TBL.COMPANY_INNER_CODE_DES as COMPANY_INNER_CODE_DES,
          sum(
          (
          CASE
                    WHEN REPORT_ITEM IN ('CB00JD0001', 'CW00JT0016', 'CW00GS0007') then month_amt
                    ELSE NULL
          END
          )
          ) as CB00JD0001MIX
from      SNAPSHOT_LATEST_TIME_TBL
join      dipeak.baowu_data.T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417 on dipeak.baowu_data.T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.ACCT_PERIOD_NO = SNAPSHOT_LATEST_TIME_TBL.LAST_DATE
and       T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.COMPANY_INNER_CODE_DES = SNAPSHOT_LATEST_TIME_TBL.COMPANY_INNER_CODE_DES
where     dipeak.baowu_data.T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.ACCT_PERIOD_NO = '20250531'
and       SUBSTR (
          dipeak.baowu_data.T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.ACCT_PERIOD_NO,
          5,
          2
          ) BETWEEN '01' AND '12'
and       (
          T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.COMPANY_INNER_CODE_DES IN ('宝武集团鄂城钢铁有限公司-法人')
AND       T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.GRANULARITY = 'month'
          )
group by  COMPANY_INNER_CODE_DES`.trim()
    await compare({
      currentSql,
      expectSql,
      oldSql: `
      WITH DIM_TBL AS (
  SELECT DISTINCT T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.COMPANY_INNER_CODE_DES AS COMPANY_INNER_CODE_DES FROM dipeak.baowu_data.T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417 WHERE dipeak.baowu_data.T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.ACCT_PERIOD_NO = '20250531' AND (T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.COMPANY_INNER_CODE_DES IN ('宝武集团鄂城钢铁有限公司-法人') AND T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.GRANULARITY = 'month' AND T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.GRANULARITY = 'month')
), CB00JD0001MIX_TBL AS (
  SELECT sum(t_ads_fact_indicator_comparison_extends_single_new0417.month_amt) as CB00JD0001MIX, T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.COMPANY_INNER_CODE_DES AS COMPANY_INNER_CODE_DES FROM dipeak.baowu_data.T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417 WHERE dipeak.baowu_data.T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.ACCT_PERIOD_NO = '20250531' AND (T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.COMPANY_INNER_CODE_DES IN ('宝武集团鄂城钢铁有限公司-法人') AND T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.GRANULARITY = 'month' AND T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.GRANULARITY = 'month') AND REPORT_ITEM IN ('CB00JD0001', 'CW00JT0016', 'CW00GS0007') GROUP BY COMPANY_INNER_CODE_DES
)
SELECT DIM_TBL.COMPANY_INNER_CODE_DES, CB00JD0001MIX_TBL.CB00JD0001MIX FROM DIM_TBL LEFT JOIN CB00JD0001MIX_TBL ON DIM_TBL.COMPANY_INNER_CODE_DES = CB00JD0001MIX_TBL.COMPANY_INNER_CODE_DES
      `,
    })
  })

  it('宝武集团 应收账款大于100的前10个子公司', async () => {
    const metricToSql = new MetricToSql({
      metricConfig: metricConfigRecord['chvkZ9hCPz0xd6HC'],
      version: '2025',
    })
    const queryParams = {
      metricNames: ['SUMI1122'],
      groupBys: ['COMPANY_INNER_CODE_DES'],
      originGroupBys: ['COMPANY_INNER_CODE_DES'],
      where:
        "COMPANY_INNER_CODE_DES IN ('宝山钢铁股份有限公司-法人','宝武碳业科技股份有限公司-法人','上海欧冶供应链有限公司-法人') AND SUMI1122 > 100",
      orderBys: ['SUMI1122 desc'],
      limit: 30,
      timeQueryParams: {
        timeGranularity: 'total',
        timeStartFunction: {
          type: 'specificDate',
          year: 2025,
          month: 6,
          day: 1,
        },
        timeEndFunction: {
          type: 'specificDate',
          year: 2025,
          month: 6,
          day: 30,
        },
      },
      isMetricNamesExactMatch: true,
      isWhereExactMatch: true,
      extraInfo: {
        is_sub: false,
        groupbys_with_level: [
          {
            groupBys: 'COMPANY_INNER_CODE_DES',
            groupByLevel: -1,
          },
        ],
        metric_ner: ['应收账款'],
        where_ner: ['宝武集团'],
        metric_scores: {
          SUMI2202: 0.75,
          SUMI1122: 1,
        },
      },
      notExistMetricNames: null,
      notExistGroupBys: null,
      notExistOrderBys: null,
    } as any as QueryParams

    const { sql: currentSql } = await metricToSql.toSql(queryParams)
    const expectSql = `
with      SNAPSHOT_LATEST_TIME_TBL as (
          select    T_ADS_FACT_WSSJ_TOTAL_INDEX.COMPANY_INNER_CODE_DES as COMPANY_INNER_CODE_DES,
                    max(
                    dipeak.baowu_data.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO
                    ) as LAST_DATE
          from      dipeak.baowu_data.T_ADS_FACT_WSSJ_TOTAL_INDEX
          where     dipeak.baowu_data.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO = '202506'
          and       SUBSTR (
                    dipeak.baowu_data.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO,
                    5,
                    2
                    ) BETWEEN '01' AND '12'
          and       (
                    T_ADS_FACT_WSSJ_TOTAL_INDEX.COMPANY_INNER_CODE_DES IN (
                    '宝山钢铁股份有限公司-法人',
                    '宝武碳业科技股份有限公司-法人',
                    '上海欧冶供应链有限公司-法人'
                    )
                    )
          group by  COMPANY_INNER_CODE_DES
          )
select    SNAPSHOT_LATEST_TIME_TBL.COMPANY_INNER_CODE_DES as COMPANY_INNER_CODE_DES,
          sum(
          (
          CASE
                    WHEN REPORT_ITEM = 'I1122' then end_amt
                    ELSE NULL
          END
          )
          ) as SUMI1122
from      SNAPSHOT_LATEST_TIME_TBL
join      dipeak.baowu_data.T_ADS_FACT_WSSJ_TOTAL_INDEX on dipeak.baowu_data.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO = SNAPSHOT_LATEST_TIME_TBL.LAST_DATE
and       T_ADS_FACT_WSSJ_TOTAL_INDEX.COMPANY_INNER_CODE_DES = SNAPSHOT_LATEST_TIME_TBL.COMPANY_INNER_CODE_DES
where     SUMI1122 > 100
and       dipeak.baowu_data.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO = '202506'
and       SUBSTR (
          dipeak.baowu_data.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO,
          5,
          2
          ) BETWEEN '01' AND '12'
and       (
          T_ADS_FACT_WSSJ_TOTAL_INDEX.COMPANY_INNER_CODE_DES IN (
          '宝山钢铁股份有限公司-法人',
          '宝武碳业科技股份有限公司-法人',
          '上海欧冶供应链有限公司-法人'
          )
          )
group by  COMPANY_INNER_CODE_DES
order by  SUMI1122 desc
limit     30`.trim()
    await compare({
      currentSql,
      expectSql,
      oldSql: `
     WITH DIM_TBL AS (
  SELECT DISTINCT AMT_WIDE_TABLE.TARGET_COMPANY_INNER_DES AS COMPANY_INNER_CODE_DES FROM dipeak.baowu_data.AMT_WIDE_TABLE WHERE dipeak.baowu_data.AMT_WIDE_TABLE.ACCT_PERIOD_NO = '202506' AND AMT_WIDE_TABLE.TARGET_COMPANY_INNER_DES IN ('宝山钢铁股份有限公司-法人', '宝武碳业科技股份有限公司-法人', '上海欧冶供应链有限公司-法人') AND substr(ACCT_TITLE_CODE,1,4)='1122' GROUP BY COMPANY_INNER_CODE_DES
)
SELECT DIM_TBL.COMPANY_INNER_CODE_DES, SUMI1122_TBL.SUMI1122 FROM DIM_TBL LEFT JOIN SUMI1122_TBL ON DIM_TBL.COMPANY_INNER_CODE_DES = SUMI1122_TBL.COMPANY_INNER_CODE_DES WHERE SUMI1122 > 100 ORDER BY SUMI1122 desc LIMIT 30 `,
    })
  })

  it('宝武集团 应收账款大于100的前10个子公司 fixed', async () => {
    const metricToSql = new MetricToSql({
      metricConfig: metricConfigRecord['chvkZ9hCPz0xd6HC'],
      version: '2025',
      snapshotDateMode: 'fixed',
    })
    const queryParams = {
      metricNames: ['SUMI1122'],
      groupBys: ['COMPANY_INNER_CODE_DES'],
      originGroupBys: ['COMPANY_INNER_CODE_DES'],
      where:
        "COMPANY_INNER_CODE_DES IN ('宝山钢铁股份有限公司-法人','宝武碳业科技股份有限公司-法人','上海欧冶供应链有限公司-法人') AND SUMI1122 > 100",
      orderBys: ['SUMI1122 desc'],
      limit: 30,
      timeQueryParams: {
        timeGranularity: 'total',
        timeStartFunction: {
          type: 'specificDate',
          year: 2025,
          month: 6,
          day: 1,
        },
        timeEndFunction: {
          type: 'specificDate',
          year: 2025,
          month: 6,
          day: 30,
        },
      },
      isMetricNamesExactMatch: true,
      isWhereExactMatch: true,
      extraInfo: {
        is_sub: false,
        groupbys_with_level: [
          {
            groupBys: 'COMPANY_INNER_CODE_DES',
            groupByLevel: -1,
          },
        ],
        metric_ner: ['应收账款'],
        where_ner: ['宝武集团'],
        metric_scores: {
          SUMI2202: 0.75,
          SUMI1122: 1,
        },
      },
      notExistMetricNames: null,
      notExistGroupBys: null,
      notExistOrderBys: null,
    } as any as QueryParams

    const { sql: currentSql } = await metricToSql.toSql(queryParams)
    const expectSql = `
select    T_ADS_FACT_WSSJ_TOTAL_INDEX.COMPANY_INNER_CODE_DES as COMPANY_INNER_CODE_DES,
          sum(
          (
          CASE
                    WHEN REPORT_ITEM = 'I1122' then end_amt
                    ELSE NULL
          END
          )
          ) as SUMI1122
from      dipeak.baowu_data.T_ADS_FACT_WSSJ_TOTAL_INDEX
where     SUMI1122 > 100
and       dipeak.baowu_data.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO = '202506'
and       SUBSTR (
          dipeak.baowu_data.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO,
          5,
          2
          ) BETWEEN '01' AND '12'
and       (
          T_ADS_FACT_WSSJ_TOTAL_INDEX.COMPANY_INNER_CODE_DES IN (
          '宝山钢铁股份有限公司-法人',
          '宝武碳业科技股份有限公司-法人',
          '上海欧冶供应链有限公司-法人'
          )
          )
and       dipeak.baowu_data.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO IN ('202506')
group by  COMPANY_INNER_CODE_DES
order by  SUMI1122 desc
limit     30`.trim()
    await compare({
      currentSql,
      expectSql,
      oldSql: `
     WITH DIM_TBL AS (
  SELECT DISTINCT AMT_WIDE_TABLE.TARGET_COMPANY_INNER_DES AS COMPANY_INNER_CODE_DES FROM dipeak.baowu_data.AMT_WIDE_TABLE WHERE dipeak.baowu_data.AMT_WIDE_TABLE.ACCT_PERIOD_NO = '202506' AND AMT_WIDE_TABLE.TARGET_COMPANY_INNER_DES IN ('宝山钢铁股份有限公司-法人', '宝武碳业科技股份有限公司-法人', '上海欧冶供应链有限公司-法人') AND substr(ACCT_TITLE_CODE,1,4)='1122' GROUP BY COMPANY_INNER_CODE_DES
)
SELECT DIM_TBL.COMPANY_INNER_CODE_DES, SUMI1122_TBL.SUMI1122 FROM DIM_TBL LEFT JOIN SUMI1122_TBL ON DIM_TBL.COMPANY_INNER_CODE_DES = SUMI1122_TBL.COMPANY_INNER_CODE_DES WHERE SUMI1122 > 100 ORDER BY SUMI1122 desc LIMIT 30 `,
    })
  })
})

describe('非total情况下 求不可累加指标 mode=fixed 时间维度total', async () => {
  it('宝武共享1月份的 两金情况', async () => {
    const metricToSql = new MetricToSql({
      metricConfig: metricConfigRecord['chvkZ9hCPz0xd6HC'],
      version: '2025',
      snapshotDateMode: 'fixed',
    })
    const queryParams = {
      extraInfo: {
        groupbys_with_level: [
          {
            groupByLevel: 0,
            groupBys: 'COMPANY_INNER_CODE_DES',
          },
        ],
        is_sub: false,
        metric_ner: ['资产负债率'],
        metric_scores: {
          SUMIA070040: 1,
        },
        timeQueryType: '年',
        where_ner: ['宝地资产'],
      },
      groupBys: ['COMPANY_INNER_CODE_DES'],
      isMetricNamesExactMatch: true,
      isWhereExactMatch: true,
      limit: null,
      metricNames: ['SUMIA070040'],
      notExistGroupBys: null,
      notExistMetricNames: null,
      notExistOrderBys: null,
      orderBys: ['V_DATE_ ASC'],
      originGroupBys: ['COMPANY_INNER_CODE_DES'],
      originWhere: "COMPANY_INNER_CODE_DES IN ('上海宝地不动产资产管理有限公司-管理合并')",
      timeQueryParams: {
        timeDimensionName: null,
        timeEndFunction: {
          day: 31,
          month: 12,
          quarter: null,
          type: 'specificDate',
          year: 2024,
        },
        timeGranularity: 'total',
        timeStartFunction: {
          day: 1,
          month: 1,
          quarter: null,
          type: 'specificDate',
          year: 2022,
        },
      },
      where: "COMPANY_INNER_CODE_DES IN ('上海宝地不动产资产管理有限公司-管理合并')",
    } as any as QueryParams

    const { sql: currentSql } = await metricToSql.toSql(queryParams)
    const expectSql = `
select    T_ADS_FACT_WSSJ_TOTAL_INDEX.COMPANY_INNER_CODE_DES as COMPANY_INNER_CODE_DES,
          sum(
          (
          CASE
                    WHEN REPORT_ITEM = 'IA070040' then month_amt
                    ELSE NULL
          END
          )
          ) as SUMIA070040
from      dipeak.baowu_data.T_ADS_FACT_WSSJ_TOTAL_INDEX
where     dipeak.baowu_data.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO = '202412'
and       SUBSTR (
          dipeak.baowu_data.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO,
          5,
          2
          ) BETWEEN '01' AND '12'
and       (
          T_ADS_FACT_WSSJ_TOTAL_INDEX.COMPANY_INNER_CODE_DES IN ('上海宝地不动产资产管理有限公司-管理合并')
          )
and       dipeak.baowu_data.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO IN ('202412')
group by  COMPANY_INNER_CODE_DES`.trim()
    await compare({
      currentSql,
      expectSql,
      oldSql:
        "WITH DIM_TBL AS (\n SELECT DISTINCT SUBSTRING(dipeak.baowu_data.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO, 1, 4) AS V_DATE_, T_ADS_FACT_WSSJ_TOTAL_INDEX.COMPANY_INNER_CODE_DES AS COMPANY_INNER_CODE_DES FROM dipeak.baowu_data.T_ADS_FACT_WSSJ_TOTAL_INDEX WHERE SUBSTRING(dipeak.baowu_data.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO, 1, 4) >= '2022' AND SUBSTRING(dipeak.baowu_data.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO, 1, 4) <= '2024' AND (T_ADS_FACT_WSSJ_TOTAL_INDEX.COMPANY_INNER_CODE_DES IN ('上海宝地不动产资产管理有限公司-管理合并'))\n), SUMIA070040_TBL AS (\n SELECT sum(t_ads_fact_wssj_total_index.month_amt) as SUMIA070040, SUBSTRING(dipeak.baowu_data.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO, 1, 4) AS V_DATE_, T_ADS_FACT_WSSJ_TOTAL_INDEX.COMPANY_INNER_CODE_DES AS COMPANY_INNER_CODE_DES FROM dipeak.baowu_data.T_ADS_FACT_WSSJ_TOTAL_INDEX WHERE SUBSTRING(dipeak.baowu_data.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO, 1, 4) >= '2022' AND SUBSTRING(dipeak.baowu_data.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO, 1, 4) <= '2024' AND (T_ADS_FACT_WSSJ_TOTAL_INDEX.COMPANY_INNER_CODE_DES IN ('上海宝地不动产资产管理有限公司-管理合并')) AND REPORT_ITEM = 'IA070040' GROUP BY V_DATE_, COMPANY_INNER_CODE_DES\n)\nSELECT DIM_TBL.V_DATE_, DIM_TBL.COMPANY_INNER_CODE_DES, SUMIA070040_TBL.SUMIA070040 FROM DIM_TBL LEFT JOIN SUMIA070040_TBL ON DIM_TBL.COMPANY_INNER_CODE_DES = SUMIA070040_TBL.COMPANY_INNER_CODE_DES AND DIM_TBL.V_DATE_ = SUMIA070040_TBL.V_DATE_ ORDER BY V_DATE_ ASC",
    })
  })

  it('集团各年资产负债率', async () => {
    const metricToSql = new MetricToSql({
      metricConfig: metricConfigRecord['fS0U86Sz9l2k3AH7'],
      version: '2025',
      snapshotDateMode: 'fixed',
    })
    const queryParams = {
      metricNames: ['SUMIA070040'],
      groupBys: ['COMPANY_INNER_CODE_DES'],
      originGroupBys: ['COMPANY_INNER_CODE_DES'],
      where: "COMPANY_INNER_CODE_DES IN ('中国宝武钢铁集团有限公司-法人','中国宝武钢铁集团有限公司-资产合并')",
      originWhere: "COMPANY_INNER_CODE_DES IN ('中国宝武钢铁集团有限公司-资产合并')",
      orderBys: ['V_DATE_ ASC'],
      limit: null,
      timeQueryParams: {
        timeStartFunction: {
          type: 'specificDate',
          year: 2015,
          month: 1,
          day: 1,
          quarter: null,
        },
        timeEndFunction: {
          type: 'specificDate',
          year: 2025,
          month: 7,
          day: 14,
          quarter: null,
        },
        timeGranularity: 'total',
        timeDimensionName: null,
      },
      isMetricNamesExactMatch: true,
      isWhereExactMatch: false,
      extraInfo: {
        is_sub: false,
        groupbys_with_level: [
          {
            groupBys: 'COMPANY_INNER_CODE_DES',
            groupByLevel: 0,
          },
        ],
        metric_ner: ['资产负债率'],
        where_ner: ['集团'],
        timeQueryType: '年',
        metric_scores: {
          SUMIA070040: 1,
        },
      },
      notExistMetricNames: null,
      notExistGroupBys: null,
      notExistOrderBys: null,
    } as any as QueryParams

    const { sql: currentSql } = await metricToSql.toSql(queryParams)
    const expectSql = `
select    T_ADS_FACT_WSSJ_TOTAL_INDEX.COMPANY_INNER_CODE_DES as COMPANY_INNER_CODE_DES,
          sum(
          (
          CASE
                    WHEN REPORT_ITEM = 'IA070040' then month_amt
                    ELSE NULL
          END
          )
          ) as SUMIA070040
from      dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX
where     dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO = '202507'
and       SUBSTR (
          dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO,
          5,
          2
          ) BETWEEN '01' AND '12'
and       (
          T_ADS_FACT_WSSJ_TOTAL_INDEX.COMPANY_INNER_CODE_DES IN ('中国宝武钢铁集团有限公司-法人', '中国宝武钢铁集团有限公司-资产合并')
          )
and       dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO IN ('202507')
group by  COMPANY_INNER_CODE_DES`.trim()
    await compare({
      currentSql,
      expectSql,
      oldSql:
        "WITH DIM_TBL AS (\n  SELECT DISTINCT SUBSTRING(dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO, 1, 4) AS V_DATE_, T_ADS_FACT_WSSJ_TOTAL_INDEX.COMPANY_INNER_CODE_DES AS COMPANY_INNER_CODE_DES FROM dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX WHERE SUBSTRING(dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO, 1, 4) >= '2015' AND SUBSTRING(dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO, 1, 4) <= '2025' AND (T_ADS_FACT_WSSJ_TOTAL_INDEX.COMPANY_INNER_CODE_DES IN ('中国宝武钢铁集团有限公司-资产合并'))\n), SUMIA070040_TBL AS (\n  SELECT sum(t_ads_fact_wssj_total_index.month_amt) as SUMIA070040, SUBSTRING(dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO, 1, 4) AS V_DATE_, T_ADS_FACT_WSSJ_TOTAL_INDEX.COMPANY_INNER_CODE_DES AS COMPANY_INNER_CODE_DES FROM dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX WHERE SUBSTRING(dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO, 1, 4) >= '2015' AND SUBSTRING(dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX.ACCT_PERIOD_NO, 1, 4) <= '2025' AND (T_ADS_FACT_WSSJ_TOTAL_INDEX.COMPANY_INNER_CODE_DES IN ('中国宝武钢铁集团有限公司-资产合并')) AND REPORT_ITEM = 'IA070040' GROUP BY V_DATE_, COMPANY_INNER_CODE_DES\n)\nSELECT DIM_TBL.V_DATE_, DIM_TBL.COMPANY_INNER_CODE_DES, SUMIA070040_TBL.SUMIA070040 FROM DIM_TBL LEFT JOIN SUMIA070040_TBL ON DIM_TBL.COMPANY_INNER_CODE_DES = SUMIA070040_TBL.COMPANY_INNER_CODE_DES AND DIM_TBL.V_DATE_ = SUMIA070040_TBL.V_DATE_ ORDER BY V_DATE_ ASC",
    })
  })

  it('场景颗粒度日，查询颗粒度月', async () => {
    const metricToSql = new MetricToSql({
      metricConfig: metricConfigRecord['c_ompZrkNw2QNBQgzRRRI'],
      version: '2025',
      snapshotDateMode: 'fixed',
    })
    const queryParams = {
      metricNames: ['CB00JD0010'],
      groupBys: ['COMPANY_INNER_CODE_DES'],
      originGroupBys: ['COMPANY_INNER_CODE_DES'],
      where: "COMPANY_INNER_CODE_DES IN ('中国宝武钢铁集团有限公司-法人','中国宝武钢铁集团有限公司-资产合并')",
      originWhere: "COMPANY_INNER_CODE_DES IN ('中国宝武钢铁集团有限公司-资产合并')",
      orderBys: ['V_DATE_ ASC'],
      limit: null,
      timeQueryParams: {
        timeStartFunction: {
          type: 'specificDate',
          year: 2015,
          month: 1,
          day: 1,
          quarter: null,
        },
        timeEndFunction: {
          type: 'specificDate',
          year: 2025,
          month: 7,
          day: 14,
          quarter: null,
        },
        timeGranularity: 'total',
        timeDimensionName: null,
      },
      isMetricNamesExactMatch: true,
      isWhereExactMatch: false,
      extraInfo: {
        is_sub: false,
        groupbys_with_level: [
          {
            groupBys: 'COMPANY_INNER_CODE_DES',
            groupByLevel: 0,
          },
        ],
        metric_ner: ['资产负债率'],
        where_ner: ['集团'],
        timeQueryType: '年',
        metric_scores: {
          SUMIA070040: 1,
        },
      },
      notExistMetricNames: null,
      notExistGroupBys: null,
      notExistOrderBys: null,
    } as any as QueryParams

    const { sql: currentSql } = await metricToSql.toSql(queryParams)
    const expectSql = `
select    T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.COMPANY_INNER_CODE_DES as COMPANY_INNER_CODE_DES,
          sum(
          (
          CASE
                    WHEN REPORT_ITEM IN ('CB00JD0010') then month_amt
                    ELSE NULL
          END
          )
          ) as CB00JD0010
from      dipeak.baowu_data.T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417
where     dipeak.baowu_data.T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.ACCT_PERIOD_NO = '20250714'
and       SUBSTR (
          dipeak.baowu_data.T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.ACCT_PERIOD_NO,
          5,
          2
          ) BETWEEN '01' AND '12'
and       (
          T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.COMPANY_INNER_CODE_DES IN ('中国宝武钢铁集团有限公司-法人', '中国宝武钢铁集团有限公司-资产合并')
AND       T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.GRANULARITY = 'month'
          )
and       dipeak.baowu_data.T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.ACCT_PERIOD_NO IN ('20250714')
group by  COMPANY_INNER_CODE_DES`.trim()
    await compare({
      currentSql,
      expectSql,
    })
  })

  it('场景颗粒度日，查询颗粒度年', async () => {
    const metricToSql = new MetricToSql({
      metricConfig: metricConfigRecord['c_ompZrkNw2QNBQgzRRRI'],
      version: '2025',
      snapshotDateMode: 'fixed',
    })
    const queryParams = {
      metricNames: ['CB00JD0010'],
      groupBys: ['COMPANY_INNER_CODE_DES'],
      originGroupBys: ['COMPANY_INNER_CODE_DES'],
      where: "COMPANY_INNER_CODE_DES IN ('中国宝武钢铁集团有限公司-法人','中国宝武钢铁集团有限公司-资产合并')",
      originWhere: "COMPANY_INNER_CODE_DES IN ('中国宝武钢铁集团有限公司-资产合并')",
      orderBys: ['V_DATE_ ASC'],
      limit: null,
      timeQueryParams: {
        timeStartFunction: {
          type: 'specificDate',
          year: 2015,
          month: 1,
          day: 1,
          quarter: null,
        },
        timeEndFunction: {
          type: 'specificDate',
          year: 2025,
          month: 7,
          day: 14,
          quarter: null,
        },
        timeGranularity: 'year',
        timeDimensionName: null,
      },
      isMetricNamesExactMatch: true,
      isWhereExactMatch: false,
      extraInfo: {
        is_sub: false,
        groupbys_with_level: [
          {
            groupBys: 'COMPANY_INNER_CODE_DES',
            groupByLevel: 0,
          },
        ],
        metric_ner: ['资产负债率'],
        where_ner: ['集团'],
        timeQueryType: '年',
        metric_scores: {
          SUMIA070040: 1,
        },
      },
      notExistMetricNames: null,
      notExistGroupBys: null,
      notExistOrderBys: null,
    } as any as QueryParams

    const { sql: currentSql } = await metricToSql.toSql(queryParams)
    const expectSql = `
select    SUBSTRING(
          dipeak.baowu_data.T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.ACCT_PERIOD_NO,
          1,
          4
          ) as V_DATE_,
          T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.COMPANY_INNER_CODE_DES as COMPANY_INNER_CODE_DES,
          sum(
          (
          CASE
                    WHEN REPORT_ITEM IN ('CB00JD0010') then month_amt
                    ELSE NULL
          END
          )
          ) as CB00JD0010
from      dipeak.baowu_data.T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417
where     SUBSTRING(
          dipeak.baowu_data.T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.ACCT_PERIOD_NO,
          1,
          4
          ) >= '2015'
AND       SUBSTRING(
          dipeak.baowu_data.T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.ACCT_PERIOD_NO,
          1,
          4
          ) <= '2025'
and       SUBSTR (
          dipeak.baowu_data.T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.ACCT_PERIOD_NO,
          5,
          2
          ) BETWEEN '01' AND '12'
and       (
          T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.COMPANY_INNER_CODE_DES IN ('中国宝武钢铁集团有限公司-法人', '中国宝武钢铁集团有限公司-资产合并')
AND       T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.GRANULARITY = 'month'
          )
and       dipeak.baowu_data.T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.ACCT_PERIOD_NO IN (
          '20151231',
          '20161231',
          '20171231',
          '20181231',
          '20191231',
          '20201231',
          '20211231',
          '20221231',
          '20231231',
          '20241231',
          '20250714'
          )
group by  V_DATE_,
          COMPANY_INNER_CODE_DES
order by  V_DATE_ ASC`.trim()
    await compare({
      currentSql,
      expectSql,
    })
  })

  it('场景颗粒度日，查询颗粒度TOTAL', async () => {
    const metricToSql = new MetricToSql({
      metricConfig: metricConfigRecord['c_ompZrkNw2QNBQgzRRRI'],
      version: '2025',
      snapshotDateMode: 'fixed',
    })
    const queryParams = {
      metricNames: ['CB00JD0010'],
      groupBys: ['COMPANY_INNER_CODE_DES'],
      originGroupBys: ['COMPANY_INNER_CODE_DES'],
      where: "COMPANY_INNER_CODE_DES IN ('中国宝武钢铁集团有限公司-法人','中国宝武钢铁集团有限公司-资产合并')",
      originWhere: "COMPANY_INNER_CODE_DES IN ('中国宝武钢铁集团有限公司-资产合并')",
      orderBys: ['V_DATE_ ASC'],
      limit: null,
      timeQueryParams: {
        timeStartFunction: {
          type: 'specificDate',
          year: 2015,
          month: 1,
          day: 1,
          quarter: null,
        },
        timeEndFunction: {
          type: 'specificDate',
          year: 2025,
          month: 7,
          day: 14,
          quarter: null,
        },
        timeGranularity: 'total',
        timeDimensionName: null,
      },
      isMetricNamesExactMatch: true,
      isWhereExactMatch: false,
      extraInfo: {
        is_sub: false,
        groupbys_with_level: [
          {
            groupBys: 'COMPANY_INNER_CODE_DES',
            groupByLevel: 0,
          },
        ],
        metric_ner: ['资产负债率'],
        where_ner: ['集团'],
        timeQueryType: '年',
        metric_scores: {
          SUMIA070040: 1,
        },
      },
      notExistMetricNames: null,
      notExistGroupBys: null,
      notExistOrderBys: null,
    } as any as QueryParams

    const { sql: currentSql } = await metricToSql.toSql(queryParams)
    const expectSql = `
select    T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.COMPANY_INNER_CODE_DES as COMPANY_INNER_CODE_DES,
          sum(
          (
          CASE
                    WHEN REPORT_ITEM IN ('CB00JD0010') then month_amt
                    ELSE NULL
          END
          )
          ) as CB00JD0010
from      dipeak.baowu_data.T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417
where     dipeak.baowu_data.T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.ACCT_PERIOD_NO = '20250714'
and       SUBSTR (
          dipeak.baowu_data.T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.ACCT_PERIOD_NO,
          5,
          2
          ) BETWEEN '01' AND '12'
and       (
          T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.COMPANY_INNER_CODE_DES IN ('中国宝武钢铁集团有限公司-法人', '中国宝武钢铁集团有限公司-资产合并')
AND       T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.GRANULARITY = 'month'
          )
and       dipeak.baowu_data.T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS_SINGLE_NEW0417.ACCT_PERIOD_NO IN ('20250714')
group by  COMPANY_INNER_CODE_DES`.trim()
    await compare({
      currentSql,
      expectSql,
    })
  })
})
