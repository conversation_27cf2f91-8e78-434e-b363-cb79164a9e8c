/* eslint-disable no-case-declarations */
/* eslint-disable no-console */
import knex from 'knex'
import { format as baseSqlFormatter } from 'sql-formatter'
import { assertExhaustive } from 'src/shared/common-utils'
import { measure2Sql, PluginClass } from 'src/shared'
import { DATE_ALIAS, MetricConfig } from 'src/shared/metric-types'
// import { timeFormatMap } from 'src/shared/metric-utils'
import { MetricToSql } from '../../metric-to-sql'
import { DerivedMeta } from '../../derived-meta'
import { dimension2Sql } from './generate-sql-2024-plugin'

const sqlFormatter = (sql: string) =>
  baseSqlFormatter(sql, {
    indentStyle: 'tabularLeft',
    // keywordCase: 'upper',
    // identifierCase: 'upper',
    // dataTypeCase: 'upper',
    // functionCase: 'upper',
  })

const SUFFIX_TBL = '_TBL'
const wrapperTable = (name: string) => name + SUFFIX_TBL
const AGG_TBL = wrapperTable('AGG')
const FLATTED_TBL = wrapperTable('FLATTED')
const SNAPSHOT_LAST_DATE_TBL = wrapperTable('SNAPSHOT_LATEST_TIME')
const SNAPSHOT_TBL = wrapperTable('SNAPSHOT')
const MIX_TBL = wrapperTable('MIX')
const LAST_DATE = 'LAST_DATE'
const concatTableName = (...args: any[]) => args.filter(Boolean).join('.')
type QueryBuilder = knex.Knex.QueryBuilder

export class GenerateSql2025Plugin implements PluginClass<MetricToSql> {
  config!: MetricToSql['config']
  metricConfig!: MetricConfig
  derivedMeta!: DerivedMeta
  knex = knex({
    client: 'mysql',
    wrapIdentifier: (value, _origImpl) => {
      return value
    },
  })
  tables: string[] = []
  buildLayers: {
    tableName: string
    prevTable?: string
    build: (builder: QueryBuilder) => void
  }[] = []

  init() {
    this.tables = []
    this.buildLayers = []
  }

  apply(instance: MetricToSql): void {
    this.config = instance.config
    this.metricConfig = instance.config.metricConfig

    instance.hooks.afterCreateDerivedMetaBuilder.tapPromise(GenerateSql2025Plugin.name, async (builder) => {
      builder.hooks.afterGenDerivedMeta.tapPromise(GenerateSql2025Plugin.name, async (derivedMeta) => {
        this.derivedMeta = derivedMeta
      })
    })

    instance.hooks.afterCreateSqlBuilder.tapPromise(GenerateSql2025Plugin.name, async (builder) => {
      builder.hooks.onSqlBuild.tapPromise(GenerateSql2025Plugin.name, async () => {
        // return undefined
        try {
          if (instance.config.version !== '2025') throw new Error('非2025版，跳过')
          this.init()
          return sqlFormatter(this.toSql())
        } catch (err: any) {
          console.log('Metric2Sql优化失败，退回老链路: ' + (err?.message ?? err?.toString()))
        }
      })
    })
  }

  buildWhere(builder: QueryBuilder) {
    // where
    if (this.derivedMeta.timeSqlPart?.where) {
      builder.whereRaw(this.derivedMeta.timeSqlPart.where)
    }
    if (this.derivedMeta.timeSqlPart?.validatedTime) {
      builder.whereRaw(this.derivedMeta.timeSqlPart.validatedTime)
    }
    // if (this.metricConfig.timeDimensionDatum) {
    //   const { timeDimensionName, timeDimensionFormat, timeDimensionType } = this.metricConfig.timeDimensionDatum
    //   if (timeDimensionType === 'string' && timeDimensionFormat) {
    //     builder.whereRaw(`STR_TO_DATE(${timeDimensionName}, '${timeFormatMap[timeDimensionFormat]}') IS NOT NULL`)
    //   }
    // }
    builder.whereRaw(this.derivedMeta.whereDerived.sharedWhere)
  }

  buildGroupBy(builder: QueryBuilder) {
    // group by
    if (this.derivedMeta.timeSqlPart?.groupBy) {
      // builder.groupBy(this.derivedMeta.timeSqlPart.groupBy)
      builder.groupBy(DATE_ALIAS)
    }
    this.derivedMeta.queryParams.groupBys?.forEach((groupBy) => {
      builder.groupBy(groupBy)
    })
  }

  buildSimpleMetric(builder: QueryBuilder, metricNames: string[]) {
    for (const metricName of metricNames) {
      const metric = this.metricConfig.findMetricByName(metricName)
      let sql = ''
      if (metric.type === 'simple') {
        const measure = this.metricConfig.findMeasureByNameWithoutErr(metric.typeParams.measure)
        const columnName = concatTableName(this.metricConfig.name.split('.').at(-1)!, measure?.name ?? '')
        sql = metric.typeParams.sql_expr || measure2Sql(measure)
        if (metric.filter) {
          sql = sql.replace(
            new RegExp(columnName, 'gi'),
            (val) => `(CASE WHEN ${metric.filter} then ${val.split('.').at(-1)} ELSE NULL END)`,
          )
        }
      } else {
        throw new Error(`暂时不支持${metricName}指标`)
      }
      builder.select({ [metric.name]: sql })
    }
  }

  buildDimLayer({ builder, metricNames }: { builder: QueryBuilder; metricNames: string[] }) {
    // select distinct
    if (this.derivedMeta.timeSqlPart?.groupBy) {
      // 加上日期提参的 group by
      builder.select({ [DATE_ALIAS]: this.derivedMeta.timeSqlPart.groupBy })
    }
    this.derivedMeta.queryParams.groupBys?.forEach((groupBy) => {
      const dim = this.metricConfig.findDimensionByName(groupBy)
      builder.select(dimension2Sql(dim))
    })
    this.buildSimpleMetric(builder, metricNames)
    this.buildWhere(builder)
    this.buildGroupBy(builder)
  }

  buildSnapshotLatestLayer({ builder }: { builder: QueryBuilder }) {
    // select distinct
    if (this.derivedMeta.timeSqlPart?.groupBy) {
      // 加上日期提参的 group by
      builder.select({ [DATE_ALIAS]: this.derivedMeta.timeSqlPart.groupBy })
    }
    this.derivedMeta.queryParams.groupBys?.forEach((groupBy) => {
      const dim = this.metricConfig.findDimensionByName(groupBy)
      builder.select(dimension2Sql(dim))
    })
    // this.buildSimpleMetric(builder, metricNames)
    builder.max({
      [LAST_DATE]: this.metricConfig.timeDimensionDatum?.timeDimensionName,
    })
    this.buildWhere(builder)
    this.buildGroupBy(builder)
  }

  buildMaxSnapshotLayer({ builder, metricNames }: { builder: QueryBuilder; metricNames: string[] }) {
    this.appendCommonSelect(builder, SNAPSHOT_LAST_DATE_TBL)
    this.buildSimpleMetric(builder, metricNames)
    this.buildGroupBy(builder)
    builder.join(this.tables[0], (joinClause: knex.Knex.JoinClause) => {
      joinClause.andOn(
        this.metricConfig.timeDimensionDatum?.timeDimensionName ?? '',
        '=',
        concatTableName(SNAPSHOT_LAST_DATE_TBL, LAST_DATE),
      )
      this.appendCommonJoin(joinClause, this.tables[0], SNAPSHOT_LAST_DATE_TBL)
    })
    this.buildWhere(builder)
  }

  buildFixedSnapshotLayer({ builder, metricNames }: { builder: QueryBuilder; metricNames: string[] }) {
    // select distinct
    if (this.derivedMeta.timeSqlPart?.groupBy) {
      // 加上日期提参的 group by
      builder.select({ [DATE_ALIAS]: this.derivedMeta.timeSqlPart.groupBy })
    }
    this.derivedMeta.queryParams.groupBys?.forEach((groupBy) => {
      const dim = this.metricConfig.findDimensionByName(groupBy)
      builder.select(dimension2Sql(dim))
    })
    this.buildSimpleMetric(builder, metricNames)
    this.buildWhere(builder)
    this.buildGroupBy(builder)
    if (this.derivedMeta.timeSqlPart?.lastDate) {
      builder.whereRaw(this.derivedMeta.timeSqlPart.lastDate)
    }
  }

  flat(metricName: string) {
    const metric = this.metricConfig.findMetricByName(metricName)
    let sql = ''
    if (metric.type === 'derived') {
      sql = metric.typeParams.expr
      for (const { name } of metric.typeParams.metrics) {
        sql = sql.replace(new RegExp(name, 'g'), this.flat(name))
      }
    } else if (metric.type === 'ratio') {
      // TODO: 这里分母是错的
      sql = `${this.flat(metric.typeParams.numerator)} / ${this.flat(metric.typeParams.denominator)}`
    } else if (metric.type === 'simple') {
      return metric.name
    }
    return `(${sql})`
  }

  buildFlattedLayer({ builder, metricNames }: { builder: QueryBuilder; metricNames: string[] }) {
    this.appendCommonSelect(builder)
    for (const metricName of metricNames) {
      const metric = this.metricConfig.findMetricByName(metricName)
      const sql = this.flat(metricName)
      builder.select({ [metric.name]: sql })
    }
  }

  buildMixLayer({
    builder,
    simpleMetrics,
    nonCumulativeMetrics,
  }: {
    builder: QueryBuilder
    simpleMetrics: string[]
    nonCumulativeMetrics: string[]
  }) {
    if (this.derivedMeta.timeSqlPart?.groupBy) {
      // 加上日期提参的 group by
      builder.select(
        `COALESCE(${concatTableName(AGG_TBL, DATE_ALIAS)}, ${concatTableName(SNAPSHOT_TBL, DATE_ALIAS)}) AS ${DATE_ALIAS}`,
      )
    }
    this.derivedMeta.queryParams.groupBys?.forEach((groupBy) => {
      builder.select(
        `COALESCE(${concatTableName(AGG_TBL, groupBy)}, ${concatTableName(SNAPSHOT_TBL, groupBy)}) AS ${groupBy}`,
      )
    })
    simpleMetrics.forEach((name) => {
      builder.select({ [name]: concatTableName(AGG_TBL, name) })
    })
    nonCumulativeMetrics.forEach((name) => {
      builder.select({ [name]: concatTableName(SNAPSHOT_TBL, name) })
    })
    builder.fullOuterJoin(AGG_TBL, (joinClause) => this.appendCommonJoin(joinClause, AGG_TBL, SNAPSHOT_TBL))
  }

  buildFinalLayer({ builder, metricNames }: { builder: QueryBuilder; metricNames: string[] }) {
    this.appendCommonSelect(builder)
    metricNames.forEach((metricName) => {
      builder.select(metricName)
    })
    // from 直接拿上一张表
    builder.from(this.tables.at(-1)!)
    // last where
    builder.whereRaw(this.derivedMeta.whereDerived.lastWhere)
    // last orderby
    this.derivedMeta.queryParams.orderBys?.forEach((orderBy) => {
      builder.orderByRaw(orderBy)
    })
    // last limit
    if (this.derivedMeta.queryParams.limit) builder.limit(this.derivedMeta.queryParams.limit)
  }

  appendCommonSelect(builder: QueryBuilder, tableName?: string) {
    if (this.derivedMeta.timeSqlPart?.groupBy) {
      // 加上日期提参的 group by
      builder.select({ [DATE_ALIAS]: concatTableName(tableName, DATE_ALIAS) })
    }
    this.derivedMeta.queryParams.groupBys?.forEach((groupBy) => {
      builder.select({ [groupBy]: concatTableName(tableName, groupBy) })
    })
  }
  appendCommonJoin(joinClause: knex.Knex.JoinClause, table1: string, table2: string) {
    if (this.derivedMeta.timeSqlPart?.groupBy) {
      joinClause.andOn(
        table1 === this.tables[0] ? this.derivedMeta.timeSqlPart.groupBy : concatTableName(table1, DATE_ALIAS),
        '=',
        concatTableName(table2, DATE_ALIAS),
      )
    }
    this.derivedMeta.queryParams.groupBys?.forEach((groupBy) => {
      const dim = this.metricConfig.findDimensionByName(groupBy)
      joinClause.andOn(
        table1 === this.tables[0] ? dimension2Sql(dim, false) : concatTableName(table1, groupBy),
        '=',
        concatTableName(table2, groupBy),
      )
    })
  }

  toSql(): string {
    console.info('[GenerateSql.DerivedMeta]', JSON.stringify(this.derivedMeta))
    this.tables.push(this.metricConfig.name)
    if (this.derivedMeta.queryParams.periodOverPeriods && this.derivedMeta.queryParams.periodOverPeriods.length)
      throw new Error('不支持同环比')
    const builder = this.knex.queryBuilder()
    const metricNames = Array.from(
      new Set(this.derivedMeta.queryParams.metricNames.concat(this.derivedMeta.whereDerived.metricNames)),
    )
    const orderedMetricInfo = this.metricConfig.getOrderedMetrics(metricNames)
    const flattedMetricNames = orderedMetricInfo.levels.flat()
    const simpleMetrics = flattedMetricNames.filter((name) => {
      const metric = this.metricConfig.findMetricByName(name)
      return metric.type === 'simple' && metric.isCumulative
    })
    const nonCumulativeMetrics = flattedMetricNames.filter((name) => {
      const metric = this.metricConfig.findMetricByName(name)
      return metric.type === 'simple' && !metric.isCumulative
    })
    const derivedMetrics = flattedMetricNames.filter(
      (name) => this.metricConfig.findMetricByName(name).type !== 'simple' && metricNames.includes(name),
    )
    // 暂时跳过
    if (nonCumulativeMetrics.length === 0) throw new Error('不存在不可累加指标')
    if (derivedMetrics.some((name) => this.metricConfig.findMetricByName(name).type === 'ratio'))
      throw new Error('不支持比值指标')
    if (
      derivedMetrics.some((name) => {
        const metric = this.metricConfig.findMetricByName(name)
        return metric.type === 'derived' && !metric.isCumulative
      })
    )
      throw new Error('不支持不可累加的派生指标')
    // create layers
    if (simpleMetrics.length) {
      this.buildLayers.push({
        tableName: AGG_TBL,
        prevTable: this.tables[0],
        build: (builder) => this.buildDimLayer({ builder, metricNames: simpleMetrics }),
      })
    }
    if (nonCumulativeMetrics.length) {
      switch (this.config.snapshotDateMode) {
        case undefined:
        case 'max':
          this.buildLayers.push(
            {
              tableName: SNAPSHOT_LAST_DATE_TBL,
              prevTable: this.tables[0],
              build: (builder) => this.buildSnapshotLatestLayer({ builder }),
            },
            {
              tableName: SNAPSHOT_TBL,
              build: (builder) => this.buildMaxSnapshotLayer({ builder, metricNames: nonCumulativeMetrics }),
            },
          )
          break
        case 'fixed':
          this.buildLayers.push({
            tableName: SNAPSHOT_TBL,
            prevTable: this.tables[0],
            build: (builder) => this.buildFixedSnapshotLayer({ builder, metricNames: nonCumulativeMetrics }),
          })
          break
        default:
          assertExhaustive(this.config.snapshotDateMode)
      }
    }
    if (simpleMetrics.length && nonCumulativeMetrics.length) {
      this.buildLayers.push({
        tableName: MIX_TBL,
        build: (builder) => this.buildMixLayer({ builder, simpleMetrics, nonCumulativeMetrics }),
      })
    }
    if (derivedMetrics.length) {
      this.buildLayers.push({
        tableName: FLATTED_TBL,
        build: (builder) => {
          builder.select(...simpleMetrics, ...nonCumulativeMetrics)
          this.buildFlattedLayer({ builder, metricNames: derivedMetrics })
        },
      })
    }
    // build
    for (const { tableName, prevTable = this.tables.at(-1)!, build } of this.buildLayers.slice(0, -1)) {
      this.tables.push(tableName)
      builder.with(tableName, (internalBuilder) => {
        internalBuilder.from(prevTable)
        build(internalBuilder)
      })
    }
    // last table
    const { prevTable = this.tables.at(-1)!, build } = this.buildLayers.at(-1)!
    builder.from(prevTable)
    builder.whereRaw(this.derivedMeta.whereDerived.lastWhere)
    this.derivedMeta.queryParams.orderBys?.forEach((orderBy) => {
      builder.orderByRaw(orderBy)
    })
    if (this.derivedMeta.queryParams.limit) builder.limit(this.derivedMeta.queryParams.limit)
    build(builder)
    return builder.toQuery().replace('full outer join', 'full join').replace('inner join', 'join')
  }
}
