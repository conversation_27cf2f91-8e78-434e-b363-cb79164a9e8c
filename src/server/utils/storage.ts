import { AsyncLocalStorage } from 'async_hooks'
import { Request } from 'express'
import { DEFAULT_DOMAIN } from 'src/shared/auth'

export const asyncLocalStorage = new AsyncLocalStorage<{
  req: Request
  traceId?: string
  username?: string
  domain: string
}>()

export function getCurrentContext() {
  return asyncLocalStorage.getStore()
}

export function getDomainFromRequest(req: Request) {
  const { TENANT_SESSION_ID, PROJECT_SESSION_ID } = req.cookies ?? {}
  if (TENANT_SESSION_ID && PROJECT_SESSION_ID) {
    return ['', TENANT_SESSION_ID, PROJECT_SESSION_ID].join('/')
  }
  return DEFAULT_DOMAIN
}

export function getDomain() {
  return getCurrentContext()?.domain ?? DEFAULT_DOMAIN
}
