/* eslint-disable @typescript-eslint/naming-convention */
import { XGroup, XRole } from '@prisma/client'
import express, { Router, Request, Response } from 'express'
import axios from 'axios'
import jwt from 'jsonwebtoken'
import {
  AUTH_ACTION,
  AUTH_DOMAIN,
  AUTH_RESOURCE,
  AUTH_USER,
  AuthAdminUserListPostRequestSchema,
  BUILTIN_ROLES,
  contactData,
  contactUserData,
  encodeResourceRule,
  extractInfoFromEnforcer,
  getRoleFromGroup,
  resourceTypeList,
  ResourceTypes,
  TENANT_ADMIN,
} from 'src/shared/auth'
import { LlmType } from 'src/shared/common-types'
import { PageParam } from 'src/shared'
import { DEFAULT_SCENE_ID } from 'src/shared/metric-types'
import { asyncResponseWrapper } from 'src/server/asyncResponseWrapper'
import { getConfigManagement } from 'src/server/AskBI/configManagement/dao'
import { timeout } from 'src/shared/common-utils'
import { createUser, ALL_LLMS, decryptUserId, createResource, generateXengineUrl } from '../utils'
import {
  createAndGrantUrl,
  deleteProjectUser,
  PROCESS_ENV,
  rangerGetAuthToken,
  rangerLoginUrl,
  updateProjectUserUrl,
} from '../server-constants'
import MetricConfig from '../MetricStore/metric-config'
import { getDomain } from '../utils/storage'
import { prisma } from '../dao/db'
import { commonProxy } from '../commonProxy'
import { jwtOptions, passportMiddleware } from './passport'
import { enforcer } from './enforcer'

export function promiseTimeout<T>(promise: Promise<T>, { time }: { time?: number } = {}): Promise<T> {
  return Promise.race([promise, timeout(time)])
}

export async function getTokenFromRanger(username: string, password: string) {
  const base64Encoded = Buffer.from(password).toString('base64')
  const jsonData: {
    userName: string
    password: string
  } = {
    userName: username,
    password: base64Encoded,
  }
  console.info(`Ranger: 登录${rangerLoginUrl}, data=${JSON.stringify(jsonData)}, password=${password}`)
  try {
    let authToken = ''
    try {
      const res: any = await Promise.race([axios.get(rangerGetAuthToken), timeout()])
      authToken = res.data.data
    } catch (error) {
      console.error('rangerGetAuthToken error', error)
    }
    console.info(`Ranger: 获取unique-token=${authToken}`)
    const res: any = await axios.post(rangerLoginUrl, jsonData, {
      headers: {
        'unique-token': authToken, // 将 token 添加到 header
      },
    })
    if (res.data.code === 0) {
      return res.data.data
    } else {
      console.error('Login with range does not have cookie.')
      console.info(`Ranger: 登录失败,code!=0,data=${res.data}`)
      return null
    }
  } catch (error: any) {
    console.info(`Ranger: 登录失败异常, ${JSON.stringify(error?.response)}`)
    return null
  }
}

async function createAndGrant({
  username,
  password,
  projectAdmin,
}: {
  username: string
  password: string
  projectAdmin: boolean
}) {
  const data = {
    username,
    projectAdmin,
    permissions: [],
    password,
  }
  const res = await axios.post(createAndGrantUrl, data)
  console.info('===> createAndGrant Result', data, res.data)
}

// username: 'yg22',
// projectAdmin: true,
// permissions: [],
// password: 'yyyyGGGG1!'
async function deleteUser({ name }: { name: string }) {
  const res = await axios.delete(deleteProjectUser(name))
  console.info('===> deleteUser Result', name, res.data)
  if (res.data.code === 0) {
    return res.data.data
  }
}

async function updateProjectUser({
  username,
  projectAdmin,
  password,
}: {
  username: string
  projectAdmin: boolean
  password?: string
}) {
  const data = {
    projectAdmin,
    password,
  }
  const res = await axios.put(updateProjectUserUrl(username), data)
  console.info('===> updateProjectUser Result', data, res.data)
}

// async function getProjectUsers() {
//   const res = await axios.get(getProjectUsersUrl)
//   console.info('===> getProjectUsers Result', res.data)
//   return res.data as { name: string; projectAdmin: boolean }[]
// }

function createRouter() {
  const router: Router = express.Router()

  router.post(
    '/login',
    asyncResponseWrapper(async (req: Request, res: Response) => {
      const { username, password, isEncryption } = req.body
      const localUsername = isEncryption ? decryptUserId(username) : username
      const data: Record<string, any> = {}
      // 只用ranger 进行登录
      if (PROCESS_ENV.AUTH_LOGIN_SYSTEM === 'ranger') {
        const userInfo = await getTokenFromRanger(username, decryptUserId(password))
        console.info('Login with ranger', userInfo)
        if (userInfo) {
          data.ranger = { token: userInfo.jwtToken, uToken: userInfo.token }
        } else {
          return res.json({
            code: 400,
            data: {},
            msg: '账号或密码错误',
            message: '账号或密码错误',
          })
        }

        // 初始化角色 已经在 prisma seed.ts 做了兼容。
        let initRole = await prisma.xRole.findFirst({
          where: {
            roleName: 'init_role_with_all_scene_project_read_permission',
          },
        })

        if (!initRole) {
          initRole = await prisma.xRole.create({
            data: {
              roleName: 'init_role_with_all_scene_project_read_permission',
            },
          })
        }

        let user = await prisma.xUser.findFirst({ where: { username: localUsername } })
        // 客户不提供 mysql 的情况下，节点漂移登录态丢失，重新登录 ranger token 需要创建用户。
        // prisma/seed.ts 已经创建了角色和对应的资源
        if (!user) {
          user = await createUser({ username: localUsername, password, roles: [initRole.id] })
        }

        if (!user) throw new Error('登录失败')

        const token = jwt.sign({ username: user.username, id: user.id }, jwtOptions.secretOrKey, {
          expiresIn: '6h',
        })
        data.token = token

        return res.json({
          code: 0,
          data,
        })
      }
      const user = await prisma.xUser.findFirst({ where: { username: localUsername } })
      if (!(user && user.password === password)) throw new Error('用户名或密码错误')

      const userInfo = await getTokenFromRanger(localUsername, decryptUserId(user.password))
      console.info('Login with ranger', userInfo)
      if (userInfo) {
        data.ranger = {
          token: userInfo.jwtToken,
          uToken: userInfo.token,
        }
      }
      data.isAdmin = !!userInfo?.projectAdmin
      const jwtData = {
        username: user.username,
        id: user.id,
        isAdmin: data.isAdmin,
        domain: await enforcer.getDomainFromUserId(user.id),
      }
      console.info('JWT', jwtData)
      // 如果用户验证通过，生成JWT
      const token = jwt.sign(jwtData, jwtOptions.secretOrKey, {
        // 与XE对齐
        expiresIn: '6h',
      })
      data.token = token
      res.json({
        code: 0,
        data,
      })
    }),
  )

  // TODO 这个 在浦发上可能不行  浦发的登录信息存储在 session storage 里面
  router.post(
    '/logout',
    asyncResponseWrapper(async (req, res) => {
      try {
        if (req.headers.cookie) {
          const cookies = req.headers.cookie.split('; ')
          cookies.forEach((cookie) => {
            const [name] = cookie.split('=')
            res.clearCookie(name)
          })
        }
        return res.json({ code: 0, data: {}, msg: '退出登录成功' })
      } catch (error) {
        console.error('Logout error: ' + error)
        return res.json({ code: 0, msg: '退出登录失败' })
      }
    }),
  )

  // 需要JWT验证的受保护路由示例
  router.get(
    '/user-info',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      const userInfo = req.user!
      console.info(`GET ${req.url}: ${JSON.stringify(userInfo)}`)
      const groups = await enforcer.getGroupsFromUserId(userInfo.id)
      // 兼容老版本代码，把role提取到group数组中
      const roles = await enforcer.getRolesFromUserId(userInfo.id)
      return res.json({
        code: 0,
        data: {
          id: userInfo.id,
          username: userInfo.username,
          nickname: userInfo.nickname,
          groups: groups.concat(roles.map((v) => ({ id: v.id, groupName: v.roleName }))),
          isAdmin: userInfo.isAdmin,
          domain: userInfo.domain,
        },
      })
    }),
  )

  // const projectsCache: Record<string, any> = {}
  router.get(
    '/projects',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      console.info('====> GET /api/auth/projects', req.user)
      const userInfo = req.user!
      // const projects =
      //   projectsCache[userInfo.id] ?? (projectsCache[userInfo.id] = await enforcer.getProjectsFromUserId(userInfo.id))
      return res.json({
        code: 0,
        data: {
          projects: await enforcer.getProjectsFromUserId(userInfo.id),
          DEFAULT_SELECT_PROJECT: PROCESS_ENV.DEFAULT_SELECT_PROJECT,
          DEFAULT_PROJECT: PROCESS_ENV.DEFAULT_PROJECT,
          DEFAULT_SCENE: PROCESS_ENV.DEFAULT_SCENE,
        },
      })
    }),
  )

  router.get(
    '/llms',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      const userInfo = req.user!
      const llmList = await enforcer.getLlmsFromUserId(userInfo.id)
      // 获取当前用户的默认数据源
      const defaultLlmType: LlmType = llmList.some((llm) => llm.type === PROCESS_ENV.DEFAULT_LLM_MODEL)
        ? (PROCESS_ENV.DEFAULT_LLM_MODEL as LlmType)
        : llmList.length > 0
          ? llmList[0].type
          : (PROCESS_ENV.DEFAULT_LLM_MODEL as LlmType)
      return res.json({ code: 0, data: { llmList, defaultLlmType } })
    }),
  )

  router.get(
    '/metrics',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      let sceneId = req.query.sceneId as string
      console.info('Get metric list with query:', req.query)
      // console.info('Get metric req.user?.username:', req.user?.username)
      const userInfo = req.user!
      const username = userInfo.username || ''
      if (sceneId === DEFAULT_SCENE_ID) {
        const projects = await enforcer.getProjectsFromUserId(username)
        sceneId = projects[0]?.semanticScenes[0].id || ''
      }

      try {
        const metricConfig = await MetricConfig.createBySceneId(sceneId, false)
        // const metricList = metricConfig.allMetrics
        // const hotMetricList = metricConfig.hotMetrics

        // Assign displayExpr to each metric
        // metricList.forEach((metric) => (metric.displayExpr = metricConfig.getMetricDisplayExpr(metric)))
        // hotMetricList.forEach((metric) => (metric.displayExpr = metricConfig.getMetricDisplayExpr(metric)))
        return res.json({
          code: 0,
          data: metricConfig,
          // {
          //   metricTableName: metricConfig.name,
          //   allDimensions: metricConfig.allDimensions,
          //   allMeasures: metricConfig.allMeasures,
          //   allMetrics: metricList,
          //   hotMetrics: hotMetricList,
          //   allExternalReports: metricConfig.allExternalReports,
          //   timeDimensionDatum: metricConfig.timeDimensionDatum,
          // },
        })
      } catch (error) {
        console.error('获取该model下面的metric信息失败 ' + (error as Error)?.message, error)
        return res.json({ code: 500, msg: '获取该model下面的metric信息失败 ' + (error as Error)?.message })
      }
    }),
  )

  router.get(
    '/project/list',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      console.info('GET /project/list', req.query)
      const user = req.user!
      const projects = await enforcer.getProjectsFromUserId(user.id)
      return res.json({ code: 0, data: { list: projects, total: projects.length } })
    }),
  )

  router.post(
    '/project',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      console.info('POST', req.url, req.body)
      const { description, name } = req.body
      const data = await prisma.semanticProject.create({
        data: {
          name,
          description,
        },
      })
      await enforcer.addToDomain(contactData('project', data.id))
      return res.json({ code: 0, data })
    }),
  )

  router.get(
    '/project',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      const info = await enforcer.getInfoFromDomain()
      console.info('GET /project', req.query)
      const { id } = req.query
      if (!(info.project ?? []).includes(id as string)) throw new Error(`项目${id}不存在`)
      const data = await prisma.semanticProject.findFirst({
        where: { id: id as string },
      })
      if (!data) throw new Error(`项目${id}不存在`)

      return res.json({ code: 0, data })
    }),
  )

  router.put(
    '/project',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      console.info('PUT /project', req.body)
      const { id, name, description } = req.body
      const data = await prisma.semanticProject.update({
        where: { id },
        data: { name, description },
      })
      return res.json({ code: 0, data: data })
    }),
  )

  router.delete(
    '/project',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      console.info('DELETE /project', req.body)
      const { id } = req.body

      await prisma.$transaction(async () => {
        await prisma.semanticScene.deleteMany({ where: { semanticProjectId: id } })
        await prisma.semanticMetric.deleteMany({ where: { semanticProjectId: id } })
        await prisma.semanticExternalReport.deleteMany({ where: { semanticProjectId: id } })
        await prisma.semanticMetricTreeRoot.deleteMany({ where: { semanticProjectId: id } })
        await prisma.semanticMetricTree.deleteMany({ where: { semanticProjectId: id } })
        await prisma.semanticProject.delete({
          where: { id: id as string },
        })
        await prisma.casbinRule.deleteMany({ where: { [AUTH_USER]: contactData('g2', id), ptype: 'g2' } })
      })

      return res.json({ code: 0 })
    }),
  )

  router.get(
    '/scene/list',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      console.info('/scene/list', req.query)
      const user = req.user!
      const projects = await enforcer.getProjectsFromUserId(user.id)

      const { projectId } = req.query

      if (projectId) {
        const project = projects.find((v) => v.id === projectId)
        if (!project) throw new Error('没有项目权限')
        return res.json({ code: 0, data: { list: project.semanticScenes, total: project.semanticScenes.length } })
      }
      const scenes = projects.flatMap((v) => v.semanticScenes)
      return res.json({ code: 0, data: { list: scenes, total: scenes.length } })
    }),
  )

  router.get(
    '/scene',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      console.info('/scene', req.query)
      const { id } = req.query
      const data = await prisma.semanticScene.findFirst({
        where: { id: id as string },
        include: {
          semanticProject: {
            select: { id: true, name: true },
          },
        },
      })
      if (!data) throw new Error(`场景${id}不存在`)
      const sceneConfig = await getConfigManagement('scene', id as string)

      const modelNames = await prisma.semanticScenesModels.findMany({
        where: { sceneId: data.id },
      })
      ;(data as any).modelNames = modelNames.map((v) => v.modelName)

      return res.json({
        code: 0,
        data: { ...data, ...sceneConfig },
      })
    }),
  )

  router.delete(
    '/scene',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      console.info('DELETE /scene', req.body)
      const { id } = req.body
      const data = Promise.all([
        prisma.semanticScene.delete({ where: { id } }),
        prisma.semanticScenesModels.deleteMany({ where: { sceneId: id } }),
      ])
      return res.json({ code: 0, data })
    }),
  )

  router.post(
    '/scene',
    asyncResponseWrapper(async (req, res) => {
      console.info('POST /auth/scene', req.body)
      const info = await enforcer.getInfoFromDomain()
      const {
        agent,
        creationUser,
        label,
        modelId,
        modelNames,
        projectId,
        tableName,
        timeDimensionFormat,
        timeGranularityMin,
      } = req.body

      if (await prisma.semanticScene.count({ where: { label, id: { in: info.scene } } })) throw new Error('场景已存在')
      const scene = await prisma.semanticScene.create({
        data: {
          label,
          tableName,
          agent,
          semanticProjectId: projectId,
          timeDimensionFormat,
          timeGranularityMin,
          createdBy: creationUser,
          updatedBy: creationUser,
          modelId,
        },
      })
      if (modelNames && modelNames.length > 0) {
        await prisma.semanticScenesModels.createMany({
          data: modelNames.map((modelName: string) => ({
            sceneId: scene.id,
            modelName,
          })),
        })
      }
      await enforcer.addToDomain(contactData('scene', scene.id))
      return res.json({ code: 0, data: scene })
    }),
  )

  router.put(
    '/scene',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      console.info('PUT /scene', req.body)
      const { id, modelNames, tableName, timeDimensionFormat, timeDimensionType, timeGranularityMin } = req.body
      const scene = await prisma.semanticScene.update({
        where: { id },
        data: {
          tableName,
          timeDimensionFormat,
          timeDimensionType,
          timeGranularityMin,
        },
      })
      if (Array.isArray(modelNames)) {
        await prisma.$transaction(async () => {
          await prisma.semanticScenesModels.deleteMany({ where: { sceneId: id } })
          await prisma.semanticScenesModels.createMany({
            data: modelNames.map((modelName: string) => ({ sceneId: id, modelName })),
          })
        })
      }
      return res.json({ code: 0, data: scene })
    }),
  )

  router.get(
    '/admin/user/list',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      const info = await enforcer.getInfoFromDomain()
      console.info('GET /admin/user/list', req.query)
      // const list = await getProjectUsers()
      const { success, data, error } = AuthAdminUserListPostRequestSchema.safeParse(req.query)
      if (!success) {
        const message = error.errors
          .map((error) => `${error.path.join('.')} is ${error.message.toLowerCase()}`)
          .join(', ')
        throw new Error(`参数错误: ${message}`)
      }
      const pageParam = PageParam.from(data)
      const total = await prisma.xUser.count({
        where: {
          username: { contains: data.name },
          id: { in: info.user ?? [] },
        },
      })
      const users = await prisma.xUser.findMany({
        skip: pageParam.skip,
        take: pageParam.take,
        select: {
          id: true,
          username: true,
          createdAt: true,
          rangerUsername: true,
        },
        where: {
          username: { contains: data.name },
          id: { in: info.user ?? [] },
        },
      })
      type User = (typeof users)[number] & { roles: XRole[]; groups: XGroup[] }

      const gPolicies = await Promise.all(
        users.map((v) => enforcer.e.getFilteredGroupingPolicy(0, contactUserData(v.id), '')),
      )

      const userInfoList: { groups: string[]; roles: string[] }[] = []
      for (let i = 0; i < users.length; i++) {
        const user = users[i] as User
        user.groups = []
        user.roles = []
        const item: (typeof userInfoList)[number] = { groups: [], roles: [] }
        for (const p of gPolicies[i]) {
          const { type, id } = extractInfoFromEnforcer(getRoleFromGroup(p))
          if (type === 'group') {
            item.groups.push(id)
          } else if (type === 'role') {
            item.roles.push(id)
          }
        }
        userInfoList.push(item)
      }

      await Promise.all(
        users.map(async (v, i) => {
          const info = userInfoList[i]
          const user = v as User
          user.groups = await prisma.xGroup.findMany({ where: { id: { in: info.groups ?? [] } } })
          user.roles = await prisma.xRole.findMany({ where: { id: { in: info.roles ?? [] } } })
          if (await enforcer.hasRoleForUser(user.id, BUILTIN_ROLES[0].value)) {
            user.roles.push({
              id: BUILTIN_ROLES[0].value,
              roleName: BUILTIN_ROLES[0].label,
              createdAt: new Date(),
              updatedAt: new Date(),
            })
          }
          if (await enforcer.hasRoleForUser(user.id, BUILTIN_ROLES[1].value)) {
            user.roles.push({
              id: BUILTIN_ROLES[1].value,
              roleName: BUILTIN_ROLES[1].label,
              createdAt: new Date(),
              updatedAt: new Date(),
            })
          }
        }),
      )

      return res.json({ code: 0, data: { list: users, total } })
    }),
  )

  router.post(
    '/admin/user',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      console.info('POST /admin/user', req.body)
      // const info = await enforcer.getInfoFromDomain()
      const { username, password, roles = [], groups, roleNames, groupNames } = req.body
      const isExist = await prisma.xUser.findFirst({
        where: {
          username,
          // id: { in: info.user ?? [] }
        },
      })
      if (isExist) throw new Error('用户名不能重复')
      await createAndGrant({
        username,
        password: decryptUserId(password),
        projectAdmin: roles.includes(TENANT_ADMIN),
      })
      await prisma.$transaction(async () => {
        const user = await prisma.xUser.create({
          data: {
            username,
            password,
          },
        })
        if (Array.isArray(roles)) {
          await enforcer.unionUserRole({
            user: { id: user.id, type: 'user' },
            role: { type: 'roles', ids: roles },
          })
        }
        if (Array.isArray(roleNames)) {
          const idListFromNames = (
            await prisma.xRole.findMany({ where: { roleName: { in: roleNames } }, select: { id: true } })
          ).map((v) => v.id)
          await enforcer.unionUserRole({
            user: { id: user.id, type: 'user' },
            role: { type: 'roles', ids: idListFromNames },
          })
        }
        if (Array.isArray(groups)) {
          await enforcer.unionUserRole({
            user: { id: user.id, type: 'user' },
            role: { type: 'groups', ids: groups },
          })
        }
        if (Array.isArray(groupNames)) {
          const idListFromNames = (
            await prisma.xGroup.findMany({ where: { groupName: { in: groupNames } }, select: { id: true } })
          ).map((v) => v.id)
          await enforcer.unionUserRole({
            user: { id: user.id, type: 'user' },
            role: { type: 'groups', ids: idListFromNames },
          })
        }
        await enforcer.addToDomain(contactData('user', user.id))
        return res.json({ code: 0, data: user })
      })
    }),
  )

  router.post(
    '/admin/user/update',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      // const info = await enforcer.getInfoFromDomain()
      console.info('UPDATE /admin/user', req.body)
      const { username, id, password, roles, groups } = req.body
      if (!id) throw new Error('ID不存在')
      if (username) {
        const isExist = await prisma.xUser.findFirst({
          where: {
            username,
            //  id: { in: info.user ?? [] }
          },
        })
        if (isExist) throw new Error('用户名不能重复')
      }
      const user = await prisma.xUser.findFirst({
        where: {
          id,
          //  id: { in: info.user ?? [] }
        },
      })
      const data = { password }
      const updateRes = await prisma.xUser.update({
        data,
        where: { id },
        select: { id: true, username: true, createdAt: true },
      })
      if (Array.isArray(roles)) {
        await enforcer.unionUserRole({ user: { id, type: 'user' }, role: { type: 'roles', ids: roles } })
      }
      if (Array.isArray(groups)) {
        await enforcer.unionUserRole({ user: { id, type: 'user' }, role: { type: 'groups', ids: groups } })
      }
      await updateProjectUser({
        username: user!.username,
        password: password ? decryptUserId(password) : undefined,
        projectAdmin: roles.includes(TENANT_ADMIN),
      })
      return res.json({ code: 0, data: updateRes })
    }),
  )

  router.delete(
    '/admin/user',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      const { id } = req.body
      if (!id) throw new Error('ID不存在')
      const user = await prisma.xUser.findFirst({ where: { id } })
      if (!user) throw new Error('用户不存在')
      await prisma.xUser.delete({ where: { id } })
      await prisma.casbinRule.deleteMany({ where: { id: contactUserData(id) } })
      await enforcer.refresh()
      await deleteUser({ name: user.username })
      return res.json({ code: 0 })
    }),
  )

  router.get(
    '/admin/role/list',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      const info = await enforcer.getInfoFromDomain()
      console.info('GET /admin/role/list', req.query)
      const roles = await prisma.xRole.findMany({
        // skip,
        // take: pageSize,
        orderBy: {
          createdAt: 'desc',
        },
        select: {
          id: true,
          roleName: true,
          createdAt: true,
        },
        where: {
          roleName:
            typeof req.query.roleName === 'string'
              ? {
                  contains: req.query.roleName,
                }
              : undefined,
          id: { in: info.role ?? [] },
        },
      })

      roles.unshift(...BUILTIN_ROLES.map((v) => ({ roleName: v.label, id: v.value, createdAt: new Date() })))

      return res.json({ code: 0, data: { list: roles, total: roles.length } })
    }),
  )

  router.post(
    '/admin/role',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      // const info = await enforcer.getInfoFromDomain()
      const { roleName } = req.body
      const isExist = await prisma.xRole.findFirst({
        where: {
          roleName,
          // id: { in: info.role ?? [] }
        },
      })
      if (isExist) {
        return res.json({ code: 1, msg: '角色名不能重复' })
      }
      const createRes = await prisma.xRole.create({
        data: {
          roleName,
        },
      })
      await enforcer.addToDomain(contactData('role', createRes.id))
      return res.json({ code: 0, data: createRes })
    }),
  )

  router.post(
    '/admin/role/update',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      const { id, roleName } = req.body
      if (!id) {
        return res.json({ code: 1, msg: 'ID不存在' })
      }
      if (roleName) {
        const isExist = await prisma.xRole.findFirst({ where: { roleName } })
        if (isExist) {
          return res.json({ code: 1, msg: '角色名不能重复' })
        }
      }
      const updateRes = await prisma.xRole.update({
        data: { roleName },
        where: { id },
        select: { id: true, roleName: true, createdAt: true },
      })
      await enforcer.addToDomain(contactData('role', updateRes.id))
      return res.json({ code: 0, data: updateRes })
    }),
  )

  router.delete(
    '/admin/role',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      const { id } = req.body
      if (!id) {
        return res.json({ code: 1, msg: 'ID不存在' })
      }
      const user = await prisma.xRole.findFirst({ where: { id } })
      if (!user) {
        return res.json({ code: 1, msg: '角色不存在' })
      }
      await prisma.xRole.delete({ where: { id } })
      await prisma.casbinRule.deleteMany({ where: { [AUTH_USER]: contactData('role', id), ptype: 'g2' } })
      return res.json({ code: 0 })
    }),
  )
  /*
GROUP 相关的暂时用不到，后期用到了可以从role复制
  router.get(
    '/admin/group/list',
    passportMiddleware,
    asyncResponseWrapper(async (_, res) => {
      const roles = await prisma.xGroup.findMany({
        // skip,
        // take: pageSize,
        orderBy: {
          createdAt: 'desc',
        },
        select: {
          id: true,
          groupName: true,
          createdAt: true,
        },
      })

      return res.json({ code: 0, data: { list: roles, total: roles.length } })
    }),
  )

  router.post(
    '/admin/group',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      const { groupName } = req.body
      const isExist = await prisma.xGroup.findFirst({ where: { groupName } })
      if (isExist) {
        return res.json({ code: 1, msg: '用户组名不能重复' })
      }
      const createRes = await prisma.xGroup.create({
        data: {
          groupName,
        },
      })
      return res.json({ code: 0, data: createRes })
    }),
  )

  router.post(
    '/admin/group/update',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      const { id, groupName } = req.body
      if (!id) {
        return res.json({ code: 1, msg: 'ID不存在' })
      }
      if (groupName) {
        const isExist = await prisma.xGroup.findFirst({ where: { groupName } })
        if (isExist) {
          return res.json({ code: 1, msg: '用户组名不能重复' })
        }
      }
      const updateRes = await prisma.xGroup.update({
        data: { groupName },
        where: { id },
        select: { id: true, groupName: true, createdAt: true },
      })
      return res.json({ code: 0, data: updateRes })
    }),
  )

  router.delete(
    '/admin/group',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      const { id } = req.body
      if (!id) {
        return res.json({ code: 1, msg: 'ID不存在' })
      }
      const user = await prisma.xGroup.findFirst({ where: { id } })
      if (!user) {
        return res.json({ code: 1, msg: '用户组不存在' })
      }
      await prisma.xGroup.delete({ where: { id } })
      return res.json({ code: 0 })
    }),
  )
*/
  router.post(
    '/union',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      const { v0, v1 } = req.body
      await enforcer.unionUserRole({ user: v0, role: v1 })
      return res.json({ code: 0 })
    }),
  )

  router.get(
    '/admin/resource/list',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      console.info('GET /admin/resource/list', req.query)
      const info = await enforcer.getInfoFromDomain()
      const resList = await prisma.xResource.findMany({
        include: {
          rules: {
            distinct: [AUTH_USER],
            select: { [AUTH_USER]: true, [AUTH_ACTION]: true },
          },
        },
        where: {
          name: typeof req.query.name === 'string' ? { contains: req.query.name } : undefined,
          type: typeof req.query.type === 'string' ? req.query.type : undefined,
          id: { in: info.resource ?? [] },
        },
      })
      const infoData: Record<Partial<ResourceTypes>, string[]> = { project: [], scene: [], llm: [], page: [], key: [] }
      for (const resource of resList) {
        const { type, typeData } = resource
        if (typeData && (type === 'project' || type === 'llm' || type === 'scene' || type === 'key')) {
          infoData[type as ResourceTypes] ??= []
          if (typeof typeData === 'string') {
            infoData[type as ResourceTypes].push(typeData)
          } else if (Array.isArray(typeData)) {
            infoData[type as ResourceTypes].push(...(typeData as string[]))
          }
        }
      }
      const infoResult: Record<ResourceTypes, any[]> = { project: [], scene: [], llm: [], page: [], key: [] }
      for (const { value } of resourceTypeList) {
        const ids = infoData[value]
        if (!ids) continue
        if (value === 'project') {
          infoResult[value] = await prisma.semanticProject.findMany({ where: { id: { in: ids } } })
        } else if (value === 'llm') {
          infoResult[value] = ALL_LLMS.filter((v) => ids.includes(v.id))
        } else if (value === 'scene') {
          infoResult[value] = await prisma.semanticScene.findMany({
            where: { id: { in: ids } },
            include: {
              semanticProject: {
                select: { id: true, name: true },
              },
            },
          })
        }
      }
      return res.json({ code: 0, data: { list: resList, total: resList.length, info: infoResult } })
    }),
  )

  router.get(
    '/admin/resource/data',
    passportMiddleware,
    asyncResponseWrapper(async (_, res) => {
      const info = await enforcer.getInfoFromDomain()
      const [projects, llms, roles, groups, users] = await Promise.all([
        prisma.semanticProject
          .findMany({ include: { semanticScenes: true }, where: { id: { in: info.project ?? [] } } })
          .then((projects) => enforcer.normalizeProjects(projects)),
        Promise.resolve(ALL_LLMS),
        prisma.xRole.findMany({
          select: { id: true, roleName: true },
          where: { id: { in: info.role ?? [] } },
        }),
        prisma.xGroup.findMany({
          select: { id: true, groupName: true },
          where: { id: { in: info.group ?? [] } },
        }),
        prisma.xUser.findMany({
          select: { id: true, username: true },
          where: { id: { in: info.user ?? [] } },
        }),
      ])
      roles.unshift(...BUILTIN_ROLES.map((v) => ({ roleName: v.label, id: v.value, createdAt: new Date() })))
      return res.json({ code: 0, data: { projects, llms, roles, groups, users } })
    }),
  )

  router.post(
    '/admin/resource',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      console.info('POST /admin/resource', req.body)
      const { name, type, typeData, rules = [] } = req.body
      const resource = await createResource(name, type, typeData, rules)
      await enforcer.addToDomain(contactData('resource', resource.id))
      await enforcer.refresh()
      return res.json({ code: 0, data: { resource } })
    }),
  )

  router.put(
    '/admin/resource',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      console.info('PUT /admin/resource', req.body)
      const { id: resourceId, name, type, typeData, rules } = req.body
      await prisma.$transaction(async () => {
        await prisma.xResource.update({
          where: { id: resourceId },
          data: { name, type, typeData },
        })
        await prisma.casbinRule.deleteMany({ where: { resourceId } })
        await prisma.casbinRule.createMany({
          data: rules
            .map(({ type: ownerType, id: ownerId, action }: Record<string, string>) => {
              return encodeResourceRule(type, typeData).map((resource) => {
                return {
                  ptype: 'p',
                  [AUTH_USER]: `${ownerType}:${ownerId}`,
                  [AUTH_RESOURCE]: resource,
                  [AUTH_ACTION]: action,
                  [AUTH_DOMAIN]: getDomain(),
                  resourceId,
                }
              })
            })
            .flat(),
        })
        await enforcer.refresh()
      })
      return res.json({ code: 0 })
    }),
  )

  router.delete(
    '/admin/resource',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      const { id } = req.body
      await prisma.casbinRule.deleteMany({ where: { resourceId: id } })
      await prisma.casbinRule.deleteMany({ where: { [AUTH_USER]: contactData('resource', id) } })
      const data = await prisma.xResource.delete({
        where: { id },
      })
      await enforcer.refresh()
      return res.json({ code: 0, data })
    }),
  )

  router.get(
    '/enforce',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      if (!req.user) return res.json({ code: 0, data: false })
      const userInfo = req.user!
      console.info('GET /auth/enforce', req.query, userInfo.id)
      const { resource, action } = req.query
      const can = resource && action ? await enforcer.e.enforce(contactUserData(userInfo.id), resource, action) : false
      return res.json({ code: 0, data: can })
    }),
  )

  router.post(
    '/enforce',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      if (!req.user) return res.json({ code: 0, data: [] })
      const userInfo = req.user!
      console.info('POST /auth/enforce', req.query, userInfo.id)
      const list: { resource: string; action: string }[] = req.body.list
      const resultList = await enforcer.e.batchEnforce(
        list.map(({ resource, action }) => [contactUserData(userInfo.id), getDomain(), resource, action]),
      )
      return res.json({ code: 0, data: resultList })
    }),
  )

  router.get(
    '/authorization',
    asyncResponseWrapper(async (req, res) => {
      const { v0 = '', v1 = '', v2 = '' } = req.query
      const can = await enforcer.e.enforceEx(v0, v1, v2)
      return res.json({ code: 0, data: can })
    }),
  )

  router.all('/*', async (req, res) => {
    try {
      const { path, baseUrl } = req

      const url = generateXengineUrl(path, baseUrl)
      return await commonProxy(req, res, url)
    } catch (error: any) {
      console.error(`出现错误，请求地址${req.url}`, error?.message || error)
      return res.status(500).json({ code: 500, msg: error?.message || '服务端错误' })
    }
  })
  return router
}

export const router = createRouter()
