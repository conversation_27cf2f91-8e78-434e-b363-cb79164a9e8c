import { useBoolean, useRequest } from 'ahooks'
import axios from 'axios'
import { atom, useSetAtom } from 'jotai'
import { useEffect, useState } from 'react'
import { APIResponse } from 'src/shared/common-types'
import { Product, ROLE_PAGE_LIST } from 'src/shared/products'
import { askBIApiUrls } from 'src/shared/url-map'
import { currentLoginUserAtom, envAtom } from '../pages/AskBI/askBIAtoms'

const AUTO_AUTH_LIST = [
  { key: 'dashBoard', action: 'read', resource: `page:/xengine/dashboard/info` },
  { key: 'metricStore', action: 'read', resource: `page:/metric-store` },
  { key: 'manage', action: 'read', resource: `page:/manage` },
  { key: 'metricStoreMetrics', action: 'read', resource: `page:/metric-store/metrics` },
  { key: 'metricStoreDimensions', action: 'read', resource: `page:/metric-store/dimensions` },
  { key: 'metricStoreMetricTree', action: 'read', resource: `page:/metric-store/metric-tree` },
  { key: 'metricStoreCharts', action: 'read', resource: `page:/metric-store/charts` },
  { key: 'metricStoreDocument', action: 'read', resource: `page:/metric-store/document` },
  { key: 'metricStoreAskHistory', action: 'read', resource: `page:/metric-store/ask-history` },
  { key: 'metricStoreHint', action: 'read', resource: `page:/metric-store/hint` },
  { key: 'metricStoreExternalReport', action: 'read', resource: `page:/metric-store/external-report` },
  { key: 'metricStoreSmartReport', action: 'read', resource: `page:/metric-store/smart-report` },
  { key: 'allowedRanger', action: 'read', resource: `page:/xengine/ranger` },
  { key: 'manageAdminResource', action: 'read', resource: 'page:/manage/admin/resource' },
  { key: 'manageAdminRole', action: 'read', resource: 'page:/manage/admin/role' },
  { key: 'manageAdminUser', action: 'read', resource: 'page:/manage/admin/user' },
  { key: 'chat', action: 'read', resource: 'page:/chat' },
  { key: 'manageProjectList', action: 'read', resource: 'page:/manage/project/list' },
  { key: 'manageScenario', action: 'read', resource: 'page:/manage/scenario' },

  { key: 'manageDataScene', action: 'read', resource: 'page:/manage/data-scene' },
  { key: 'manageExternalDatasource', action: 'read', resource: 'page:/manage/external-datasource' },
  { key: 'manageFileDatasource', action: 'read', resource: 'page:/manage/file-datasource' },
  { key: 'manageDataModel', action: 'read', resource: 'page:/manage/data-model' },
  { key: 'manageMetricModel', action: 'read', resource: 'page:/manage/metric-model' },
  { key: 'manageErManagement', action: 'read', resource: 'page:/manage/er-management/er-management' },
  { key: 'manageMaterialization', action: 'read', resource: 'page:/manage/materialization' },
  { key: 'manageSqlQuery', action: 'read', resource: 'page:/manage/sql-query/sql-query' },
  { key: 'manageOperation', action: 'read', resource: 'page:/manage/operation' },
  { key: 'manageTenant', action: 'read', resource: 'page:/manage/tenant' },
] as const

export const authAtom = atom({} as Record<(typeof AUTO_AUTH_LIST)[number]['key'], boolean>)

export function useAutoAuth() {
  const setAuthAtom = useSetAtom(authAtom)
  const { data } = useBatchAuth({
    list: AUTO_AUTH_LIST.map(({ action, resource }) => ({ action, resource })),
  })
  useEffect(() => {
    const auth = AUTO_AUTH_LIST.reduce(
      (o, { key }, i) => {
        o[key] = !!data[i]
        return o
      },
      {} as Record<(typeof AUTO_AUTH_LIST)[number]['key'], boolean>,
    )
    console.info('[Auth]', auth)
    setAuthAtom(auth)
  }, [data, setAuthAtom])
}

export function useAuth(props: { resource: string; action: string }) {
  const { resource, action } = props
  const { data, loading } = useRequest(async () => {
    const res = await axios.get<APIResponse<boolean>>(askBIApiUrls.auth.enforce, {
      params: { resource, action },
    })
    if (res.data.code === 0) {
      return !!res.data.data
    }
    return false
  })
  return { data, loading }
}

async function batchEnforce({ ac, list }: { ac: AbortController; list: { resource: string; action: string }[] }) {
  const res = await axios.post<APIResponse<boolean[]>>(askBIApiUrls.auth.enforce, { list }, { signal: ac.signal })
  if (res.data.code === 0) {
    return res.data.data
  }
  return []
}

export function useBatchAuth({
  list,
  force = false,
}: {
  list: { resource: string; action: string }[]
  force?: boolean
}) {
  const allowedPageSet = useAllowedPageSet()
  const [data, setData] = useState<boolean[]>([])
  const [loading, loadingOps] = useBoolean(false)
  useEffect(() => {
    const ac = new AbortController()
    loadingOps.setTrue()
    batchEnforce({ ac, list })
      .then((data) => {
        if (data) {
          for (let i = 0; i < list.length; i++) {
            const item = list[i]
            if (item.action === 'read' && allowedPageSet.has(item.resource.split(':')[1])) {
              data[i] = true
            }
          }
        }
        setData(data ?? [])
      })
      .catch((err) => {
        if (err.code !== 'ERR_CANCELED') {
          console.info('BatchEnforce Error', err)
        }
      })
      .finally(() => {
        loadingOps.setFalse()
      })
    return () => {
      ac.abort()
      loadingOps.setFalse()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [loadingOps, force])
  return { data, loading }
}

function useAllowedPageSet() {
  const userInfo = useAtomValue(currentLoginUserAtom)
  const env = useAtomValue(envAtom)
  const products = env['VITE_PRODUCTS'].split(',') as Product[]
  const set = new Set<string>(products.map((product) => ROLE_PAGE_LIST[product]?.user ?? []).flat())
  if (userInfo?.isAdmin) {
    products
      .map((product) => ROLE_PAGE_LIST[product]?.admin ?? [])
      .flat()
      .forEach((page) => set.add(page))
  }
  return set
}
