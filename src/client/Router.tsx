/**
 * @description 路由文件
 */
import React, { PropsWithChildren, useEffect, useState } from 'react'
import { createBrowserRouter, Navigate, RouteObject, RouterProvider } from 'react-router-dom'
import axios from 'axios'
import { use<PERSON>tom, useAtomValue, useSet<PERSON>tom } from 'jotai'
import { routerMap } from '@XEngineRouter/routerMap'
import { chatPath, XEngineHomePath } from 'src/shared/constants'
import { cachedDynamicImportComponent } from 'src/shared/common-utils'
import { askBIApiUrls, askBIPageUrls } from 'src/shared/url-map'

const AskBILayout = cachedDynamicImportComponent(() =>
  import('./components/layout/AskBILayout').then((mod) => ({ default: mod.AskBILayout })),
)

const ErrorPage = cachedDynamicImportComponent(() => import('./pages/ErrorPage'))
const HomePage = cachedDynamicImportComponent(() => import('./pages/AskBI/Home/Home'))
const SystemInfo = cachedDynamicImportComponent(() => import('./pages/AskBI/admin/SystemInfo/SystemInfo'))
const Users = cachedDynamicImportComponent(() => import('./pages/AskBI/admin/Users/<USER>'))
const Groups = cachedDynamicImportComponent(() => import('./pages/AskBI/admin/Groups/Groups'))
const Resources = cachedDynamicImportComponent(() => import('./pages/AskBI/admin/Resources/Resources'))
const Roles = cachedDynamicImportComponent(() => import('./pages/AskBI/admin/Roles/Roles'))
const Permissions = cachedDynamicImportComponent(() => import('./pages/AskBI/admin/Permissions/Permissions'))
const ChartEmbedPage = cachedDynamicImportComponent(() => import('./pages/AskBI/ChartEmbed/ChartEmbed'))
const ChatPageWrapper = cachedDynamicImportComponent(() => import('./pages/AskBI/ChatPageWrapper/ChatPageWrapper'))
const Page404 = cachedDynamicImportComponent(() => import('./pages/Page404'))
const Login = cachedDynamicImportComponent(() => import('./pages/Login'))
const LoginError = cachedDynamicImportComponent(() => import('./pages/LoginError'))
const AskBIMetricsLayout = cachedDynamicImportComponent(() =>
  import('./components/layout/AskBIMetricsLayout').then((mod) => ({ default: mod.AskBIMetricsLayout })),
)
const AskBIDashboardLayout = cachedDynamicImportComponent(() =>
  import('./components/layout/AskBIDashboardLayout').then((mod) => ({ default: mod.AskBIDashboardLayout })),
)

// 监控看板
const DashboardInfo = cachedDynamicImportComponent(() => import('./pages/Dashboard/DashboardInfo/DashboardInfo'))
const CodeStatistics = cachedDynamicImportComponent(() => import('./pages/Dashboard/CodeCollect/CodeStatistics'))
const CodeOverview = cachedDynamicImportComponent(() => import('./pages/Dashboard/CodeCollect/CodeOverview'))

const ChartManagePage = cachedDynamicImportComponent(() => import('./pages/AskBI/ChartManage/ChartManage'))
const ChartEdit = cachedDynamicImportComponent(() => import('./pages/AskBI/ChartManage/ChartEdit'))
const ChartDetail = cachedDynamicImportComponent(() => import('./pages/AskBI/ChartManage/ChartDetail'))
// 指标相关的子界面
const DimensionOverview = cachedDynamicImportComponent(() => import('./pages/MetricStore/Dimensions/Overview'))
const DimensionCreate = cachedDynamicImportComponent(() => import('./pages/MetricStore/Dimensions/Create'))
const DimensionManage = cachedDynamicImportComponent(() => import('./pages/MetricStore/Dimensions/Manage'))
const MeasureCreate = cachedDynamicImportComponent(() => import('./pages/MetricStore/Measures/Create'))
const MeasureManage = cachedDynamicImportComponent(() => import('./pages/MetricStore/Measures/Manage'))
const MetricsOverview = cachedDynamicImportComponent(() => import('./pages/MetricStore/Metrics/Overview'))
const MetricDetail = cachedDynamicImportComponent(() => import('./pages/MetricStore/Metrics/Detail'))
const MetricsCreate = cachedDynamicImportComponent(() => import('./pages/MetricStore/Metrics/Create'))
const MetricsManage = cachedDynamicImportComponent(() => import('./pages/MetricStore/Metrics/Manage'))
const ScenarioManage = cachedDynamicImportComponent(() => import('./pages/AskBI/Scenario/Scenario'))
const ScenarioDetail = cachedDynamicImportComponent(() => import('./pages/AskBI/Scenario/Detail'))
const StreamJobManage = cachedDynamicImportComponent(() => import('./pages/AskBI/StreamJob/StreamJobManage'))
const MetricTreeOverview = cachedDynamicImportComponent(() => import('./pages/MetricStore/MetricTree/Overview'))
const MetricTreeDetail = cachedDynamicImportComponent(() => import('./pages/MetricStore/MetricTree/Detail'))
const ExecuteSql = cachedDynamicImportComponent(() => import('./pages/AskBI/admin/ExecuteSql/ExecuteSql'))
const AskBIManageLayout = cachedDynamicImportComponent(() =>
  import('./components/layout/AskBIManageLayout').then((mod) => ({ default: mod.AskBIManageLayout })),
)
const AskBotLayout = cachedDynamicImportComponent(() => import('./components/layout/askbot-layout'))
// 外部数据源
const ExternalCatalogList = cachedDynamicImportComponent(
  () => import('./pages/MetricStore/ExternalDatasource/CatalogList'),
)
const ExternalTableList = cachedDynamicImportComponent(() => import('./pages/MetricStore/ExternalDatasource/TableList'))
const ExternalTableDetail = cachedDynamicImportComponent(
  () => import('./pages/MetricStore/ExternalDatasource/TableDetail'),
)
const ExternalCatalogUpdate = cachedDynamicImportComponent(
  () => import('./pages/MetricStore/ExternalDatasource/CatalogUpdate'),
)
const ExternalReportManagement = cachedDynamicImportComponent(
  () => import('./pages/MetricStore/ExternalReport/Management'),
)
const BatchInfo = cachedDynamicImportComponent(() => import('./pages/MetricStore/BatchInfo/BatchErrorInfo'))
const AskHistoryOverView = cachedDynamicImportComponent(() => import('./pages/MetricStore/AskHistory/Overview'))
const HintOverview = cachedDynamicImportComponent(() => import('./pages/MetricStore/Hint/Overview'))
// 项目
const ProjectList = cachedDynamicImportComponent(() => import('./pages/MetricStore/Project/List'))
// 指标模型
const MetricModelList = cachedDynamicImportComponent(() => import('./pages/MetricStore/MetricModel/List'))
const MetricModelQuery = cachedDynamicImportComponent(() => import('./pages/MetricStore/MetricModel/Query'))
const IFrame = cachedDynamicImportComponent(() => import('./pages/AskBI/IFrame/IFrame'))
const ScenarioCreate = cachedDynamicImportComponent(() => import('./pages/AskBI/Scenario/Create'))
const DocumentView = cachedDynamicImportComponent(() => import('./pages/AskDoc/DocumentView'))
const Chat = cachedDynamicImportComponent(() => import('./pages/chat'))
// 数据场景
const DataScene = cachedDynamicImportComponent(() => import('./pages/AskBI/DIDataManage/DataScene'))
// 集群管理
const ClusterList = cachedDynamicImportComponent(() => import('./pages/MetricStore/Cluster/List'))
const UI = cachedDynamicImportComponent(() => import('./pages/ui'))

import {
  currentLoginUserAtom,
  envAtom,
  isOpenTenantAtom,
  lastTenantSelectionAtom,
  loginAppIdAtom,
  tenantListAtom,
} from './pages/AskBI/askBIAtoms'
import TemplateManagement from './pages/MetricStore/SmartReport/TemplateManagement'
import ReportGeneration from './pages/MetricStore/SmartReport/ReportGeneration'
import ReportManagement from './pages/MetricStore/SmartReport/ReportManagement'
import MarkingTaskManagement from './pages/MetricStore/SmartReport/MarkingTaskManagement'
import { formatPathWithBaseUrl } from './utils'
import AppearanceSetting from './pages/System/Appearance/Appearance'
import TenantModal from './components/TenantModal/TenantModal'

/**
 * 原来只在管理页存在
 * 改造后应该为 只要登录了 就应该要选择租户
 */
export const TenantWrapper = ({ children }: PropsWithChildren<{}>) => {
  // const location = useLocation()
  const [showTenantModal, setShowTenantModal] = useState(false)
  const setTenantList = useSetAtom(tenantListAtom)
  const setIsOpenTenant = useSetAtom(isOpenTenantAtom)
  const setLastTenantSelection = useSetAtom(lastTenantSelectionAtom)
  const isOpenTenant = useAtomValue(isOpenTenantAtom)
  const userInfo = useAtomValue(currentLoginUserAtom)

  useEffect(() => {
    if (window.location.pathname.startsWith('/login')) return
    // if (userInfo?.id !== 'root') return
    const checkTenantState = async () => {
      try {
        const tenantResponse = await axios.get(askBIApiUrls.xengine.tenant.getTenantState)
        setIsOpenTenant(tenantResponse.data.data)
      } catch (error) {
        console.error('Failed to get tenant state:', error)
      }
    }

    checkTenantState()
  }, [isOpenTenant, setIsOpenTenant, userInfo])

  useEffect(() => {
    const initializeTenant = async () => {
      if (!isOpenTenant) return
      // if (userInfo?.id !== 'root') return
      if (window.location.pathname.startsWith('/login')) return

      try {
        // 1. 获取租户列表
        const response = await axios.get(askBIApiUrls.xengine.tenant.list)
        let normalTenants: any[] = []

        if (response.data?.data) {
          if (Array.isArray(response.data.data)) {
            normalTenants = response.data.data.filter((tenant: { state: string }) => tenant.state === 'NORMAL')
          } else if (response.data.data.list && Array.isArray(response.data.data.list)) {
            normalTenants = response.data.data.list.filter((tenant: { state: string }) => tenant.state === 'NORMAL')
          }
          setTenantList(normalTenants)

          // 2. 检查本地存储的租户信息
          // if (!window.location.pathname.startsWith('/login')) {
          const selectedTenant = localStorage.getItem('lastTenantSelection')

          if (selectedTenant) {
            try {
              const parsedTenant = JSON.parse(selectedTenant)
              const tenantExists = normalTenants.some((tenant) => tenant.tenantName === parsedTenant.tenant)
              if (!tenantExists) {
                // 如果之前选择的租户不在当前列表中，清除选择并显示选择框
                localStorage.removeItem('lastTenantSelection')
                setShowTenantModal(true)
                document.cookie = 'TENANT_SESSION_ID=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax'
                document.cookie = 'PROJECT_SESSION_ID=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax'
              } else {
                document.cookie = `TENANT_SESSION_ID=${parsedTenant.tenant}; path=/; SameSite=Lax`
                document.cookie = `PROJECT_SESSION_ID=${parsedTenant.project}; path=/; SameSite=Lax`
              }
            } catch (error) {
              console.error('Invalid tenant selection format:', error)
              localStorage.removeItem('lastTenantSelection')
              setShowTenantModal(true)
            }
          } else {
            setShowTenantModal(true)
          }
          // } else {
          //   setShowTenantModal(false)
          // }
        }
      } catch (error) {
        console.error('Failed to fetch tenant list:', error)
      }
    }

    initializeTenant()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [setTenantList, isOpenTenant, userInfo])

  const handleTenantSelect = (tenantInfo: { tenant: string; catalogName: string; project: string }) => {
    setLastTenantSelection(tenantInfo)
    setShowTenantModal(false)
    window.location.reload()
  }

  return (
    <>
      <TenantModal visible={showTenantModal} onSelect={handleTenantSelect} />
      {children}
    </>
  )
}

/**
 * 递归处理每一个路由，如果它没有BASE_URL前缀，就加上，然后遍历子路由
 */
function formatRoutesWithBaseUrl(routes: RouteObject[]) {
  for (const route of routes) {
    route.path = formatPathWithBaseUrl(route.path)
    formatRoutesWithBaseUrl(route?.children ?? [])
  }
  return routes
}

// const router = createBrowserRouter([
const getRouter = ({ defaultRouterPath }: { defaultRouterPath: string }) => {
  return createBrowserRouter(
    formatRoutesWithBaseUrl([
      {
        path: '/ng',
        element: <AskBotLayout />,
        children: [
          {
            path: 'ui',
            element: <UI />,
          },
          {
            path: 'chat',
            element: <Chat />,
          },
        ],
      },
      {
        path: '/',
        element: <AskBILayout />,
        children: [
          {
            path: '/',
            element: <Navigate to={defaultRouterPath} replace />,
          },
          {
            path: chatPath,
            element: <HomePage />,
          },
          {
            path: 'chat/:conversationId',
            element: <ChatPageWrapper />,
          },
        ],
        errorElement: <ErrorPage />,
      },
      {
        path: '/charts/embed/:chartId',
        element: <ChartEmbedPage />,
      },
      {
        path: '/scenario/create',
        element: <ScenarioCreate />,
      },
      {
        path: '/manage',
        element: (
          <>
            <AskBIManageLayout />
          </>
        ),
        children: [
          {
            ...routerMap.SQLQuery.sqlQueryEditor,
          },
          { ...routerMap.dataModel.virtualTable },
          { ...routerMap.dataModel.businessVirtualTable },
          {
            ...routerMap.dataModel.virtualTableMaterializationRecommend,
          },
          { ...routerMap.dataModel.createBusinessVirtualTable },
          { ...routerMap.dataModel.createSpecialVirtualTable },
          { ...routerMap.dataModel.createVirtualTable },
          { ...routerMap.dataModel.virtualTableDetail },
          { ...routerMap.dataModel.businessVirtualTableDetail },
          { ...routerMap.dataModel.catalogManager },
          { ...routerMap.ERManagement.erManager },
          { ...routerMap.ERManagement.smartERQuery },
          { ...routerMap.tenant.selectAllTenants },
          {
            ...routerMap.smartx.materialViewList,
          },
          {
            ...routerMap.smartx.createMaterialView,
          },
          {
            ...routerMap.smartx.materialViewDetail,
          },
          {
            ...routerMap.smartx.smartQueryDetail,
          },
          {
            ...routerMap.smartx.smartQueryList,
          },
          {
            ...routerMap.smartx.smartQueryHiveDetail,
          },
          {
            ...routerMap.smartx.smartQueryTableDetail,
          },
          {
            ...routerMap.smartx.smartMVScan,
          },
          {
            ...routerMap.smartx.mvJobList,
          },
          {
            ...routerMap.smartx.smartMVScan,
          },
          {
            ...routerMap.ERManagement.queryRelatedMV,
          },
          {
            ...routerMap.advance.logFileList,
          },
          {
            ...routerMap.advance.toolsBox,
          },
          {
            ...routerMap.advance.dataComparison,
          },
          {
            ...routerMap.SQLQuery.sqlHistory,
          },
          {
            ...routerMap.SQLQuery.sqlQueryManage,
          },
          {
            ...routerMap.error.httpErrorShow,
          },
          {
            ...routerMap.batchInfo,
          },
          { ...routerMap.permission },
          {
            ...routerMap.fileDatasource,
          },
          {
            path: 'scenario',
            element: <ScenarioManage />,
          },
          {
            path: 'scenario/detail',
            element: <ScenarioDetail />,
          },
          {
            path: 'operation/advance/stream-job-manage',
            element: <StreamJobManage />,
          },
          {
            path: 'admin/role',
            element: <Roles />,
          },
          {
            path: 'admin/user',
            element: <Users />,
          },
          {
            path: 'admin/group',
            element: <Groups />,
          },
          {
            path: 'admin/resource',
            element: <Resources />,
          },
          {
            path: 'admin/permissions',
            element: <Permissions />,
          },
          {
            path: 'admin/system-info',
            element: <SystemInfo />,
          },
          {
            path: 'admin/execute-sql',
            element: <ExecuteSql />,
          },
          // 外部数据源
          {
            path: 'external-datasource/catalog-list',
            element: <ExternalCatalogList />,
          },
          {
            path: 'external-datasource/table-list',
            element: <ExternalTableList />,
          },
          {
            path: 'external-datasource/table-list/table-detail',
            element: <ExternalTableDetail />,
          },
          {
            path: 'external-datasource/catalog-list/catalog-update',
            element: <ExternalCatalogUpdate />,
          },
          {
            path: 'external-datasource/batch-info',
            element: <BatchInfo />,
          },
          {
            path: 'data-scene',
            element: <DataScene />,
          },
          // 项目
          {
            path: 'project/list',
            element: <ProjectList />,
          },
          {
            path: 'project/detail',
            element: <ScenarioManage />,
          },
          // 指标模型
          {
            path: 'metric-model/list',
            element: <MetricModelList />,
          },
          {
            path: askBIPageUrls.manage.metricModel.query,
            element: <MetricModelQuery />,
          },
          // 集群管理
          {
            path: askBIPageUrls.manage.cluster.list,
            element: <ClusterList />,
          },
          {
            // 系统设置-外观
            path: 'system/appearance-setting',
            element: <AppearanceSetting />,
          },
          {
            path: '*',
            element: <Page404 />,
          },
        ],
      },

      {
        path: '/metric-store',
        element: <AskBIMetricsLayout />,
        children: [
          {
            path: 'dimensions/overview',
            element: <DimensionOverview />,
          },
          {
            path: 'dimensions/manage',
            element: <DimensionManage />,
          },
          {
            path: 'dimensions/create',
            element: <DimensionCreate />,
          },
          {
            path: 'measures/manage',
            element: <MeasureManage />,
          },
          {
            path: 'measures/create',
            element: <MeasureCreate />,
          },
          {
            path: 'metrics/overview',
            element: <MetricsOverview />,
          },
          {
            path: 'metrics/detail/:sceneId/:metricName',
            element: <MetricDetail />,
          },
          {
            path: 'metrics/manage',
            element: <MetricsManage />,
          },
          {
            path: 'metrics/create',
            element: <MetricsCreate />,
          },

          {
            path: 'metric-tree/overview',
            element: <MetricTreeOverview />,
          },
          {
            path: 'charts/list',
            element: <ChartManagePage />,
          },
          {
            path: 'charts/edit/:chartId',
            element: <ChartEdit />,
          },
          {
            path: 'charts/copy/:chartId',
            element: <ChartEdit />,
          },
          {
            path: 'charts/detail/:chartId',
            element: <ChartDetail />,
          },
          {
            path: 'metric-tree/detail/:projectId/:treeId',
            element: <MetricTreeDetail />,
          },
          {
            path: 'document/list',
            element: <DocumentView />,
          },
          {
            path: 'ask-history/overview',
            element: <AskHistoryOverView />,
          },
          {
            path: 'hint/overview',
            element: <HintOverview />,
          },
          {
            path: 'smart-report/template',
            element: <TemplateManagement />,
          },
          {
            path: 'smart-report/report',
            element: <ReportManagement />,
          },
          {
            path: 'smart-report/template/report-generation',
            element: <ReportGeneration />,
          },
          {
            path: 'smart-report/marking-task',
            element: <MarkingTaskManagement />,
          },
          {
            path: 'external-report/management',
            element: <ExternalReportManagement />,
          },
        ],
      },
      {
        path: '/dashboard',
        element: <AskBIDashboardLayout />,
        children: [
          {
            path: 'info',
            element: <DashboardInfo />,
          },
          {
            path: 'code-statistics',
            element: <CodeStatistics />,
          },
          {
            path: 'code-overview',
            element: <CodeOverview />,
          },
        ],
      },
      {
        path: '/login',
        element: <Login />,
      },
      {
        path: '/login-error',
        element: <LoginError />,
      },
      {
        path: '/xengine/ranger',
        element: <IFrame url={`/xengine/ranger`} />,
      },
      {
        path: '/xengine/open-meta-data',
        element: <IFrame url={`/`} />,
      },
      {
        path: '*',
        element: <Page404 />,
      },
    ]),
  )
}

export default function Root() {
  useAtomValue(loginAppIdAtom)
  const [env] = useAtom(envAtom)

  const router = getRouter({
    defaultRouterPath: env?.VITE_PRODUCTS?.trim() === 'X-Engine' ? XEngineHomePath : chatPath,
  })

  return (
    <TenantWrapper>
      <RouterProvider router={router} />
    </TenantWrapper>
  )
}
