import React, { useState, useEffect } from 'react'
import { PageHeader } from '@ant-design/pro-layout'
import { askBIApiUrls } from 'src/shared/url-map'
import { routerMap } from '@XEngineRouter/routerMap'
import request from 'src/shared/xengine-axios'
import { Button, Typography, Modal, Popover, Form, Input, Empty, Table, Card } from 'antd'
import type { TableProps } from 'antd'
import type { CommonListType, VirtualTableType } from '@shared/xengine-types'
import Columns from '@ui/table/Columns'
import { useBoolean, useRequest } from 'ahooks'
import CSVUpload from 'src/client/components/CSVUpload/CSVUpload'
import { MoreOutlined } from '@ant-design/icons'
import DBSelect from 'src/client/components/DBSelect/DBSelect'
import { isAlphaNumberAndAlphaStart } from '@shared/common-utils'
import { useNavigate } from 'react-router-dom'
import { App } from 'antd'
import CatalogDatabaseTree, { DatabaseItem, CatalogItem } from 'src/client/components/CatalogDatabaseTree'
import Search from 'antd/es/input/Search'
import { getWinInnerSize } from '@libs/util'
import { downloadFile } from 'src/shared/common-utils'

const columns = [
  {
    title: '表名称',
    fixed: 'left',
    width: '20%',
    ellipsis: true,
    dataIndex: 'name',
  },
  {
    title: '表目录',
    dataIndex: 'catalogName',
  },
  {
    title: '表库',
    dataIndex: 'databaseName',
  },
  {
    title: '创建时间',
    width: 180,
    dataIndex: ['modification', 'gmtCreated'],
    render: Columns({ type: 'time' }),
  },
  {
    title: '创建人',
    dataIndex: ['modification', 'creator'],
    width: 160,
    ellipsis: true,
  },
] as TableProps['columns']

const PAGESIZE = 10

export default function CSVFileDataSource() {
  const [deleteTableModalOpen, deleteTableModalOpenOps] = useBoolean(false)
  const [CSVUploadOpen, setCSVUploadOpen] = useState(false)
  const [createLikeTableModalOpen, createLikeTableModalOpenOps] = useBoolean(false)
  const [activeInfo, setActiveInfo] = useState({
    key: '',
    data: {} as VirtualTableType,
  })
  const [createLikeTableForm] = Form.useForm()
  const { message } = App.useApp()
  const navigate = useNavigate()
  const [dbDataMap, setDbDataMap] = useState<Record<string, DatabaseItem[]>>({})
  const [searchValue, setSearchValue] = useState('')
  const [searchTableValue, setSearchTableValue] = useState('')
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([])
  const [selectedKeys, setSelectedKeys] = useState<React.Key[]>([])
  const [xWidth, setXWidth] = useState('100%')
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: PAGESIZE,
    total: 0,
  })
  const [currentSelection, setCurrentSelection] = useState<{
    catalog: string
    database: string
  }>({
    catalog: '',
    database: '',
  })

  const { loading: deleteLoading, run: deleteTable } = useRequest(
    () =>
      request.delete(askBIApiUrls.xengine.ptable.delete, {
        params: {
          catalog: activeInfo.data.catalogName,
          database: activeInfo.data.databaseName,
          name: activeInfo.data.name,
        },
      }),
    {
      manual: true,
      onSuccess() {
        deleteTableModalOpenOps.setFalse()
        getTableList(currentSelection.catalog, currentSelection.database, 1, pagination.pageSize, searchTableValue)
        setPagination((res) => ({ ...res, current: 1 }))
      },
      onError(err) {
        message.error(err.message || '删除失败')
      },
    },
  )

  const { loading: createLikeTableLoading, run: createLikeTable } = useRequest(
    ({ catalog, database, table }: { catalog: string; database: string; table: string }) =>
      request.post(askBIApiUrls.xengine.VTable.createLikeTable, {
        targetCatalog: catalog,
        targetDatabase: database,
        targetTable: `vt_${table}`,
        sourceCatalog: activeInfo.data.catalogName,
        sourceDatabase: activeInfo.data.databaseName,
        sourceTable: activeInfo.data.name,
      }),
    {
      onSuccess(_, [params]) {
        message.success(
          <>
            创建贴源虚拟表
            <Typography.Link
              onClick={() =>
                navigate(
                  `${routerMap.dataModel.virtualTable.path}?catalog=${params.catalog}&database=${params.database}`,
                )
              }
            >{`vt_${params.table}`}</Typography.Link>
            成功
          </>,
        )
        createLikeTableModalOpenOps.setFalse()
      },
      onError(err) {
        message.error(err.message || '创建贴源虚拟表失败')
      },
      manual: true,
    },
  )

  const { data: catalogData } = useRequest(
    () =>
      request
        .get<{}, CatalogItem[]>(askBIApiUrls.xengine.catalogList, { params: { current: 1, pageSize: -1 } })
        .then((res) => res.filter((item) => item.type === 'INTERNAL')),
    {
      onSuccess(catalogData) {
        catalogData.forEach((item) => {
          getDatabaseRun(item.name)
        })
      },
    },
  )

  const getDatabaseRun = async (catalog: string) => {
    try {
      const data = await request.get<{ catalog: string }, DatabaseItem[]>(askBIApiUrls.xengine.databaseList, {
        params: { catalog },
      })

      setDbDataMap((prev) => ({
        ...prev,
        [catalog]: data,
      }))
    } catch (e: any) {
      message.error(e?.message || '获取数据库失败')
    }
  }

  const {
    run: getTableList,
    loading: tableListLoading,
    data: tableListData,
  } = useRequest(
    (catalog: string, database: string, current: number, pageSize: number, table?: string) => {
      const requestCatalog = catalog || catalogData?.[0]?.name || ''
      const requestDatabase = dbDataMap[requestCatalog].find((d) => d.name === database)
        ? database
        : dbDataMap[requestCatalog]?.[0]?.name || ''
      const currentSelectedKeys = [[requestCatalog, requestDatabase].filter(Boolean).join('.')]
      if (currentSelectedKeys?.[0] !== selectedKeys?.[0]) {
        if (requestCatalog && expandedKeys.indexOf(requestCatalog) === -1) {
          setExpandedKeys((keys) => keys.concat([requestCatalog]))
        }
        setSelectedKeys(currentSelectedKeys)
      }

      return request.get<unknown, CommonListType<VirtualTableType>>(askBIApiUrls.xengine.ptable.list, {
        params: {
          current,
          pageSize,
          catalog: requestCatalog,
          database: requestDatabase,
          table,
        },
      })
    },
    {
      ready: Boolean(catalogData?.[0]?.name && dbDataMap[catalogData?.[0]?.name || '']?.length),
      refreshDeps: [catalogData?.[0]?.name, dbDataMap[catalogData?.[0]?.name || '']],
    },
  )

  const { run: exportTableSchema, loading: exportTableSchemaLoading } = useRequest(
    (params: { catalog: string; database: string; table: string }) => {
      return downloadFile(
        `${askBIApiUrls.xengine.ptable.exportTableSchema}?${new URLSearchParams(params).toString()}`,
        undefined,
        {
          onSuccess() {
            message.success('下载成功')
          },
          onError(err) {
            message.error(err?.message || '下载失败')
          },
          responseType: 'arraybuffer',
        },
      )
    },
    {
      manual: true,
    },
  )

  const { run: exportTableData, loading: exportTableDataLoading } = useRequest(
    (params: { catalog: string; database: string; table: string }) => {
      return downloadFile(
        `${askBIApiUrls.xengine.ptable.exportData}?${new URLSearchParams(params).toString()}`,
        undefined,
        {
          onSuccess() {
            message.success('下载成功')
          },
          onError(err) {
            message.error(err?.message || '下载失败')
          },
          responseType: 'arraybuffer',
        },
      )
    },
    {
      manual: true,
    },
  )

  const handleTableChange = (newPagination: any) => {
    setPagination((prev) => ({
      ...prev,
      current: newPagination.current,
      pageSize: newPagination.pageSize,
    }))
    getTableList(
      currentSelection.catalog,
      currentSelection.database,
      newPagination.current,
      newPagination.pageSize,
      searchTableValue,
    )
  }

  // 添加resize，调整Table的大小
  useEffect(() => {
    const setTableWidth = (width: number) => {
      if (width < 1330) {
        setXWidth('110%')
      } else {
        setXWidth('100%')
      }
    }
    setTableWidth(getWinInnerSize().innerWidth)

    // Window resize set table scroll x value
    window.addEventListener('resize', (e: any) => {
      setTableWidth(e?.target?.innerWidth)
    })
  }, [location.href])

  const operationColumnMenuItems = [
    {
      key: 'create',
      label: '创建贴源虚拟表',
      onClick: createLikeTableModalOpenOps.setTrue,
    },
    {
      key: 'export-schema',
      label: '导出字段元数据',
      onClick(record: VirtualTableType) {
        exportTableSchema({
          catalog: record.catalogName,
          database: record.databaseName,
          table: record.name,
        })
      },
    },
    {
      key: 'export-data',
      label: ' 导出CSV文件数据',
      onClick(record: VirtualTableType) {
        exportTableData({
          catalog: record.catalogName,
          database: record.databaseName,
          table: record.name,
        })
      },
    },
    {
      key: 'delete',
      danger: true,
      label: '删除',
      onClick: deleteTableModalOpenOps.setTrue,
    },
  ]

  // 获取操作按钮loading
  const getOperationBtnLoading = (key: string) => {
    const isMatch = activeInfo.key === key
    if (!isMatch) return false
    if (key === 'export-schema') {
      return exportTableSchemaLoading
    }
    if (key === 'export-data') {
      return exportTableDataLoading
    }
    return false
  }

  return (
    <div className="flex h-full w-full gap-4">
      <div className="mx-3 w-60 shrink-0">
        <CatalogDatabaseTree
          catalogData={catalogData}
          dbDataMap={dbDataMap}
          searchValue={searchValue}
          onSearchChange={setSearchValue}
          expandedKeys={expandedKeys}
          onExpandedKeysChange={setExpandedKeys}
          selectedKeys={selectedKeys}
          onSelectedKeysChange={setSelectedKeys}
          onSelectionChange={({ catalog, database }) => {
            setCurrentSelection({ catalog, database })
            setPagination((prev) => ({
              ...prev,
              current: 1,
            }))
            getTableList(catalog, database, 1, pagination.pageSize, searchTableValue)
          }}
          searchPlaceholder="搜索表目录、表库"
        />
      </div>
      <div className="catalog-database-tree-sibling-wrap flex w-full flex-col">
        <PageHeader
          className={`mb-4 p-0`}
          title={
            <Search
              className="font-normal"
              placeholder="搜索表名称"
              allowClear
              onSearch={(e) => {
                setSearchTableValue(e)
                setPagination((prev) => ({
                  ...prev,
                  current: 1,
                }))
                getTableList(currentSelection.catalog, currentSelection.database, 1, PAGESIZE, e)
              }}
            />
          }
          extra={
            <Button type="primary" onClick={() => setCSVUploadOpen(true)}>
              上传CSV文件
            </Button>
          }
        />
        <Card>
          <Table
            columns={columns?.concat([
              {
                title: '操作',
                fixed: 'right',
                width: 80,
                render: (record: VirtualTableType) => (
                  <Popover
                    content={
                      <div className="w-[120px]">
                        {operationColumnMenuItems.map((i) => (
                          <Button
                            key={i.key}
                            block
                            type="text"
                            danger={i.danger}
                            onClick={() => {
                              setActiveInfo({
                                key: i.key,
                                data: record,
                              })
                              i.onClick?.(record)
                            }}
                            loading={getOperationBtnLoading(i.key)}
                          >
                            {i.label}
                          </Button>
                        ))}
                      </div>
                    }
                  >
                    <MoreOutlined className="cursor-pointer" />
                  </Popover>
                ),
              },
            ])}
            rowKey="name"
            scroll={{ x: xWidth }}
            dataSource={tableListData?.list}
            loading={tableListLoading}
            pagination={{
              ...pagination,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 条`,
            }}
            locale={{
              emptyText: <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description="该数据库下未获取到数据" />,
            }}
            onChange={handleTableChange}
          />
        </Card>
      </div>
      <Modal
        title={'删除文件表'}
        open={deleteTableModalOpen}
        onOk={deleteTable}
        onCancel={deleteTableModalOpenOps.setFalse}
        okText={'确认删除'}
        okButtonProps={{
          loading: deleteLoading,
        }}
      >
        <p>
          确认删除
          <Typography.Text type="danger">
            {[activeInfo?.data?.catalogName, activeInfo?.data?.databaseName, activeInfo?.data?.name]
              .filter(Boolean)
              .join('.')}
          </Typography.Text>
          文件表吗？
        </p>
      </Modal>
      <CSVUpload
        open={CSVUploadOpen}
        setOpen={setCSVUploadOpen}
        onUploadSuccess={({ catalog, database }) => {
          getTableList(catalog, database, 1, pagination.pageSize, searchTableValue)
          setPagination((res) => ({ ...res, current: 1 }))
        }}
      />
      <Modal
        open={createLikeTableModalOpen}
        title="选择贴源虚拟表配置"
        okText="创建贴源虚拟表"
        width={640}
        onCancel={() => {
          createLikeTableForm.resetFields()
          createLikeTableModalOpenOps.setFalse()
        }}
        onOk={async () => createLikeTable(await createLikeTableForm.validateFields())}
        okButtonProps={{
          loading: createLikeTableLoading,
        }}
      >
        <Form form={createLikeTableForm} labelCol={{ span: 4 }}>
          <DBSelect
            span={24}
            required={{ catalog: true, database: true }}
            setFieldValue={createLikeTableForm.setFieldValue}
          />
          <Form.Item label="虚拟表名称" required>
            <div className="flex items-center">
              <span className="mb-6 mr-1">vt_</span>
              <Form.Item
                className="flex-1"
                name="table"
                validateFirst
                rules={[
                  { required: true, message: '请输入虚拟表名称' },
                  {
                    validator(_, value) {
                      return isAlphaNumberAndAlphaStart(value)
                        ? Promise.resolve('success')
                        : Promise.reject('需以数字字母下划线组成，并以字母开头')
                    },
                  },
                ]}
              >
                <Input placeholder={'请输入虚拟表的名称'} />
              </Form.Item>
            </div>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}
