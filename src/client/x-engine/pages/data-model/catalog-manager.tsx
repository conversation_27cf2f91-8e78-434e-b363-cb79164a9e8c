// @ts-nocheck
import React, { useState, useRef } from 'react'
import { ProTable } from '@ant-design/pro-components'
import { type ProColumns } from '@ant-design/pro-components'
import { PageHeader } from '@ant-design/pro-layout'
import { Button, Modal, Form, Input, message, Space, Typography } from 'antd'
import { Api } from '@api'
import { useRequest } from 'ahooks'
import { ActionType } from '../../static'

interface CatalogColumnItemType {
  catalog: string
  database: string
  catalogRowSpan: number
}

type FormatDataType = { catalog: string; database: string; catalogRowSpan?: number; id: number }

const generateCatalogTableColumns = ({
  onClickAddDatabaseText,
  onClickDeleteCatalogText,
  onClickDeleteDatabaseText,
}: {
  onClickAddDatabaseText?: (record: CatalogColumnItemType) => any
  onClickDeleteCatalogText: (arg: { catalog: string }) => any
  onClickDeleteDatabaseText: (arg: { catalog: string; database: string }) => any
}) =>
  [
    {
      title: '虚拟表目录',
      dataIndex: 'catalog',
      fieldProps: {
        placeholder: '请输入虚拟表目录名称',
      },
      formItemProps: {
        labelCol: { flex: '82px' },
      },
      onCell: (record) => {
        return {
          rowSpan: record.catalogRowSpan ? record.catalogRowSpan : 0,
          colSpan: 1,
        }
      },
    },
    {
      title: '虚拟表数据库',
      dataIndex: 'database',
      fieldProps: {
        placeholder: '请输入虚拟表数据库名称',
      },
      formItemProps: {
        labelCol: { flex: '100px' },
      },
      colSpan: 1,
      render: (database, record) => {
        if (database === '+++') {
          return (
            <Typography.Link
              onClick={() => {
                if (typeof onClickAddDatabaseText === 'function') {
                  onClickAddDatabaseText(record)
                }
              }}
            >
              +新增数据库
            </Typography.Link>
          )
        }
        return database
      },
      onCell: (record) => {
        return record.database === '+++' ? { colSpan: 2 } : { colSpan: 1 }
      },
    },
    {
      title: '数据库操作',
      dataIndex: 'database_operation',
      search: false,
      onCell: (record) => {
        return record.database === '+++' ? { colSpan: 0 } : { colSpan: 1 }
      },
      render: (_, record) => {
        return (
          <Space>
            <Typography.Text
              type="danger"
              style={{
                cursor: 'pointer',
              }}
              onClick={() => {
                if (typeof onClickDeleteDatabaseText === 'function') {
                  onClickDeleteDatabaseText({
                    catalog: record.catalog,
                    database: record.database,
                  })
                }
              }}
            >
              删除
            </Typography.Text>
          </Space>
        )
      },
    },
    {
      title: '数据目录操作',
      dataIndex: 'catalog_operation',
      search: false,
      render: (_, record) => {
        return (
          <>
            <Space>
              <Typography.Text
                type="danger"
                style={{
                  cursor: record.catalog !== 'dipeak' ? 'pointer' : '',
                }}
                disabled={record.catalog === 'dipeak'}
                onClick={() => {
                  if (typeof onClickDeleteCatalogText === 'function' && record.catalog !== 'dipeak') {
                    onClickDeleteCatalogText({
                      catalog: record.catalog,
                    })
                  }
                }}
              >
                删除
              </Typography.Text>
            </Space>
          </>
        )
      },
      onCell: (record) => {
        return {
          rowSpan: record.catalogRowSpan ? record.catalogRowSpan : 0,
          colSpan: 1,
        }
      },
    },
  ] as ProColumns<CatalogColumnItemType>[]

function formatDataToTable(data: { catalog: string; database: string }[]) {
  if (!Array.isArray(data)) {
    return []
  }
  data.sort((a, b) => a?.catalog.localeCompare(b?.catalog, 'en', { sensitivity: 'base' }))
  const formatData: FormatDataType[] = []
  let idx = 0
  let curItem: FormatDataType = null
  for (let i = 0; i <= data.length; ++i) {
    const { catalog, database } = data?.[i] || {}
    const { catalog: preCatalog } = data?.[i - 1] || {}
    if (preCatalog && i >= 1 && preCatalog !== catalog) {
      const isSingleItem = formatData?.[formatData.length - 1]?.catalog !== preCatalog
      formatData.push({
        catalog: preCatalog,
        database: '+++',
        catalogRowSpan: isSingleItem ? 1 : 0,
        id: idx++,
      })
    }
    if (catalog && database) {
      const addItem = { ...data[i] } as FormatDataType
      if (i === 0 || catalog !== preCatalog) {
        addItem.catalogRowSpan = 1
        curItem = addItem
      }
      addItem.id = idx++
      curItem.catalogRowSpan++
      formatData.push(addItem)
    }
  }
  return formatData
}

export default function CatalogManager() {
  const [messageApi, contextHolder] = message.useMessage()

  const [isCreateCatalogModalOpen, setIsCreateCatalogModalOpen] = useState(false)
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false)
  const [curDeleteInfo, setCurDeleteInfo] = useState<{ catalog: string; database?: string }>(null)
  const [deleteType, setDeleteType] = useState<'catalog' | 'database'>()
  const [createType, setCreateType] = useState<'catalog' | 'database'>()
  const [createCatalogForm] = Form.useForm()
  const catalogTableAction = useRef<ActionType>()
  const { run: createDatabase, loading: createDatabaseLoading } = useRequest(
    Api.apiEngineV1DatabaseCreateVtableDatabasePost,
    {
      manual: true,
      onSuccess() {
        messageApi.success('创建成功')
        handleCloseCreateCatalogModal()
        catalogTableAction.current.reload()
      },
    },
  )

  const { run: createCatalog, loading: createCatalogLoading } = useRequest(Api.apiEngineV1CatalogVtcatalogCreatePost, {
    manual: true,
    onSuccess() {
      messageApi.success('创建成功')
      handleCloseCreateCatalogModal()
      catalogTableAction.current.reload()
    },
  })

  const { run: deleteCatalog, loading: deleteCatalogLoading } = useRequest(Api.apiEngineV1CatalogVtcatalogDropDelete, {
    manual: true,
    onSuccess() {
      messageApi.success('删除成功')
      setCurDeleteInfo(null)
      setIsDeleteModalOpen(false)
      catalogTableAction.current.reload()
    },
  })

  const { run: deleteDatabase, loading: deleteDatabaseLoading } = useRequest(
    Api.apiEngineV1DatabaseDropVtableDatabaseDelete,
    {
      manual: true,
      onSuccess() {
        messageApi.success('删除成功')
        setCurDeleteInfo(null)
        setIsDeleteModalOpen(false)
        catalogTableAction.current.reload()
      },
    },
  )

  function handleCloseCreateCatalogModal() {
    setIsCreateCatalogModalOpen(false)
    createCatalogForm.resetFields()
  }

  return (
    <>
      {contextHolder}
      <PageHeader
        title="虚拟表目录管理"
        onBack={() => window.history.back()}
        extra={
          <Button
            type="primary"
            onClick={() => {
              setCreateType('catalog')
              setIsCreateCatalogModalOpen(true)
            }}
          >
            新增数据目录
          </Button>
        }
      />

      <ProTable
        pagination={false}
        rowKey="id"
        columns={generateCatalogTableColumns({
          onClickAddDatabaseText: (record) => {
            setIsCreateCatalogModalOpen(true)
            setCreateType('database')
            createCatalogForm.setFieldValue('catalog', record.catalog)
          },
          onClickDeleteCatalogText: (info) => {
            setCurDeleteInfo(info)
            setIsDeleteModalOpen(true)
            setDeleteType('catalog')
          },
          onClickDeleteDatabaseText: (info) => {
            setCurDeleteInfo(info)
            setIsDeleteModalOpen(true)
            setDeleteType('database')
          },
        })}
        request={async (params: { catalog?: string; database?: string }) => {
          const catalogData = await Api.apiEngineV1CatalogVtcatalogListGet(params).then((res) => res)
          const formatCatalogData = formatDataToTable(catalogData)
          return Promise.resolve({
            data: formatCatalogData,
            total: formatCatalogData.length,
          })
        }}
        options={false}
        actionRef={catalogTableAction}
      />

      {/* 创建数据目录 */}
      <Modal
        width={540}
        title={createType === 'database' ? '创建虚拟表数据库' : '创建虚拟表目录'}
        open={isCreateCatalogModalOpen}
        onCancel={() => {
          handleCloseCreateCatalogModal()
        }}
        okButtonProps={{
          loading: createCatalogLoading || createDatabaseLoading,
          async onClick() {
            await createCatalogForm.validateFields()
            const values = createCatalogForm.getFieldsValue()
            switch (createType) {
              case 'catalog': {
                createCatalog(values)
                break
              }
              case 'database': {
                createDatabase(values)
                break
              }
            }
          },
        }}
      >
        <Form
          form={createCatalogForm}
          labelCol={{
            flex: '120px',
          }}
        >
          <Form.Item
            name="catalog"
            label="数据目录名称"
            validateFirst
            rules={[
              { required: true },
              {
                async validator(_, value) {
                  const testReg = /[^a-zA-Z_0-9]/
                  if (testReg.test(value)) {
                    return Promise.reject('请输入符合格式的数据目录名称')
                  }
                },
              },
            ]}
          >
            <Input placeholder="支持英文、数字、下划线" readOnly={createType === 'database'} />
          </Form.Item>

          <Form.Item
            name="databases"
            label="数据库名称"
            validateFirst
            validateDebounce={400}
            rules={[
              { required: true },
              {
                async validator(_, value) {
                  const testReg = /[^a-zA-Z,_0-9]/
                  if (testReg.test(value)) {
                    return Promise.reject('请输入符合格式的数据库名称')
                  }
                },
              },
              {
                async validator(_, value) {
                  const dbs = (value || '').split(',')
                  const dbSet = [...new Set(dbs)]
                  if (dbs.length > dbSet.length) {
                    return Promise.reject('有重复的数据库名称输入')
                  }
                },
              },
            ]}
          >
            <Input placeholder="支持英文、数字、下划线，可填写多个，用“,”分隔" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 删除目录 */}
      <Modal
        title={`确认删除数据${deleteType === 'catalog' ? '目录' : '库'}${
          (deleteType === 'catalog' ? curDeleteInfo?.catalog : curDeleteInfo?.database) || ''
        }`}
        open={isDeleteModalOpen}
        onCancel={() => {
          setCurDeleteInfo(null)
          setIsDeleteModalOpen(false)
        }}
        okButtonProps={{
          loading: deleteCatalogLoading || deleteDatabaseLoading,
          onClick() {
            if (curDeleteInfo) {
              switch (deleteType) {
                case 'catalog': {
                  if (curDeleteInfo) {
                    const { catalog } = curDeleteInfo
                    deleteCatalog({
                      catalog,
                    })
                  }
                  break
                }
                case 'database': {
                  if (curDeleteInfo) {
                    const { catalog, database } = curDeleteInfo
                    deleteDatabase({ catalog, database })
                  }
                  break
                }
              }
            }
          },
        }}
      >
        <p>
          {`删除当前数据${deleteType === 'catalog' ? '目录' : '库'}会同步删除数据${
            deleteType === 'catalog' ? '目录' : '库'
          }内虚拟表，是否确定删除？`}
        </p>
      </Modal>
    </>
  )
}
