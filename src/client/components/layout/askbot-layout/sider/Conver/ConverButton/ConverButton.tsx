import { AddNewIcon, SvgIcon } from 'src/client/components/SvgIcon'
import { AskBotLeftSiderCollapsedAtom } from '../../sider'

export function ConverButton() {
  const [collapsed] = useAtom(AskBotLeftSiderCollapsedAtom)

  return (
    <Button
      type="primary"
      icon={<SvgIcon icon={AddNewIcon} className={''} />}
      className={clsx('h-[36px]', collapsed ? 'w-[86px]' : 'w-[146px]')}
    >
      新会话&nbsp;
    </Button>
  )
}
