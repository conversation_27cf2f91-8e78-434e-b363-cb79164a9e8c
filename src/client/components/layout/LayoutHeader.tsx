/**
 * Layout 的顶部导航条 Header
 */
import React, { useCallback, useEffect, useState } from 'react'
import clsx from 'clsx'
import { Dropdown, MenuProps, message, Pagination } from 'antd'
import { useLocation, useNavigate } from 'react-router-dom'
import { useAtom, useAtomValue, useSetAtom } from 'jotai'
import { RESET } from 'jotai/utils'
import axios from 'axios'
import { XMarkIcon } from '@heroicons/react/24/outline'
import { useRequest, useTitle } from 'ahooks'
import {
  BASE_URL,
  chatPath,
  CHROME_ACTIONS,
  DEFAULT_HTML_TITLE,
  IS_CHROME_EXTENSION,
  IS_H5,
  XENGINE_HTML_TITLE,
  XEngineHomePath,
} from '@shared/constants'
import { ASK_BI_BASE, askBIApiUrls, askBIPageUrls } from 'src/shared/url-map'
import { TenantListResponse } from 'src/shared/metric-types'
import { APIResponse } from 'src/shared/common-types'
import {
  lastTenantSelectionAtom,
  brandInfoAtom,
  currentLoginUserAtom,
  envAtom,
  isShowMatchedParamDrawerAtom,
  loginAppIdAtom,
  queryStateAtom,
  isOpenTenantAtom,
  metricConfigAtom,
} from 'src/client/pages/AskBI/askBIAtoms'
import fileIcon from '/img/file.svg'
import { useAutoAuth, authAtom } from 'src/client/hooks/useAuth'
import { isBaoWu } from 'src/shared/baowu-share-utils'
import { appearanceAtom } from 'src/client/pages/System/Appearance/appearanceSettingAtom'
import { LogoType } from 'src/client/pages/System/Appearance/constant'
import { beforeLogout } from 'src/shared/auth'
import ConverWidget from '../ConverWidget'
import { SvgIcon, xengineIcon, dipeakMobileIcon } from '../SvgIcon'
import { useMetricsSidebarMenuItems } from './AskBIMetricsLayout'

interface Tenant {
  tenantName: string
  state: 'NORMAL' | 'DISCARDED'
  projects?: Project[]
  catalogName: string
}

interface Project {
  projectName: string
  comment: string
}

interface CurrentSelectionTenantProject {
  tenant: string
  project: string
}

export default function LayoutHeader({
  showMenu = true,
  navPadding = true,
}: {
  isFullWidth?: boolean
  showMenu?: boolean
  navPadding?: boolean
}) {
  useAutoAuth()
  const [currentLoginUserInfo, setCurrentLoginUserInfo] = useAtom(currentLoginUserAtom)
  const loginAppId = useAtomValue(loginAppIdAtom)
  const brandInfo = useAtomValue(brandInfoAtom)
  const isShowMatchedParamDrawer = useAtomValue(isShowMatchedParamDrawerAtom)
  const location = useLocation()
  const navigate = useNavigate()
  const queryState = useAtomValue(queryStateAtom)
  const [env] = useAtom(envAtom)
  const setLastTenantSelection = useSetAtom(lastTenantSelectionAtom)
  // const accessPaths = useAtomValue(accessPathsAtom)
  const [isShowDashboard, setIsShowDashboard] = useState<boolean>(false)
  const metricConfig = useAtomValue(metricConfigAtom)
  // 宝武默认不展示askbot logo
  const [showBrandLogo, _setShowBrandLogo] = useState<boolean>(!isBaoWu(metricConfig?.metricTableName))
  const [currentSelection, setCurrentSelection] = useState<CurrentSelectionTenantProject>()
  const [total, setTotal] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const isOpenTenant = useAtomValue(isOpenTenantAtom)
  const pageSize = 10

  const auth = useAtomValue(authAtom)
  const MetricsSidebarMenuItems = useMetricsSidebarMenuItems()

  const appearanceConfig = useAtomValue(appearanceAtom)
  useTitle(env?.VITE_HTML_TITLE || env?.VITE_PRODUCTS?.trim() === 'X-Engine' ? XENGINE_HTML_TITLE : DEFAULT_HTML_TITLE)

  useEffect(() => {
    setIsShowDashboard(!!auth.dashBoard)
  }, [auth.dashBoard])

  // 获取用户的信息
  useRequest(
    async () => {
      const data = await axios.get(askBIApiUrls.auth.userInfo)
      setCurrentLoginUserInfo(data.data.data)
    },
    {
      onError: (error: any) => {
        console.error('get currentUserBasicInfo =', error)
      },
    },
  )

  const { run: fetchUserTenants, data: tenantsData } = useRequest(
    async (page = 1) => {
      const response =
        isOpenTenant &&
        (await axios.get<APIResponse<TenantListResponse>>(askBIApiUrls.xengine.tenant.list, {
          params: {
            current: page,
            pageSize,
          },
        }))
      if (!response) {
        return
      }

      setTotal(response.data.data?.total ?? 0)
      return response.data.data?.list ?? []
    },
    {
      manual: true,
      onSuccess: async (data) => {
        // 如果有上次选择的租户，使用上次的选择
        const lastSelection = localStorage.getItem('lastTenantSelection')

        // 判断这个租户是否在 tenantsData 中
        const parsedSelection = lastSelection ? JSON.parse(lastSelection) : null
        const tenant = data?.find((tenant: Tenant) => tenant.tenantName === parsedSelection?.tenant)

        if (lastSelection && tenant) {
          // 判断这个租户下面的项目是否被删除,如果项目被删除，则清除 localStorage 和 cookies
          const res = await axios
            .get(askBIApiUrls.xengine.tenant.getProjects, {
              params: { tenantName: JSON.parse(lastSelection)?.tenant },
            })
            .then((res) => {
              return res.data.data
            })

          const projects = (res as any)?.projects
          if (
            (res as any)?.state === 'DISCARDED' ||
            !projects.some(
              (project: any) => project.projectName === JSON.parse(lastSelection)?.project || projects === 'undefined',
            )
          ) {
            setLastTenantSelection(RESET)
            document.cookie = 'TENANT_SESSION_ID=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax'
            document.cookie = 'PROJECT_SESSION_ID=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax'
            window.location.reload()
          }
          setCurrentSelection(JSON.parse(lastSelection))
        }
      },
    },
  )
  useEffect(() => {
    fetchUserTenants()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    fetchUserTenants(page)
  }

  // 切换租户或项目时的处理
  const handleSelectionChange = async (tenant: string, project: string, catalogName: string) => {
    try {
      const selection = { tenant, project, catalogName }
      setCurrentSelection(selection)
      setLastTenantSelection(selection)

      // 存入 cookie 中
      document.cookie = `TENANT_SESSION_ID=${selection.tenant}; path=/; SameSite=Lax`
      document.cookie = `PROJECT_SESSION_ID=${selection.project}; path=/; SameSite=Lax`

      // 可能需要刷新页面或更新其他状态
      window.location.reload()
    } catch (error) {
      console.error('Failed to change tenant/project:', error)
    }
  }

  // 渲染租户选择器
  const renderTenantSelector = () => {
    // 如果数据还未加载完成，显示加载状态或返回 null
    if (!tenantsData?.length) {
      return null
    }

    const items = tenantsData
      ?.filter((tenant: Tenant) => tenant.state === 'NORMAL')
      ?.sort((a, b) => a.tenantName.localeCompare(b.tenantName))
      ?.map((tenant: Tenant) => ({
        key: tenant.tenantName,
        label: (
          <div className="px-2 py-1">
            <div className="flex items-center gap-2">
              <img src={fileIcon} alt="file" className="h-4 w-4" />
              <div className="font-bold">{tenant.tenantName}</div>
            </div>
            <div className="mt-2 max-h-[200px] space-y-1 overflow-y-auto [&::-webkit-scrollbar-thumb]:rounded [&::-webkit-scrollbar-thumb]:bg-gray-300 [&::-webkit-scrollbar]:hidden [&::-webkit-scrollbar]:w-1.5 hover:[&::-webkit-scrollbar]:block">
              {tenant?.projects
                ?.sort((a, b) => a.projectName.localeCompare(b.projectName))
                ?.map((project: Project) => (
                  <div
                    key={project.projectName}
                    className="cursor-pointer rounded-sm px-2 py-1 hover:bg-slate-200"
                    onClick={() => handleSelectionChange(tenant.tenantName, project.projectName, tenant.catalogName)}
                  >
                    {project.projectName}
                  </div>
                ))}
            </div>
          </div>
        ),
      }))

    return (
      <Dropdown
        menu={{ items }}
        trigger={['click']}
        placement="bottomLeft"
        dropdownRender={(menu) => (
          <div className="max-w-[200px] rounded-md bg-white shadow-lg">
            <div className="max-h-[300px] overflow-y-auto [&::-webkit-scrollbar-thumb]:rounded [&::-webkit-scrollbar-thumb]:bg-gray-300 [&::-webkit-scrollbar]:hidden [&::-webkit-scrollbar]:w-1.5 hover:[&::-webkit-scrollbar]:block">
              {menu}
            </div>
            {total > pageSize && (
              <div className="border-t border-gray-200 p-2">
                <Pagination
                  current={currentPage}
                  total={total}
                  pageSize={pageSize}
                  onChange={handlePageChange}
                  size="small"
                />
              </div>
            )}
          </div>
        )}
      >
        <div className="cursor-pointer items-center px-4 text-white">
          {currentSelection?.tenant ? (
            <>
              <span>{currentSelection?.tenant}</span>
              <span className="mx-1">/</span>
              <span>{currentSelection?.project}</span>
            </>
          ) : (
            '请选择租户'
          )}
        </div>
      </Dropdown>
    )
  }

  // 退出登录接口
  const handleLogout = async () => {
    try {
      await beforeLogout()
      window.location.href = loginAppId ? `${askBIPageUrls.login}?appid=${loginAppId}` : askBIPageUrls.login
    } catch (error: any) {
      console.error(error)
      message.error(`退出登录失败：${error?.message}`)
    }
  }

  const nickname = currentLoginUserInfo?.nickname || currentLoginUserInfo?.username
  const userDropdownItems: MenuProps['items'] = [
    {
      key: 'nickname',
      label: (
        <div className="flex max-w-[160px] items-center py-1 font-bold dark:bg-slate-600/30" title={nickname}>
          <span className="truncate">{nickname}</span>
        </div>
      ),
    },
    {
      key: 'logout',
      label: (
        <div
          className="flex cursor-pointer items-center px-2 py-1 hover:bg-slate-50 dark:bg-slate-600/30"
          onClick={() => {
            handleLogout()
          }}
        >
          退出登录
        </div>
      ),
    },
  ]

  // chrome 插件中，点击关闭按钮，发送消息给 background，由 background 关闭插件
  const handleCloseClick = useCallback(() => {
    if (IS_CHROME_EXTENSION) {
      const parentWindow = window.parent as Window
      parentWindow.postMessage({ action: CHROME_ACTIONS.closeDisplay }, '*')
    }
  }, [])

  const navList =
    env?.VITE_PRODUCTS?.trim() === 'X-Engine'
      ? []
      : [
          {
            navTitle: '首页',
            parentPath: '/chat/',
            navPath: queryState.enableOnlyChat ? askBIPageUrls.chatNew : askBIPageUrls.home,
            show: true,
          },
          {
            navTitle: '实体',
            parentPath: '/metric-store/',
            navPath: MetricsSidebarMenuItems[0]?.path ?? '/metric-store/',
            show:
              auth.metricStore ||
              auth.metricStoreMetrics ||
              auth.metricStoreDimensions ||
              auth.metricStoreMetricTree ||
              auth.metricStoreCharts ||
              auth.metricStoreDocument ||
              auth.metricStoreAskHistory ||
              auth.metricStoreHint ||
              auth.metricStoreExternalReport ||
              auth.metricStoreSmartReport,
          },
          {
            navTitle: '管理',
            parentPath: '/manage/',
            navPath: askBIPageUrls.manage.manageProject.list,
            show:
              auth.manageProjectList ||
              auth.manageScenario ||
              auth.manageDataScene ||
              auth.manageExternalDatasource ||
              auth.manageFileDatasource ||
              auth.manageDataModel ||
              auth.manageMetricModel ||
              auth.manageErManagement ||
              auth.manageMaterialization ||
              auth.manageSqlQuery ||
              auth.manageOperation ||
              auth.manageTenant ||
              auth.manageAdminResource ||
              auth.manageAdminRole ||
              auth.manageAdminUser ||
              auth.manageAdminUser ||
              auth.manageAdminRole ||
              auth.manageAdminResource,
          },
          {
            navTitle: '看板',
            parentPath: '/dashboard/',
            navPath: askBIPageUrls.dashboard.info,
            show: isShowDashboard && window.location.hostname === 'p.dipeak.com',
          },
        ]
  const renderNavAppChange = () => {
    return (
      <div className="app-nav flex gap-x-1.5 md:gap-x-4">
        {navList
          .filter((item) => item.show) // 过滤掉不需要显示的项
          .map((item, index) => (
            <div
              key={index}
              className={`nav-item relative flex h-[44px] w-[92px] cursor-pointer items-center justify-center leading-6 ${
                location.pathname.startsWith(BASE_URL + item.parentPath)
                  ? 'rounded border border-solid border-[#3D3D3D] bg-[#191919]'
                  : ''
              }`}
              onClick={(e) => {
                e.preventDefault()
                navigate(item.navPath)
              }}
            >
              <div className="text-white">{item.navTitle}</div>
            </div>
          ))}
      </div>
    )
  }

  if (brandInfo?.header === false) {
    return null
  }

  return (
    <header
      className={clsx('sticky top-0 transition-all duration-200 ease-in-out', {
        'md:block': location.pathname !== askBIPageUrls.home,
      })}
    >
      <div className="absolute -z-10 h-full w-full backdrop-blur-sm backdrop:saturate-200" />
      <nav
        className={clsx('flex items-center justify-between bg-white md:bg-[#252525]', {
          'px-5': navPadding,
          'px-0': !navPadding,
          'h-[62px]': !isShowMatchedParamDrawer,
          'h-[48px]': isShowMatchedParamDrawer,
        })}
        aria-label="Nav"
      >
        <div className="absolute left-5 hidden items-center md:flex">
          {brandInfo && (
            <div className="flex items-center">
              <div
                className="cursor-pointer"
                onClick={() => {
                  navigate(`${ASK_BI_BASE}${env?.VITE_PRODUCTS?.trim() === 'X-Engine' ? XEngineHomePath : chatPath}`)
                }}
              >
                {env?.VITE_HTML_TITLE ? (
                  <p className="text-2xl font-bold text-white">{env.VITE_HTML_TITLE}</p>
                ) : env?.VITE_PRODUCTS?.trim() === 'X-Engine' ? (
                  <SvgIcon icon={xengineIcon} className="h-[24px] w-[101px] text-white" />
                ) : (
                  <img
                    className={clsx('h-7 w-auto cursor-pointer', {
                      'h-7': brandInfo && brandInfo.brandName === 'China Telecom',
                      'h-9': brandInfo && brandInfo.brandName === 'yuanjing',
                      invisible: typeof brandInfo?.header === 'object' && brandInfo?.header?.logo === false,
                    })}
                    src={
                      appearanceConfig.logo && appearanceConfig.logoType !== LogoType.MULTI
                        ? appearanceConfig.logo
                        : brandInfo.logo
                    }
                    alt=""
                  />
                )}
              </div>
              {appearanceConfig.logo && appearanceConfig.logoType === LogoType.MULTI ? (
                <img className="ml-4 h-7" src={appearanceConfig.logo} alt="" />
              ) : null}

              {/* {brandInfo && brandInfo.jointLogo && (
                <img
                  className={clsx('ml-2 h-4 w-auto cursor-pointer', {
                    'h-[34px]': brandInfo && brandInfo.brandName === 'yangtze-computing',
                  })}
                  src={brandInfo.jointLogo}
                  alt=""
                />
              )} */}
            </div>
          )}
          {isOpenTenant && renderTenantSelector()}
        </div>

        {/* h5下显示 */}
        {showBrandLogo ? (
          <div className="flex h-[22px] items-center gap-2 md:hidden">
            {appearanceConfig.logo ? (
              <img src={appearanceConfig.logo} alt="" />
            ) : (
              <SvgIcon icon={dipeakMobileIcon} className="w-20" />
            )}
          </div>
        ) : null}

        <div />
        {/* 会话 */}
        {IS_H5 && <ConverWidget />}

        {!IS_H5 &&
          showMenu &&
          !(typeof brandInfo?.header === 'object' && brandInfo?.header?.tab === false) &&
          renderNavAppChange()}
        {!IS_H5 && (
          <div className="flex justify-end gap-4">
            {[
              // <div>
              //   {typeof brandInfo?.header === 'object' && brandInfo?.header?.themeToggle !== false && <ThemeToggle />}
              // </div>,
              <Dropdown key="dropdown" menu={{ items: userDropdownItems }} placement="bottom">
                <a
                  onClick={(e) => e.preventDefault()}
                  className={clsx('cursor-pointer', {
                    invisible: typeof brandInfo?.header === 'object' && brandInfo?.header?.userInfo === false,
                  })}
                >
                  <img className="h-6 w-6 cursor-pointer" src={brandInfo?.chatUserIcon} alt="" />
                </a>
              </Dropdown>,
            ]}
          </div>
        )}
        {IS_CHROME_EXTENSION && (
          <XMarkIcon className="h-6 w-6 cursor-pointer text-slate-400 hover:text-primary" onClick={handleCloseClick} />
        )}
      </nav>
    </header>
  )
}
