/**
 * 后台管理页面的布局
 */
import React, { Suspense, useEffect } from 'react'
import { RectangleStackIcon, UserGroupIcon } from '@heroicons/react/24/outline'
import clsx from 'clsx'
import { ConfigProvider, App } from 'antd'
import { Outlet, useLocation } from 'react-router-dom'
import { useAtom, useAtomValue, useSetAtom } from 'jotai'
import zhCN from 'antd/lib/locale/zh_CN'
import { IS_CHROME_EXTENSION, IS_H5 } from '@shared/constants'
import { askBIPageUrls } from 'src/shared/url-map'
import { MenuItem } from 'src/shared/common-types'
import { loop, filterTreeByMap } from 'src/shared/common-utils'
import { authAtom } from 'src/client/hooks/useAuth'
import {
  themeAtom,
  requestDefaultDatasetAtom,
  currentDatasetAtom,
  envAtom,
  pageMenuAccessAtom,
} from 'src/client/pages/AskBI/askBIAtoms'
import { getAntdConfigProviderTheme } from 'src/client/utils'
import AdminSidebar from '../AdminSidebar'
import {
  SvgIcon,
  menuProjectManagementIcon,
  menuSceneIcon,
  etlPaintIcon,
  menuDataSourceIcon,
  menuCSVIcon,
  menuVirtualTableIcon,
  menuMetricmodelIcon,
  ERManageMentIcon,
  menuIndexSceneIcon,
  menuSqlSearchIcon,
  menuOperationIcon,
  tenantManagement,
  menuPermissionIcon,
  appearanceIcon,
  UserIcon,
} from '../SvgIcon'
import LayoutHeader from './LayoutHeader'

let SidebarMenuItemsKeyNotInXEngine = ['scene', 'project', 'scenario']

const hideSidebarPaths = [askBIPageUrls.manage.testOverview, askBIPageUrls.manage.executeSql]
export function AskBIManageLayout() {
  const [theme, _setTheme] = useAtom(themeAtom)
  const requestDefaultDataset = useSetAtom(requestDefaultDatasetAtom)
  const currentDataset = useAtomValue(currentDatasetAtom)
  const auth = useAtomValue(authAtom)
  const getAdminSidebarMenuItems = (env: Record<string, string>): MenuItem[] => {
    const items: any = [
      (auth.manageProjectList || auth.manageScenario) && {
        key: 'scene',
        label: 'ASKBOT 场景/项目管理',
        type: 'group',
      },
      auth.manageProjectList && {
        key: 'project',
        label: 'ASKBOT 项目管理',
        icon: <SvgIcon icon={menuProjectManagementIcon} className="h-6 w-6 dark:text-white" />,
        path: askBIPageUrls.manage.manageProject.list,
      },
      auth.manageScenario && {
        key: 'scenario',
        path: askBIPageUrls.manage.scenarios.manage,
        label: 'ASKBOT 场景管理',
        icon: <SvgIcon icon={menuSceneIcon} className="h-6 w-6 dark:text-white" />,
      },
      (auth.manageDataScene ||
        auth.manageExternalDatasource ||
        auth.manageFileDatasource ||
        auth.manageDataModel ||
        auth.manageMetricModel ||
        auth.manageErManagement ||
        auth.manageMaterialization ||
        auth.manageSqlQuery ||
        auth.manageOperation ||
        auth.manageTenant) && {
        key: 'xengine-management',
        label: 'XEngine数据管理',
        type: 'group',
      },
      auth.manageDataScene && {
        key: 'data-scene',
        label: 'ETL画布',
        icon: <SvgIcon icon={etlPaintIcon} className="h-6 w-6 dark:text-white" />,
        path: askBIPageUrls.manage.xengine.dataSceneList,
      },
      auth.manageExternalDatasource && {
        key: 'external-datasource',
        label: '外部数据源',
        icon: <SvgIcon icon={menuDataSourceIcon} className="h-6 w-6 dark:text-white" />,
        children: [
          { key: 'catalog-list', path: askBIPageUrls.manage.externalDatasource.catalogList, label: '数据源管理' },
          {
            key: 'table-list',
            path: askBIPageUrls.manage.externalDatasource.tableList,
            label: '实体表管理',
          },
        ],
      },
      auth.manageFileDatasource && {
        key: 'file-datasource',
        label: '文件数据源',
        icon: <SvgIcon icon={menuCSVIcon} className="h-6 w-6 dark:text-white" />,
        path: askBIPageUrls.manage.xengine.fileDatasource,
      },
      auth.manageDataModel && {
        key: 'data-model',
        label: '虚拟表',
        icon: <SvgIcon icon={menuVirtualTableIcon} className="h-6 w-6 dark:text-white" />,
        children: [
          { label: '贴源虚拟表', key: 'virtual-table', path: askBIPageUrls.manage.xengine.virtualTable },
          { label: '业务虚拟表', key: 'business-data-model', path: askBIPageUrls.manage.xengine.businessDataModel },
          { label: '虚拟表目录管理', key: 'catalog-manager', path: askBIPageUrls.manage.xengine.catalogManager },
          {
            label: '虚拟表物化推荐',
            key: 'virtual-table-materialization-recommend',
            path: askBIPageUrls.manage.xengine.virtualTableMaterializationRecommend,
          },
        ],
      },
      auth.manageMetricModel && {
        key: 'metric-model',
        label: '指标模型管理',
        icon: <SvgIcon icon={menuMetricmodelIcon} className="h-6 w-6 dark:text-white" />,
        children: [
          { label: '指标模型', key: 'list', path: askBIPageUrls.manage.metricModel.list },
          { label: '指标模型查询', key: 'query', path: askBIPageUrls.manage.metricModel.query },
        ],
      },
      auth.manageErManagement && {
        key: 'er-management',
        label: 'ER 管理',
        icon: <SvgIcon icon={ERManageMentIcon} className="h-6 w-6 dark:text-white" />,
        children: [
          { label: 'ER查询/建模', key: 'er-manager', path: askBIPageUrls.manage.xengine.erManager },
          { label: '关联物化查询', key: 'query-related-mv', path: askBIPageUrls.manage.xengine.queryRelatedMv },
          { label: 'ER查询历史', key: 'er-query', path: askBIPageUrls.manage.xengine.erQuery },
        ],
      },
      auth.manageMaterialization && {
        key: 'materialization',
        label: '物化管理',
        icon: <SvgIcon icon={menuIndexSceneIcon} className="h-6 w-6 dark:text-white" />,
        children: [
          { label: '物化视图', key: 'material-view-list', path: askBIPageUrls.manage.xengine.materialViewList },
          { label: '任务列表', key: 'job-list', path: askBIPageUrls.manage.xengine.jobList },
          { label: '智能物化发现', key: 'material-view-scan', path: askBIPageUrls.manage.xengine.materialViewScan },
        ],
      },
      auth.manageSqlQuery && {
        key: 'sql-query',
        label: 'SQL查询',
        icon: <SvgIcon icon={menuSqlSearchIcon} className="h-6 w-6 dark:text-white" />,
        children: [
          { label: 'SQL 编辑器', key: 'sql-query-editor', path: askBIPageUrls.manage.xengine.sqlQueryEditor },
          { label: 'SQL 执行历史', key: 'sql-history', path: askBIPageUrls.manage.xengine.sqlHistory },
          { label: '实时查询状态', key: 'sql-query-manage', path: askBIPageUrls.manage.xengine.sqlQueryManage },
        ],
      },
      auth.manageOperation && {
        key: 'operation',
        label: '运维管理',
        icon: <SvgIcon icon={menuOperationIcon} className="h-6 w-6 dark:text-white" />,
        children: [
          { label: '流任务管理', key: 'stream-job-manage', path: askBIPageUrls.manage.xengine.streamJobManage },
          { label: '集群管理', key: 'cluster', path: askBIPageUrls.manage.cluster.list },
          { label: '日志', key: 'log-file-list', path: askBIPageUrls.manage.xengine.logFileList },
          { label: '工具箱', key: 'tools-box', path: askBIPageUrls.manage.xengine.toolsBox },
          { label: '数据比对', key: 'data-comparison', path: askBIPageUrls.manage.xengine.dataComparison },
        ],
      },
      auth.manageTenant && {
        key: 'tenant',
        label: '多租户',
        icon: <SvgIcon icon={tenantManagement} className="h-6 w-6 dark:text-white" />,
        children: [
          {
            label: '租户管理',
            key: 'tenant-management',
            path: askBIPageUrls.manage.xengine.tenant.selectAllTenants,
          },
        ],
      },
      // {
      //   key: 'open-meta-data',
      //   label: (
      //     <a href={env?.VITE_OPEN_META_DATA_URL} target="_blank" rel="noopener noreferrer" className="ml-1">
      //       数据目录
      //     </a>
      //   ),
      //   icon: <SvgIcon icon={DatabaseOutlined} className="h-5 w-5 dark:text-white" />,
      // },
      {
        key: 'admin-ranger-management',
        label: 'Ranger 权限管理',
        type: 'group',
      },
      {
        key: 'admin',
        label: (
          <a href={`${env?.['VITE_XENGINE_ORIGIN']}/xengine/ranger`} target="_blank" rel="noopener noreferrer">
            权限
          </a>
        ),
        icon: <SvgIcon icon={menuPermissionIcon} className="h-6 w-6 dark:text-white" />,
      },
      (auth.manageAdminResource || auth.manageAdminRole || auth.manageAdminUser) && {
        key: 'admin-management',
        label: '权限管理',
        type: 'group',
      },
      auth.manageAdminUser && {
        key: 'user',
        label: '用户管理',
        icon: <SvgIcon icon={UserIcon} className="h-6 w-6 dark:text-white" />,
        path: askBIPageUrls.manage.admin.user,
      },
      auth.manageAdminRole && {
        key: 'role',
        label: '角色管理',
        icon: <UserGroupIcon className="h-6 w-6 dark:text-white" />,
        path: askBIPageUrls.manage.admin.role,
      },
      auth.manageAdminResource && {
        key: 'resource',
        label: '资源管理',
        icon: <RectangleStackIcon className="h-6 w-6 dark:text-white" />,
        path: askBIPageUrls.manage.admin.resource,
      },
      {
        key: 'setting',
        label: '系统管理',
        type: 'group',
      },
      {
        key: 'setting-appearance',
        label: '外观',
        icon: <SvgIcon icon={appearanceIcon} className="h-6 w-6 dark:text-white" />,
        path: askBIPageUrls.manage.system.appearance,
      },
    ]
    // // {
    // //   key: 'group',
    // //   label: '用户组管理',
    // //   icon: <HomeIcon className="h-6 w-6 dark:text-white" />,
    // //   path: askBIPageUrls.manage.admin.group,
    // // },
    return items.filter(Boolean) as MenuItem[]
  }
  const location = useLocation()
  const [env] = useAtom(envAtom)
  const [pageMenuAccessKey] = useAtom(pageMenuAccessAtom)

  let AdminSidebarMenuItems = getAdminSidebarMenuItems(env)

  // 如果 xengine 单独部署对菜单进行过滤
  if (env?.VITE_PRODUCTS?.trim() === 'X-Engine') {
    const filterNotIn = env.VITE_XENGINE_KEEP_ROUTER_KEYS.split(',')
    SidebarMenuItemsKeyNotInXEngine = SidebarMenuItemsKeyNotInXEngine.filter((key) => !filterNotIn.includes(key))
    AdminSidebarMenuItems = AdminSidebarMenuItems.filter((item) => {
      return !SidebarMenuItemsKeyNotInXEngine.includes(item.key)
    })
  }
  // 移除不需要的菜单 子菜单的 key 也可以
  const VITE_REMOVE_ROUTER_KEYS = env?.VITE_REMOVE_ROUTER_KEYS.split(',')
  if (VITE_REMOVE_ROUTER_KEYS?.length) {
    VITE_REMOVE_ROUTER_KEYS.forEach((key) =>
      loop(AdminSidebarMenuItems, key, (_item, index, arr) => {
        arr.splice(index, 1)
      }),
    )
  }
  // 移除没有权限的 sider menu
  if (pageMenuAccessKey?.length) {
    const pageMenuAccessKeyMap: Record<string, boolean> = {}
    pageMenuAccessKey.forEach((item) => {
      pageMenuAccessKeyMap[item] = true
    })
    AdminSidebarMenuItems = filterTreeByMap(AdminSidebarMenuItems, pageMenuAccessKeyMap, (item: MenuItem) => {
      return pageMenuAccessKeyMap[item.key] === true
    })
    // } else {
    // AdminSidebarMenuItems.length = Math.min(AdminSidebarMenuItems.length, 2) // 接口报错 或者 没有权限控制的时候只显示前两个
  }

  // 加载默认的 dataset
  useEffect(() => {
    // 先判断 currentDatasetAtom 是否为空，当为空的时候才去请求
    if (currentDataset == null) {
      requestDefaultDataset()
    }
  }, [currentDataset, requestDefaultDataset])

  return (
    <ConfigProvider locale={zhCN} theme={getAntdConfigProviderTheme(theme)}>
      <App>
        <div
          className={clsx(
            'layout-root relative flex h-screen flex-col overflow-hidden text-black dark:bg-slate-900 dark:text-slate-100 md:overflow-visible',
            IS_CHROME_EXTENSION && 'chrome-extension',
          )}
          style={IS_H5 ? { height: window.innerHeight + 'px' } : {}}
        >
          <LayoutHeader isFullWidth={true} />
          <section className="flex flex-grow flex-row items-stretch overflow-auto border-t border-slate-200 pb-0 dark:border-slate-700">
            {hideSidebarPaths.some((url) => location.pathname.includes(url)) ? null : (
              <AdminSidebar
                menuItems={
                  // TODO merge 临时先放开 天弘需要用 ranger
                  AdminSidebarMenuItems
                  // isAllowedRanger
                  // ? AdminSidebarMenuItems
                  // : AdminSidebarMenuItems.filter((item) => !item.key?.startsWith('admin'))
                }
              />
            )}
            <div className="relative flex-1 overflow-auto bg-white p-[28px] dark:bg-slate-800">
              <Suspense fallback={<div>Loading...</div>}>
                <Outlet />
              </Suspense>
            </div>
          </section>
        </div>
      </App>
    </ConfigProvider>
  )
}
