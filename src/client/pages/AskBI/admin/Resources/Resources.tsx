import React from 'react'
import axios from 'axios'
import { useRequest } from 'ahooks'
import { App, Button, Form, Input, Popconfirm, Select, Spin, Table, Col, Row, Space } from 'antd'
import { TableProps } from 'antd/es/table'
import { MinusCircleOutlined, PlusOutlined, UserAddOutlined } from '@ant-design/icons'
import { XGroup, XRole, XUser, XResource, CasbinRule } from '@prisma/client'
import { askBIApiUrls } from 'src/shared/url-map'
import AdminCard from 'src/client/components/AdminCard'
import AdminPage from 'src/client/components/AdminPage'
import { useCreateEdit } from 'src/client/hooks/useCreateEdit'
import {
  actionOptions,
  AUTH_ACTION,
  AUTH_USER,
  extractInfoFromEnforcer,
  resourceTypeList,
  resourceTypeMap,
  ResourceTypes,
  ruleTypeList,
  RuleTypes,
} from 'src/shared/auth'
import { Llm } from 'src/shared/common-types'
import { Project, useResourceOps } from './use-resource'

export default function Resources() {
  const { message } = App.useApp()

  const { getResourceOps, getRuleOps } = useResourceOps()

  const { data, refresh, loading, runAsync } = useRequest(async (data: Partial<XResource>) => {
    const res = await axios.get<{
      data: {
        list: (XResource & { rules: CasbinRule[] })[]
        total: number
        info: Record<ResourceTypes, any[]>
      }
    }>(askBIApiUrls.auth.admin.resource.list, {
      params: data,
    })
    return res.data.data
  })

  const { data: resData, loading: resLoading } = useRequest(async () => {
    const res = await axios.get<{
      data: {
        projects?: Project[]
        llms?: Llm[]
        roles?: XRole[]
        groups?: XGroup[]
        users?: XUser[]
      }
    }>(askBIApiUrls.auth.admin.resource.data)
    return res.data.data
  })
  const { projects, llms, roles, groups, users } = resData ?? {}

  const {
    node: CreateEditNode,
    open: openCreateEdit,
    form: createEditFrom,
  } = useCreateEdit<
    Omit<NonNullable<typeof data>['list'][number], 'rules'> & { rules: { id: string; type: string; action: string } }
  >({
    refresh,
    label: '资源',
    useFormItems: ({ form }) => {
      const rules = Form.useWatch('rules', form)
      const resourceType = Form.useWatch('type', form)
      const resourceOptions = getResourceOps(resourceType).toOptions({ projects, llms })
      const ownerOptionList =
        rules?.map(({ type }: { type?: RuleTypes } = {}) => getRuleOps(type).toOptions({ users, roles, groups })) ?? []

      return (
        <>
          <Form.Item name="name" label="资源名" rules={[{ required: true }]}>
            <Input />
          </Form.Item>
          <Form.Item name="type" label="资源类型" rules={[{ required: true }]}>
            <Select options={resourceTypeList.map(({ label, value }) => ({ label, value }))} />
          </Form.Item>
          <Form.Item label="类型数据" name="typeData" dependencies={['type']} rules={[{ required: true }]}>
            <Select mode="tags" options={resourceOptions} maxTagCount="responsive" tokenSeparators={[',']} allowClear />
          </Form.Item>
          <Form.List name="rules">
            {(fields, { add, remove }, { errors }) => {
              return (
                <>
                  {fields.map((field, i) => {
                    const { key, name, ...restField } = field
                    return (
                      <div key={key} className="relative flex w-full gap-[16px]">
                        <Form.Item
                          {...restField}
                          className="w-full"
                          name={[name, 'type']}
                          label="被授予者类型"
                          rules={[{ required: true }]}
                        >
                          <Select
                            showSearch
                            options={ruleTypeList.map(({ label, value }) => ({ label, value }))}
                            filterOption={(input, option) => {
                              return (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                            }}
                          />
                        </Form.Item>
                        <Form.Item
                          {...restField}
                          className="w-full"
                          name={[name, 'id']}
                          label="被授予者Id"
                          rules={[{ required: true }]}
                        >
                          <Select
                            filterOption={(input, option?: { label: string }) => {
                              return (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                            }}
                            showSearch
                            options={ownerOptionList[i] ?? []}
                          />
                        </Form.Item>
                        <Form.Item
                          {...restField}
                          className="w-full"
                          name={[name, 'action']}
                          label="行为"
                          rules={[{ required: true }]}
                        >
                          <Select
                            filterOption={(input, option) => {
                              return (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                            }}
                            showSearch
                            options={actionOptions.map(({ label, value }) => ({ label, value }))}
                          />
                        </Form.Item>
                        <MinusCircleOutlined
                          className="dynamic-delete-button absolute right-0 top-0"
                          onClick={() => remove(field.name)}
                        />
                      </div>
                    )
                  })}
                  <Form.Item>
                    <Button type="dashed" onClick={() => add()} className="w-full" icon={<PlusOutlined />}>
                      增加权限授予用户/角色
                    </Button>
                    <Form.ErrorList errors={errors} />
                  </Form.Item>
                </>
              )
            }}
          </Form.List>
        </>
      )
    },
    handleCreate: async (data) => {
      const res = await axios.post(askBIApiUrls.auth.admin.resource.rest, data)
      return res.data
    },
    handleEdit: async (formData, payload) => {
      const body: Record<string, any> = {
        id: payload.id,
        ...formData,
      }
      const res = await axios.put(askBIApiUrls.auth.admin.resource.rest, body)
      return res.data
    },
  })

  const columns: TableProps['columns'] = [
    // {
    //   title: '资源ID',
    //   dataIndex: 'id',
    //   width: 80,
    // },
    {
      title: '资源名称',
      dataIndex: 'name',
      width: 80,
    },
    {
      title: '资源类型',
      dataIndex: 'type',
      render: (col, row) => {
        return <div key={row.id}>{resourceTypeMap[col as ResourceTypes].label}</div>
      },
      width: 120,
    },
    {
      title: '类型数据',
      dataIndex: 'typeData',
      render: (col: string[], row) => {
        const type = row.type as ResourceTypes
        const info = data?.info[type]?.filter((v) => col.includes(v.id))
        return getResourceOps(type).toTypeDataDisplay(info, col)
      },
      width: 240,
    },
    {
      title: '拥有者类型',
      dataIndex: 'rules',
      render: (rules: CasbinRule[]) => {
        if (resLoading) return <Spin />
        return (
          <div>
            {rules.map((rule) => {
              const { type } = extractInfoFromEnforcer(rule[AUTH_USER]!)
              return getRuleOps(type as RuleTypes).toRuleDisplay({ users, roles, rule, groups })
            })}
          </div>
        )
      },
    },
    {
      title: '操作',
      dataIndex: 'operations',
      render: (_, row) => (
        <div className="flex flex-row">
          <Button
            type="link"
            onClick={() => {
              openCreateEdit({ mode: 'edit', data: row })
              createEditFrom.setFieldValue(
                'rules',
                row.rules.map((v: CasbinRule) => {
                  const { id, type } = extractInfoFromEnforcer(v[AUTH_USER]!)
                  return { id, type, action: v[AUTH_ACTION] }
                }),
              )
            }}
          >
            编辑
          </Button>
          <Popconfirm
            title="删除资源"
            description={`确认删除当前${row.name}资源吗？`}
            onConfirm={() =>
              axios
                .delete(askBIApiUrls.auth.admin.resource.rest, {
                  data: { id: row.id },
                })
                .then((res) => {
                  if (res.data.code === 0) {
                    message.success('删除资源成功')
                    return refresh()
                  } else {
                    throw res.data.msg
                  }
                })
                .catch((err) => {
                  message.error('删除资源失败：' + (err?.toString() ?? ''))
                })
            }
            okText="确认"
            cancelText="取消"
          >
            <Button type="link" danger>
              删除
            </Button>
          </Popconfirm>
        </div>
      ),
    },
  ]

  const [searchForm] = Form.useForm()
  async function onSearchFinish(data: Partial<XResource>) {
    await runAsync(data)
  }

  async function onSearchReset() {
    searchForm.resetFields()
    await runAsync({})
  }

  return (
    <AdminPage title="权限管理">
      <AdminCard
        title="资源列表"
        actions={
          <Button
            type="primary"
            icon={<UserAddOutlined />}
            size="middle"
            onClick={() => openCreateEdit({ mode: 'create' })}
          >
            添加资源
          </Button>
        }
      >
        <Form form={searchForm} onFinish={onSearchFinish}>
          <Row gutter={24}>
            <Col span={8}>
              <Form.Item name="name" label="资源名称">
                <Input />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="type" label="资源类型">
                <Select options={resourceTypeList.map(({ label, value }) => ({ label, value }))} />
              </Form.Item>
            </Col>
          </Row>
          <Form.Item label={null}>
            <Space>
              <Button type="primary" htmlType="submit" loading={loading}>
                搜索
              </Button>
              <Button type="primary" loading={loading} onClick={onSearchReset}>
                重置
              </Button>
            </Space>
          </Form.Item>
        </Form>
        <Table rowKey="id" columns={columns} dataSource={data?.list} loading={loading} scroll={{ x: true }} />
      </AdminCard>
      {CreateEditNode}
    </AdminPage>
  )
}
