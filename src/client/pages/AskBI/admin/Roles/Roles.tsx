import React from 'react'
import axios from 'axios'
import dayjs from 'dayjs'
import { useRequest } from 'ahooks'
import { App, Button, Col, Form, Input, Popconfirm, Row, Space, Table } from 'antd'
import { ColumnsType } from 'antd/es/table'
import { UserAddOutlined } from '@ant-design/icons'
import { XRole } from '@prisma/client'
import { askBIApiUrls } from 'src/shared/url-map'
import AdminCard from 'src/client/components/AdminCard'
import AdminPage from 'src/client/components/AdminPage'
import { useCreateEdit } from 'src/client/hooks/useCreateEdit'
import { BUILTIN_ROLE_VALUES } from 'src/shared/auth'

export default function Roles() {
  const { message } = App.useApp()

  const { data, refresh, loading, runAsync } = useRequest(async (data: Partial<XRole>) => {
    const res = await axios.get(askBIApiUrls.auth.admin.role.list, { params: data })
    // 现在从node拿所有数据所以work，后期要注意分页后
    const resData = res.data.data
    // resData.list.unshift(...BUILTIN_ROLES.map((v) => ({ roleName: v.label, id: v.value })))
    // resData.total += BUILTIN_ROLES.length
    return resData
  })
  const { node: CreateEditNode, open } = useCreateEdit<XRole>({
    refresh,
    label: '角色',
    useFormItems: () => {
      return (
        <>
          <Form.Item name="roleName" label="角色名" rules={[{ required: true }]}>
            <Input />
          </Form.Item>
        </>
      )
    },
    handleCreate: async (data) => {
      const res = await axios.post(askBIApiUrls.auth.admin.role.rest, {
        roleName: data.roleName,
      })
      return res.data
    },
    handleEdit: async (data, payload) => {
      const body: Record<string, any> = {
        id: payload.id,
        roleName: data.roleName,
      }
      const res = await axios.post(askBIApiUrls.auth.admin.role.update, body)
      return res.data
    },
  })

  const columns: ColumnsType<XRole> = [
    {
      title: '角色ID',
      dataIndex: 'id',
      width: 80,
    },
    {
      title: '角色名',
      dataIndex: 'roleName',
      render: (text: string) => <span className="font-bold">{text}</span>,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      render: (value: string) => <span>{dayjs(value).format('YYYY-MM-DD HH:mm:ss')}</span>,
    },
    {
      title: '操作',
      dataIndex: 'operations',
      render: (_, row) =>
        BUILTIN_ROLE_VALUES.includes(row.id as any) ? (
          <div className="flex flex-row" />
        ) : (
          <div className="flex flex-row">
            <Button type="link" onClick={() => open({ mode: 'edit', data: row })}>
              编辑
            </Button>
            <Popconfirm
              title="删除角色"
              description={`确认删除当前${row.roleName}角色吗？`}
              onConfirm={() =>
                axios
                  .delete(askBIApiUrls.auth.admin.role.rest, { data: { id: row.id } })
                  .then((res) => {
                    if (res.data.code === 0) {
                      message.success('删除角色成功')
                      return refresh()
                    } else {
                      throw res.data.msg
                    }
                  })
                  .catch((err) => {
                    message.error('删除角色失败：' + (err?.toString() ?? ''))
                  })
              }
              okText="确认"
              cancelText="取消"
            >
              <Button type="link" danger>
                删除
              </Button>
            </Popconfirm>
          </div>
        ),
    },
  ]

  const [searchForm] = Form.useForm()
  async function onSearchFinish(data: Partial<XRole>) {
    await runAsync(data)
  }

  async function onSearchReset() {
    searchForm.resetFields()
    await runAsync({})
  }
  return (
    <AdminPage title="角色管理">
      <AdminCard
        title="角色列表"
        actions={
          <Button type="primary" icon={<UserAddOutlined />} size="middle" onClick={() => open({ mode: 'create' })}>
            添加角色
          </Button>
        }
      >
        <Form form={searchForm} onFinish={onSearchFinish}>
          <Row gutter={24}>
            <Col span={8}>
              <Form.Item name="roleName" label="角色名称">
                <Input />
              </Form.Item>
            </Col>
            <Col span={8} />
          </Row>
          <Form.Item label={null}>
            <Space>
              <Button type="primary" htmlType="submit" loading={loading}>
                搜索
              </Button>
              <Button type="primary" loading={loading} onClick={onSearchReset}>
                重置
              </Button>
            </Space>
          </Form.Item>
        </Form>
        <Table rowKey="id" columns={columns} dataSource={data?.list} loading={loading} scroll={{ x: true }} />
      </AdminCard>
      {CreateEditNode}
    </AdminPage>
  )
}
