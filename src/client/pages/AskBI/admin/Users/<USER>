import React, { useState } from 'react'
import axios from 'axios'
import dayjs from 'dayjs'
import { useRequest } from 'ahooks'
import { App, Button, Col, Form, Input, Popconfirm, Row, Select, Space, Table } from 'antd'
import { ColumnsType } from 'antd/es/table'
import { UserAddOutlined } from '@ant-design/icons'
import { XRole, XUser, XGroup } from '@prisma/client'
import { askBIApiUrls } from 'src/shared/url-map'
import AdminCard from 'src/client/components/AdminCard'
import AdminPage from 'src/client/components/AdminPage'
import { useCreateEdit } from 'src/client/hooks/useCreateEdit'
import { encryptUserId } from 'src/shared/common-utils'
import { getEnv } from 'src/client/utils'
import { APIResponse } from 'src/shared/common-types'
import { PageData, PageParam } from 'src/shared'
// import { Product } from 'src/shared/products'
import { AuthAdminUserListPostRequest } from 'src/shared/auth'
import { PWD_REG } from 'src/shared/constants'
// import { envAtom } from '../../askBIAtoms'

export type User = XUser & { roles: XRole[]; groups: XGroup[]; builtinRoles: string[] }

export default function Users() {
  const [pageParam, setPageParam] = useState(PageParam.of(1, 10))
  const { message } = App.useApp()

  // const { data: groupList } = useRequest(async () => {
  //   const res = await axios.get(askBIApiUrls.auth.admin.group.list)
  //   return res.data.data as { list: XGroup[]; total: number }
  // })

  const { data: roleList } = useRequest(async () => {
    const res = await axios.get<APIResponse<{ list: XRole[]; total: number }>>(askBIApiUrls.auth.admin.role.list)
    return res.data.data
  })

  const { data, refresh, loading, runAsync } = useRequest(
    async (formData: Partial<XUser>) => {
      const data: AuthAdminUserListPostRequest = {
        ...formData,
        pageSize: pageParam.pageSize,
        current: pageParam.current,
      }
      const res = await axios.get(askBIApiUrls.auth.admin.user.list, {
        params: data,
      })
      return PageData.from<User>(res.data.data)
    },
    { refreshDeps: [pageParam] },
  )

  const {
    node: CreateEditNode,
    open,
    form,
  } = useCreateEdit<User>({
    refresh,
    label: '用户',
    useFormItems: ({ mode }) => {
      return (
        <>
          <Form.Item name="username" label="用户名" rules={[{ required: true }]}>
            <Input disabled={mode !== 'create'} />
          </Form.Item>
          <Form.Item
            name="password"
            label="用户密码"
            rules={
              mode === 'create'
                ? [{ required: true }, { pattern: PWD_REG, message: '密码需要大于8位并且有大小写字母和数字和特殊字符' }]
                : [{ pattern: PWD_REG, message: '密码需要大于8位并且有大小写字母和数字和特殊字符' }]
            }
          >
            <Input.Password autoComplete="new-password" />
          </Form.Item>
          <Form.Item name="roles" label="角色" rules={[{ required: true }]}>
            <Select
              mode="multiple"
              showSearch
              allowClear
              placeholder="选择角色"
              options={roleList?.list.map((v) => ({ label: v.roleName, value: v.id })) ?? []}
            />
          </Form.Item>
          {/* <Form.Item name="groups" label="用户组">
            <Select
              mode="multiple"
              showSearch
              allowClear
              placeholder="选择用户组"
              options={groupList?.list.map((v) => ({ label: v.groupName, value: v.id })) ?? []}
            />
          </Form.Item> */}

          {/* <Form.Item name="rangerUsername" label="绑定Ranger用户">
            <Input />
          </Form.Item>
          <Form.Item name="rangerPassword" label="绑定Ranger密码">
            <Input.Password autoComplete="new-password" />
          </Form.Item> */}
        </>
      )
    },

    handleCreate: async (data) => {
      const { tag, code } = await getEnv()
      const body: typeof data = {
        username: data.username,
        password: encryptUserId(data.password!, tag, code),
        roles: data.roles,
        groups: data.groups,
      }
      // if (data.rangerUsername && data.rangerPassword) {
      //   body.rangerUsername = data.rangerUsername
      //   body.rangerPassword = encryptUserId(data.rangerPassword, tag, code)
      // }
      const res = await axios.post(askBIApiUrls.auth.admin.user.rest, body)
      return res.data
    },
    handleEdit: async (data, payload) => {
      const { tag, code } = await getEnv()
      const body: typeof data = {
        id: payload.id,
        roles: data.roles,
        groups: data.groups,
      }
      if (data.username !== payload.username) {
        body.username = data.username
      }
      if (data.password) {
        body.password = encryptUserId(data.password!, tag, code)
      }
      // if (data.rangerUsername && data.rangerPassword) {
      //   body.rangerUsername = data.rangerUsername
      //   body.rangerPassword = encryptUserId(data.rangerPassword, tag, code)
      // }
      const res = await axios.post(askBIApiUrls.auth.admin.user.update, body)
      return res.data
    },
  })

  const columns: ColumnsType<User> = [
    {
      title: '用户ID',
      dataIndex: 'id',
      width: 80,
    },
    {
      title: '用户名',
      dataIndex: 'username',
      render: (text: string) => <span className="font-bold">{text}</span>,
    },
    // {
    //   title: '所属组',
    //   dataIndex: 'groups',
    //   render: (groups: XGroup[]) => groups.map((v) => v.groupName).join(','),
    // },
    {
      title: '所属角色',
      dataIndex: 'roles',
      render: (roles: XRole[]) => roles.map((v) => v.roleName).join(','),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      render: (value: string) => <span>{dayjs(value).format('YYYY-MM-DD HH:mm:ss')}</span>,
    },
    {
      title: '操作',
      dataIndex: 'operations',
      render: (_, row) => (
        <div className="flex flex-row">
          <Button
            type="link"
            onClick={() => {
              open({ mode: 'edit', data: row })
              form.setFieldValue(
                'roles',
                row.roles.map((v) => v.id),
              )
              form.setFieldValue(
                'groups',
                row.groups.map((v) => v.id),
              )
            }}
          >
            编辑
          </Button>
          <Popconfirm
            title="删除用户"
            description={`确认删除当前${row.username}用户吗？`}
            onConfirm={() =>
              axios
                .delete(askBIApiUrls.auth.admin.user.rest, { data: { id: row.id } })
                .then((res) => {
                  if (res.data.code === 0) {
                    message.success('删除用户成功')
                    return refresh()
                  } else {
                    throw res.data.msg
                  }
                })
                .catch((err) => {
                  message.error('删除用户失败：' + (err?.toString() ?? ''))
                })
            }
            okText="确认"
            cancelText="取消"
          >
            <Button type="link" danger>
              删除
            </Button>
          </Popconfirm>
        </div>
      ),
    },
  ]

  const [searchForm] = Form.useForm()
  async function onSearchFinish(data: Partial<XUser>) {
    await runAsync(data)
  }

  async function onSearchReset() {
    searchForm.resetFields()
    await runAsync({})
  }
  return (
    <AdminPage title="用户管理">
      <AdminCard
        title="用户列表"
        actions={
          <Button type="primary" icon={<UserAddOutlined />} size="middle" onClick={() => open({ mode: 'create' })}>
            添加用户
          </Button>
        }
      >
        <Form form={searchForm} onFinish={onSearchFinish}>
          <Row gutter={24}>
            <Col span={8}>
              <Form.Item name="name" label="用户名称">
                <Input />
              </Form.Item>
            </Col>
            <Col span={8} />
          </Row>
          <Form.Item label={null}>
            <Space>
              <Button type="primary" htmlType="submit" loading={loading}>
                搜索
              </Button>
              <Button type="primary" loading={loading} onClick={onSearchReset}>
                重置
              </Button>
            </Space>
          </Form.Item>
        </Form>
        <Table
          rowKey="id"
          columns={columns}
          dataSource={data?.list}
          loading={loading}
          scroll={{ x: true }}
          pagination={{
            total: data?.total,
            onChange: async (current, pageSize) => {
              setPageParam(PageParam.of(current, pageSize))
            },
          }}
        />
      </AdminCard>
      {CreateEditNode}
    </AdminPage>
  )
}
