/**
 * Ask BI 的全局状态管理
 */
import { atom } from 'jotai'
import { nanoid } from 'nanoid'
import axios from 'axios'
import { message as antdMessage } from 'antd'
import { produce } from 'immer'
import { atomWithStorage } from 'jotai/utils'
import {
  APIResponse,
  AccessPathsType,
  BotFeature,
  BrandInfoType,
  JsonContentItem,
  DatasetDatum,
  DocReportParamsType,
  Llm,
  LlmType,
  MessageInputEditorRef,
  ReadyChartResponse,
  STRUCTURED_MESSAGE_DATA_TYPE,
  ThemeType,
  QueryState,
  ReportTemplateType,
  UserInfo,
} from '@shared/common-types'
import { sceneToDataset } from 'src/shared/common-utils'
import { ChartThemeType, CHART_THEMES } from 'src/shared/constants'
import { askBIApiUrls } from 'src/shared/url-map'
import { Dimension, Id, Metric, MetricConfigForProject, ProjectType, SceneType } from 'src/shared/metric-types'
import {
  Chat,
  ChatStatus,
  getConfigFromProjectMetricConfig,
  getTempDimensionList,
  searchMetricConfigBySearchValue,
} from 'src/client/utils'
import { isBaoWu } from 'src/shared/baowu-share-utils'
import { AnsChat } from 'src/client/components/chats'
import AskBot from '/img/askBot.webp'
import dipeakChatUserIcon from '/img/dipeakChatUserIcon.webp'
import { PlanData } from 'src/client/components/agent/utils/agent'
import { ConfigInfo, extractConfigInfo } from 'src/shared'
import { MetricConfig } from 'src/shared/types/metric-config'

export const isSubmittingAtom = atom<boolean>(false)
export const currentLoginUserAtom = atomWithStorage<UserInfo | null>('loginUserInfoData', null)
export const loginAppIdAtom = atomWithStorage<string>('loginAppIdAtom', '')
export const brandInfoAtom = atomWithStorage<BrandInfoType>('brandInfo', {
  appId: '',
  logo: AskBot,
  chatUserIcon: dipeakChatUserIcon,
  companyName: '数巅科技',
  brandName: 'DIPEAK',
  jointLogo: '',
})
// 智能报告引擎 报告生成的当前选中的报告模板
export const currentReportTemplateAtom = atomWithStorage<ReportTemplateType | null>('currentReportTemplate', null)

export const currentParamsExtractApiAtom = atomWithStorage<string>('currentParamsExtractApi', '')

// 智能报告引擎中报告模板的分时时间范围
export const reportDataTimeParamsAtom = atom<{
  timeColumn: string
  timeRangeEnd: string
  timeRangeStart: string
} | null>(null)
export const paramsExtractUrlListAtom = atom<string[]>([])
export const requestParamsExtractUrlListAtom = atom(null, async (_get, set) => {
  try {
    const response = await axios.get<APIResponse<string[]>>(askBIApiUrls.env.paramsExtractUrlList)
    if (response && response.data.data) {
      set(paramsExtractUrlListAtom, response.data.data || [])
      set(currentParamsExtractApiAtom, response.data.data[0])
    }
  } catch (error: any) {
    set(paramsExtractUrlListAtom, [])
  }
})

const getHelloTexts = (brandName: string) => {
  let helloTexts: string[] = []
  const helloTemplate = [
    '你好，我是 DIPeak BI 智能分析助手，从一个问题开始你的数据分析之旅吧',
    '欢迎使用 DIPeak BI 智能分析助手！有什么问题我可以帮助你解答？',
    '👋 我是 DIPeak BI 智能分析助手。有什么需要帮助的吗？请随时提问',
  ]

  switch (brandName) {
    case 'DIPEAK':
      helloTexts = helloTemplate
      break
    case 'China Telecom':
      helloTexts = helloTemplate.map((text) => text.replace(/DIPeak/g, '中国电信'))
      break
    case 'bbwsy':
      helloTexts = helloTemplate.map((text) => text.replace(/DIPeak/g, 'GK'))
      break
    default:
      helloTexts = helloTemplate
      break
  }

  return helloTexts
}

export const pageHeightAtom = atom<string | number>(globalThis.innerHeight)

export const initChatsAtom = atom(null, async (get, set) => {
  const brandInfo = get(brandInfoAtom)
  const brandName = brandInfo.brandName
  const helloTexts = getHelloTexts(brandName)
  const helloText = helloTexts[Math.floor(Math.random() * helloTexts.length)]
  const item: Chat = {
    id: nanoid(),
    isSystemPrompt: true,
    askTime: new Date(),
    ask: null as any,
    ans: [
      {
        role: 'assistant',
        sceneId: 'MULTI_SCENE_CHAT_MOCK_SCENE_ID',
        status: 'success',
        content: [
          {
            type: 'hello-text',
            text: helloText,
          },
        ],
      },
    ],
    selectedSceneId: 'MULTI_SCENE_CHAT_MOCK_SCENE_ID',
    docAns: {
      role: 'assistant',
      status: 'pending',
      content: [
        {
          type: 'hello-text',
          text: helloText,
        },
      ],
    },
  }
  set(chatsAtom, [item])
})
export const chatsAtom = atom<Chat[]>([])

export const conversationIdAtom = atom<string | null>(null)
export const cancelTokenSourceAtom = atom<{ abort: (...args: any) => void }>(new AbortController())

// 多租户列表
export const tenantListAtom = atom<
  {
    tenantName: string
    state: 'NORMAL' | 'DISCARDED'
    projects?: { projectName: string; comment: string }[]
    catalogName: string
  }[]
>([])

// 首页tab bi是数据洞察 doc是知识洞察 report是报告生成
export const botFeatureTypeAtom = atomWithStorage<BotFeature>('BotFeatureValue', 'BI')
export const docReportParamsAtom = atom<DocReportParamsType | null>(null)
export const lastSuccessModelNameAtom = atom<string>('')
export const reportOpListAtom = atom<{ key: string; value: string }[]>([])

/** 当前推荐问题的suggestion数据 */
export const currentSuggestionAtom = atom<{
  data: string[]
  error: Error | null
  loading: boolean
} | null>(null)

/** 当前场景的提问历史 */
export const askHistoryAtom = atom<string[] | null>(null)

/** 项目与场景相关的信息 从xengine获取 */
export const semanticProjectInfoAtom = atomWithStorage<ProjectType[]>('semanticProjectInfo', [])

/** Dataset 中包含了当前的 项目/场景相关信息 */
const datasetAtom = atom<DatasetDatum | null>(null)

/** 在localStorage中持久化存储的 Dataset 需要校验是否有效 */
export const storedDatasetAtom = atomWithStorage<DatasetDatum | null>('currentDataset', null)

/** 是否正在加载 dataset 的标识，加载成功后会设置为false */
export const isLoadingDatasetAtom = atom<boolean>(true)

/** 是否正在加载 metricConfig 相关信息 */
export const isLoadingMetricConfigDataAtom = atom<boolean>(false)

// 选择了项目 基于项目问答
export const isProjectChosenAtom = atomWithStorage<boolean>('isProjectChosen', false, undefined, {
  getOnInit: true,
})

/** 校验持久化的storedDataset是否有效 返回有效的Dataset */
function verifyStoredDataset(projectInfo: ProjectType[], storedDataset: DatasetDatum | null): DatasetDatum | null {
  if (!storedDataset) return null

  const matchedProject = projectInfo.find((project) => project.id === storedDataset.projectId)
  const matchedScene = matchedProject?.scenes.find((scene) => scene.id === storedDataset.sceneId)
  const isValidDataset =
    matchedScene &&
    matchedScene.label === storedDataset.sceneLabel &&
    matchedScene.tableName === storedDataset.tableName &&
    matchedScene.enableFollowUpQuestion === storedDataset.enableFollowUpQuestion &&
    matchedScene.enableMetricExactMatch === storedDataset.enableMetricExactMatch &&
    matchedScene.enableTryQueryUp === storedDataset.enableTryQueryUp &&
    matchedScene.enableAccMetricToastWhenEmptyData === storedDataset.enableAccMetricToastWhenEmptyData
  if (isValidDataset) {
    console.info('持久化Dataset数据校验成功')
    return storedDataset
  } else {
    console.info('持久化Dataset数据校验失败，准备获取默认的Dataset信息...')
    return null
  }
}

export const requestDefaultDatasetLoadingAtom = atom(false)

/** 初始化 获取默认的 dataset 信息 */
export const requestDefaultDatasetAtom = atom(null, async (get, set) => {
  const env = await get(envAtom)
  if (env?.VITE_PRODUCTS?.trim() === 'X-Engine') {
    return null
  }
  try {
    set(requestDefaultDatasetLoadingAtom, true)
    const { data: projectInfo } = (
      await axios.get<
        APIResponse<{
          projects: ProjectType[]
          DEFAULT_SELECT_PROJECT: boolean
          DEFAULT_PROJECT: string
          DEFAULT_SCENE: string
        }>
      >(askBIApiUrls.auth.projects)
    ).data
    const { projects: semanticProjectInfo, DEFAULT_SELECT_PROJECT, DEFAULT_PROJECT, DEFAULT_SCENE } = projectInfo ?? {}

    if (semanticProjectInfo) {
      set(semanticProjectInfoAtom, semanticProjectInfo || [])
    } else {
      throw new Error('获取当前用户的项目信息失败')
    }
    const storedDataset = verifyStoredDataset(semanticProjectInfo, get(storedDatasetAtom))

    // 在env中可以设置默认的项目明name以及场景名label，如果没有设置则默认选择第一个项目和场景
    const projectItem = DEFAULT_PROJECT
      ? (semanticProjectInfo.find((item) => item.name === DEFAULT_PROJECT) ?? semanticProjectInfo[0])
      : semanticProjectInfo.find((p) => p.scenes.length)

    const defaultScene =
      projectItem && semanticProjectInfo.find((p) => p.scenes.length)?.scenes[0]
        ? sceneToDataset({
            scene: projectItem.scenes.find((scene) => scene.label === DEFAULT_SCENE) ?? projectItem.scenes[0],
            project: projectItem,
          })
        : null
    const dataset = storedDataset ?? defaultScene

    if (DEFAULT_SELECT_PROJECT) {
      console.info('Set isProjectChosen true because SELECT_PROJECT is', DEFAULT_SELECT_PROJECT)
      set(isProjectChosenAtom, true)
    }

    set(currentDatasetAtom, dataset)
    set(isLoadingDatasetAtom, false)
  } catch (error: any) {
    set(currentDatasetAtom, null)
    set(isLoadingDatasetAtom, false)
    antdMessage.error(error.message || '场景信息获取失败，请联系管理员')
  } finally {
    set(requestDefaultDatasetLoadingAtom, false)
  }
})

/** 获取/更新 当前的dataset 获取当前项目的指标信息 */
export const currentDatasetAtom = atom(
  (get) => get(storedDatasetAtom),
  async (get, set, newDataset: DatasetDatum | null) => {
    if (!newDataset) {
      console.info('更新Dataset失败，传入的值为空')
      return
    }
    set(storedDatasetAtom, newDataset)
    set(datasetAtom, newDataset)

    // 获取某一个 场景 下面的指标信息
    try {
      const isProjectChosen = get(isProjectChosenAtom)
      console.info('Is Project Chosen', isProjectChosen)

      set(isLoadingMetricConfigDataAtom, true)

      if (isProjectChosen) {
        const metricConfigDataOfProject = await axios.get<APIResponse<MetricConfigForProject>>(
          askBIApiUrls.metrics.listInProject(newDataset.projectId),
        )
        metricConfigDataOfProject.data.data?.forEach((item) => {
          item.data = MetricConfig.from(item.data)!
        })
        set(metricConfigOfProjectAtom, metricConfigDataOfProject.data.data || null)
        set(
          metricConfigAtom,
          metricConfigDataOfProject.data.data?.find((e) => e.sceneId === newDataset.sceneId)?.data || null,
        )
      } else {
        const projectList = get(semanticProjectInfoAtom)
        const sceneDetail = (projectList || [])
          .find((project) => project.id === newDataset.projectId)
          ?.scenes.find((scene) => scene.id === newDataset.sceneId)
        if (sceneDetail?.agent?.includes('BI')) {
          const metricConfigOfScene = await axios.get<APIResponse<MetricConfig>>(askBIApiUrls.auth.metrics, {
            params: { sceneId: newDataset.sceneId },
          })
          const response = await axios.post(askBIApiUrls.metrics.getAllDimensionsValues, {
            sceneId: newDataset.sceneId,
          })
          if (metricConfigOfScene.data.data && response.data.data) {
            set(metricConfigAtom, (old) => {
              const metricConfig = MetricConfig.from(old ?? metricConfigOfScene.data.data)
              if (!metricConfig) return null
              for (const item of response.data.data) {
                const dim = metricConfig.dimensionRecord[metricConfig.formatName(item.name)]
                if (dim) {
                  dim.values = item.values
                }
              }
              return metricConfig
            })
          } else {
            set(metricConfigAtom, MetricConfig.from(metricConfigOfScene.data.data))
          }
        }
      }
      set(isLoadingMetricConfigDataAtom, false)
      // !!!特殊处理只在药监局下显示数字人，POC后续删除
      const isShowDigitalHuman = newDataset.sceneId === 'dtVjr9M6Dz0Hye0u' || newDataset.sceneId === 'U9ilCbuBmwKYkrP2'
      if (!isShowDigitalHuman) {
        set(showDigitalHumanAtom, isShowDigitalHuman)
      }
    } catch (error: any) {
      antdMessage.error(error.message || '获取指标中心信息失败，请联系管理员')
      set(isLoadingMetricConfigDataAtom, false)
      set(metricConfigOfProjectAtom, null)
      set(metricConfigAtom, null)
    }
    // 更新针对场景的推荐问题
    axios
      .get(askBIApiUrls.suggestions, {
        params: { sceneId: newDataset.sceneId, tableName: newDataset.tableName },
      })
      .then((res) => {
        set(currentSuggestionAtom, {
          data: res.data.data,
          error: null,
          loading: false,
        })
      })
      .catch((error) => {
        console.error('Get suggest questions with error:', error)
        set(currentSuggestionAtom, {
          data: [],
          error,
          loading: false,
        })
      })
    const isProjectChosen = get(isProjectChosenAtom)
    axios
      .get(askBIApiUrls.convers.askHistory, {
        params: {
          sceneId: newDataset.sceneId,
          isProjectChosen,
          projectId: newDataset.projectId,
        },
      })
      .then((res) => {
        set(askHistoryAtom, res.data.data)
      })
      .catch((error) => {
        set(askHistoryAtom, [])
        console.error('Get Ask History with error:', error)
      })
  },
)

export const allChartsAtom = atom<ReadyChartResponse[]>([])
export const envAtom = atom(async () => {
  const response = await axios.get<APIResponse<Record<string, string>>>(askBIApiUrls.env.list)
  return response.data?.data || {}
})
export const pageMenuAccessAtom = atom(async () => {
  try {
    const response = await axios.post<APIResponse<AccessPathsType[]>>(askBIApiUrls.rolesPermission.pageList, {
      serviceName: 'xengine_path',
    })
    const allowedResources: string[] = []
    const pageAccessResult = response.data.data
    if (pageAccessResult == null || pageAccessResult.length === 0) {
      return allowedResources
    }
    pageAccessResult.forEach((item: { allowed: boolean; path: string }) => {
      if (item.allowed && /^\/menuPermission\//.test(item.path)) {
        allowedResources.push(item.path.replace('/menuPermission/', ''))
      }
    })
    return allowedResources
  } catch (error: any) {
    console.error('Get access resource error:', error.message, error.response?.data)
  }
})

export const showChatHistoryListAtom = atom<boolean>(false)
export const showAskHistoryListAtom = atom<boolean>(false)

/** 模型列表 */
export const llmListAtom = atom<Llm[]>([])
/** 当前使用的 Llm 类型 */
export const llmTypeAtom = atom<LlmType | null>(null)
export const currentChartThemeTypeAtom = atom<ChartThemeType>('default')
export const currentChartThemeNameAtom = atom(
  (get) => CHART_THEMES.find((theme) => theme.type === get(currentChartThemeTypeAtom))?.name || '未知',
)

/** 颜色的默认值，就是先检查 localStorage，然后检查 prefers-color-scheme */
// const defaultTheme = IS_DARK ? 'dark' : 'light'
const defaultTheme = 'light'
export const themeAtom = atom<ThemeType>(defaultTheme)

/** 指标中心的元信息。TODO: 改个更好的名字 */
export const metricConfigAtom = atom<MetricConfig | null>(null)
export const metricConfigOfProjectAtom = atom<MetricConfigForProject | null>(null)
// 变量名字先用这个 因为指标名字上面有重复的。
export const metricListAtomForOption = atom<(Metric & Id)[]>([])

// json的已输入的提问内容数据
export const agentOriginStructuredMessageAtom = atom<JsonContentItem[]>([])
// export const agentStructuredMessageAtom = atom<string | JsonContentItem[]>('')
export const agentStructuredMessageAtom = atom<JsonContentItem[], [JsonContentItem[] | string], void>(
  (get) => get(agentOriginStructuredMessageAtom),
  (get, set, data) => {
    let result: JsonContentItem[]
    if (!data || data.length === 0) {
      set(agentOriginStructuredMessageAtom, [])
      const { setHtml } = get(messageInputEditorRefAtom)
      setHtml('')
      return
    }
    if (typeof data === 'string') {
      result = [
        {
          type: STRUCTURED_MESSAGE_DATA_TYPE.TEXT,
          'data-type': STRUCTURED_MESSAGE_DATA_TYPE.TEXT,
          'data-content': data,
        },
      ]
    } else {
      result = data
    }
    set(agentOriginStructuredMessageAtom, result)
  },
)
// 根据json派生的纯文本提问内容
export const agentMessageAtom = atom<string>((get) => {
  const structuredMessage = get(agentStructuredMessageAtom)

  let result = ''
  structuredMessage.map((item) => {
    if (item['data-content']) {
      result += item['data-content']
    }
  })
  return result
})

/** 输入框处是否显示语音识别样式 */
export const isShowSpeechRecognitionAtom = atom<boolean>(false)
/** 存对话输入框相关方法 */
export const messageInputEditorRefAtom = atom<MessageInputEditorRef>({ setHtml: () => {} })

export const updateMessageAndHtmlAtom = atom<null, [JsonContentItem[] | JsonContentItem | string, boolean?], void>(
  null,
  (get, set, data, updateCursor = true) => {
    const { updateCursorPosition, setHtml } = get(messageInputEditorRefAtom)
    let result: JsonContentItem[] = []

    const structuredMessage = get(agentStructuredMessageAtom)
    if (!data || data === '') {
      set(agentStructuredMessageAtom, data)
    } else if (typeof data === 'string') {
      const result = [{ 'data-type': STRUCTURED_MESSAGE_DATA_TYPE.TEXT, 'data-content': data }]
      set(agentStructuredMessageAtom, result)
      setHtml?.(result)
    } else if (Array.isArray(data)) {
      // 如果是数组,则认为是完整替换原数据
      set(agentStructuredMessageAtom, data)
      setHtml?.(data)
    } else {
      result = [...structuredMessage, data]
      set(agentStructuredMessageAtom, result)
      setHtml?.(result)
    }
    if (updateCursor) {
      updateCursorPosition?.()
    }
  },
)

// 已选中的指标数据
export const agentSelectedMetricAtom = atom<JsonContentItem[]>((get) => {
  const structuredMessage = get(agentStructuredMessageAtom)
  if (typeof structuredMessage === 'string') {
    return []
  }

  const result: JsonContentItem[] = structuredMessage.filter((item) => {
    return item['data-type'] === STRUCTURED_MESSAGE_DATA_TYPE.METRIC
  })
  return result
})

// 已选中的维度数据
export const agentSelectedDimensionAtom = atom<JsonContentItem[]>((get) => {
  const structuredMessage = get(agentStructuredMessageAtom)
  if (typeof structuredMessage === 'string') {
    return []
  }

  const result: JsonContentItem[] = structuredMessage.filter((item) => {
    return item['data-type'] === STRUCTURED_MESSAGE_DATA_TYPE.DIMENSION
  })
  return result
})

// 热门指标中已选中的维度数据
const originSelectedHotMetricAtom = atom<JsonContentItem | null>(null)
// 热门指标中已选中的维度数据
export const selectedHotMetricAtom = atom<JsonContentItem | null, [JsonContentItem | null], void>(
  (get) => get(originSelectedHotMetricAtom),
  (get, set, data) => {
    console.info('get', get)
    set(originSelectedHotMetricAtom, data)
    set(isShowRecommendedMetricsAtom, true)
  },
)

export const isShowRecommendedMetricsAtom = atom<boolean>(false)
export const recommendedMetricsAtom = atom<
  Promise<Array<{ id: number; type: 'digit' | 'trend' | 'topN'; jsonContent: JsonContentItem[] }>>
>(async (get) => {
  const selectedHotMetric = get(selectedHotMetricAtom)
  const metricName = selectedHotMetric?.['data-name']
  if (!metricName) {
    return Promise.resolve([])
  }
  const currentDataset = get(currentDatasetAtom)
  const data = await axios
    .post(askBIApiUrls.metrics.recommendQuestion, {
      metricName,
      sceneId: currentDataset?.sceneId,
    })
    .then((res) => {
      return res.data.data
    })
    .catch(() => {
      return []
    })
  return data
})

type SearchedMetricConfig = {
  [Property in keyof Pick<MetricConfig, 'allMetrics' | 'allDimensions'>]: {
    list: MetricConfig[Property]
    searchValue?: string
  }
}

type AllMetricsAndDimensionsType = {
  allDimensions: Dimension[]
  allMetrics: Metric[]
}

export const AllMetricsAndDimensionsAtom = atom<AllMetricsAndDimensionsType>((get) => {
  const isProjectChosen = get(isProjectChosenAtom)
  const metricConfigOfProject = get(metricConfigOfProjectAtom)

  const metricConfig = get(metricConfigAtom)
  const result: AllMetricsAndDimensionsType = {
    allDimensions: metricConfig?.allDimensions || [],
    allMetrics: metricConfig?.allMetrics || [],
  }
  if (isProjectChosen) {
    result.allMetrics = getConfigFromProjectMetricConfig<Metric>(metricConfigOfProject, 'allMetrics')
    result.allDimensions = getConfigFromProjectMetricConfig<Dimension>(metricConfigOfProject, 'allDimensions')
  }
  return result
})

// 已选的维度源数据
export const searchedOriginMetricConfigValueAtom = atom<SearchedMetricConfig>({
  allMetrics: { list: [], searchValue: '' },
  allDimensions: { list: [], searchValue: '' },
})
// 已选的维度数据
export const searchedMetricConfigValueAtom = atom<SearchedMetricConfig>((get) => {
  const originSelectedHotMetric = get(searchedOriginMetricConfigValueAtom)

  const { allMetrics, allDimensions } = originSelectedHotMetric

  const metricConfig = get(metricConfigAtom)

  const allMetricsAndDimensions = get(AllMetricsAndDimensionsAtom)
  const metrics = allMetricsAndDimensions.allMetrics
  if (allMetrics.list.length === 0 && allDimensions.list.length === 0) {
    const tempList = getTempDimensionList(allMetricsAndDimensions.allDimensions, isBaoWu(metricConfig?.metricTableName))

    return {
      allMetrics: {
        list: metrics || [],
        searchValue: allMetrics.searchValue,
      },
      allDimensions: {
        list: tempList,
        searchValue: allDimensions.searchValue,
      },
    }
  }
  return originSelectedHotMetric
})

// 根据搜索词搜索已选的维度数据
export const searchMetricConfigAtom = atom<null, [string], void>(null, async (get, set, searchValue) => {
  const metricConfig = get(metricConfigAtom)
  const { allDimensions, allMetrics } = get(AllMetricsAndDimensionsAtom)

  let searchedAllMetrics: SearchedMetricConfig['allMetrics'] = { list: [], searchValue: '' }
  let searchedAllDimensions: SearchedMetricConfig['allDimensions'] = { list: [], searchValue: '' }
  // 搜索数据过滤掉虚拟维度
  const tempList = getTempDimensionList(allDimensions, isBaoWu(metricConfig?.metricTableName))

  if (allMetrics?.length > 0) {
    if (searchValue) {
      searchedAllMetrics = await searchMetricConfigBySearchValue<Metric>(allMetrics, searchValue)
    } else {
      // 如果查询的字符串为空,则返回全部的
      searchedAllMetrics.list = allMetrics
    }
  }

  if (tempList?.length > 0) {
    if (searchValue) {
      searchedAllDimensions = await searchMetricConfigBySearchValue<Dimension>(tempList, searchValue)
    } else {
      // 如果查询的字符串为空,则返回全部的
      searchedAllDimensions.list = tempList
    }
  }

  set(searchedOriginMetricConfigValueAtom, { allDimensions: searchedAllDimensions, allMetrics: searchedAllMetrics })
})

/**是否显示维度-指标弹窗 */
export const isShowMatchedParamDrawerAtom = atom<boolean>(false)
/** 最近选中的非码值的数据 */
export const latestSelectedMessageAtom = atom<JsonContentItem>({})

/** 最近输入的数据 */
export const latestMessageAtom = atom<JsonContentItem>((get) => {
  const structuredMessage = get(agentOriginStructuredMessageAtom)
  return structuredMessage[structuredMessage.length - 1]
})

export const isShowSubDimensionAtom = atom<boolean>(false)
/**码值列表 */
export const subDimensionsAtom = atom<string[]>((get) => {
  const latestSelectedMessage = get(latestSelectedMessageAtom)
  const latestMessage = get(latestMessageAtom)
  const metricConfig = get(metricConfigAtom)
  if (
    !metricConfig ||
    !latestMessage ||
    (latestMessage['data-type'] !== STRUCTURED_MESSAGE_DATA_TYPE.DIMENSION &&
      latestMessage['data-type'] !== STRUCTURED_MESSAGE_DATA_TYPE.SUB_DIMENSION)
  ) {
    return []
  }
  if (latestSelectedMessage['data-type'] === STRUCTURED_MESSAGE_DATA_TYPE.DIMENSION) {
    const { allDimensions } = metricConfig
    const findData = allDimensions.find((item) => {
      if (item.label === latestSelectedMessage['data-content']) {
        return true
      }
      return false
    })
    if (findData?.type === 'categorical' && findData?.values) {
      return findData.values.filter(Boolean)
    }
  }
  return []
})

export const currentFollowUpQuestionNextAtom = atom<AnsChat | null>(null)

export const configSceneParamsAtom = atom<any>(null)

// 标识是从场景里面点击了报告生成，并赋值modelName
export const isReportModelNameTagAtom = atom<string | null>(null)

// 场景弹窗中是否勾选了对文件提问的switch开关
export const scenePopoverIsCheckedDocAtom = atom<boolean>(false)

// 有权限的页面list
export const accessPathsAtom = atomWithStorage<AccessPathsType[]>('pageAccessResult', [])

export let intervalId: NodeJS.Timeout | null = null

export const stopAssistantMessageAtom = atom(null, async (get, set, { chatId }: { chatId: string }) => {
  // 1. 停止提交状态
  set(isSubmittingAtom, false)

  // 2. 终止请求
  const cancelTokenSource = get(cancelTokenSourceAtom)
  cancelTokenSource.abort('用户手动终止')

  // 3. 清除定时器
  if (intervalId) {
    clearInterval(intervalId)
    intervalId = null
  }

  const chat = get(chatsAtom).find((item) => item.id === chatId)

  // 4. 更新页面显示
  set(
    chatsAtom,
    produce((draft) => {
      const theChat = draft.find((item) => item.id === chatId)
      if (theChat) {
        const newAns = theChat.ans.map((ans) => {
          if (ans.status !== ChatStatus.success) {
            return {
              ...ans,
              content: [{ type: 'text' as const, text: '用户手动终止' }],
              status: ChatStatus.failure,
            }
          }
          return ans
        })
        theChat.ans = newAns
      }
    }),
  )

  // 记录日志
  axios.post(askBIApiUrls.converChats.stopChat, { traceId: chat?.traceId, chat })
})

/**
 * 格式化解析query后的数据存储，结合getQueryState，后面对query的处理都通过它们转成js对象后再处理
 */
export const queryStateAtom = atomWithStorage<QueryState>('queryState', {
  enableOnlyChat: false,
  enableAutoLogin: false,
  enableReloadQueryState: false,
  enableLangfuse: false,
  enableNextGeneration: false,
})

// url上是否携带了问卷调查的参数
export const questionnaireAtom = atom<boolean>(false)
/**
 * 是否展示Lingjing数字人
 */
export const showDigitalHumanAtom = atom<boolean>(false)

export const confidenceDrawerConfigAtom = atom<{ open: boolean; chatId: null | string; planData?: PlanData }>({
  open: false,
  chatId: null,
  planData: undefined,
})

export const deepSeekEnableNetWorkAtom = atomWithStorage<boolean>('deepSeekEnableNetWork', false)

export const lastTenantSelectionAtom = atomWithStorage<{
  catalogName: string
  project: string
  tenant: string
} | null>('lastTenantSelection', null)

export const isOpenTenantAtom = atom(false)

export const metricConfigRecordAtom = atom((get) => {
  const isProjectChosen = get(isProjectChosenAtom)
  const currentDataset = get(currentDatasetAtom)
  const metricConfig = get(metricConfigAtom)
  const metricConfigOfProject = get(metricConfigOfProjectAtom)
  const res: Record<string, MetricConfig> = {}
  if (isProjectChosen && metricConfigOfProject) {
    for (const item of metricConfigOfProject) {
      res[item.sceneId] = item.data
    }
  } else if (!isProjectChosen && currentDataset?.sceneId && metricConfig) {
    res[currentDataset.sceneId] = metricConfig
  }
  return res
})

export const projectAtom = atom((get) => {
  const currentDataset = get(currentDatasetAtom)
  const semanticProjectInfo = get(semanticProjectInfoAtom)
  return semanticProjectInfo.find((p) => p.id === currentDataset?.projectId)
})

export const scenesListAtom = atom((get) => {
  const currentDataset = get(currentDatasetAtom)
  const isProjectChosen = get(isProjectChosenAtom)
  const project = get(projectAtom)
  if (isProjectChosen) {
    return project?.scenes ?? []
  } else {
    return project?.scenes?.filter((s) => s.id === currentDataset?.sceneId) ?? []
  }
})

export const sceneRecordAtom = atom((get) =>
  get(scenesListAtom).reduce(
    (o, scene) => {
      o[scene.id] = scene
      return o
    },
    {} as Record<string, SceneType>,
  ),
)

// 获取配置信息的统一接口，已经兼容选择项目和选择单场景的情况
export const configInfoAtom = atom<ConfigInfo & { isBiAgent: boolean; isDocAgent: boolean }>((get) => {
  const project = get(projectAtom)
  const list = get(scenesListAtom)
  const isProjectChosen = get(isProjectChosenAtom)
  let isDocAgent = false
  let isBiAgent = false
  for (const scene of list) {
    isDocAgent ||= !!scene.agent?.includes('Doc')
    isBiAgent ||= !!scene.agent?.includes('BI')
  }
  return {
    isDocAgent,
    isBiAgent,
    ...extractConfigInfo(isProjectChosen ? project : list[0]),
  }
})

export const isDianxinAtom = atom((get) => {
  const userInfo = get(currentLoginUserAtom)
  return !!(
    userInfo &&
    userInfo.groups &&
    userInfo.groups.find((item) => item.groupName === 'China Telecom' || item.groupName === 'china-telecom')
  )
})
