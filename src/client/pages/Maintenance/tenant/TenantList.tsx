import React, { useEffect, useMemo, useState } from 'react'
import { App, Button, Modal, Popover, Result, Space, Table, Tag, Form, Input, Select, Checkbox, Tabs } from 'antd'
import axios from 'axios'
import { useRequest } from 'ahooks'
import { ColumnsType } from 'antd/es/table'
import { MoreOutlined, PlusOutlined, InfoCircleOutlined } from '@ant-design/icons'
import Search from 'antd/es/input/Search'
import dayjs from 'dayjs'
import AdminPage from 'src/client/components/AdminPage'
import AdminCard from 'src/client/components/AdminCard'
import { APIResponse } from 'src/shared/common-types'
import { Id, Tenant, TenantListResponse } from 'src/shared/metric-types'
import { askBIApiUrls } from 'src/shared/url-map'
import TextHighlight from 'src/client/components/TextHighlight'
import { SvgIcon, tenantDelWarn, tenantRecover } from 'src/client/components/SvgIcon'

interface CreateTenantRequest {
  catalogName: string
  comment: string
  cpuCoreCount: string
  diskCapacity: string
  ifNotExists: boolean
  memorySize: string
  needCreateCatalog: boolean
  tenantName: string
  userNames: string[]
}

interface ProjectCreateRequest {
  projectName: string
  description: string
  users: string[]
}

interface Project {
  projectName: string
  comment: string
  users?: string[]
}

export default function TenantManage({ className }: { className?: string }) {
  const { message: antdMessage } = App.useApp()
  const [showTenantForm, setShowTenantForm] = useState(false)
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [editTenantName, setEditTenantName] = useState<string>()
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [showRestoreModal, setShowRestoreModal] = useState(false)
  const [deletingTenant, setDeletingTenant] = useState<Tenant & Id>()
  const [mode, setMode] = useState<'create' | 'edit' | 'view'>('create')
  const [currentProject, setCurrentProject] = useState<Project | null>(null)
  const [searchValue, setSearchValue] = useState('')
  const [current, setCurrent] = useState<number>(1)
  const [total, setTotal] = useState<number>(0)
  const [form] = Form.useForm()
  const [activeTab, setActiveTab] = useState<'tenant' | 'project'>('tenant')
  const [showProjectForm, setShowProjectForm] = useState(false)
  const [projectForm] = Form.useForm()
  const [isCheckBox, setIsCheckBox] = useState(false)
  const { TextArea } = Input
  const {
    run: loadTenantList,
    data: tenantList,
    loading: isTenantListLoading,
    error: tenantListError,
  } = useRequest(
    async (params?: { current: number; pageSize: number }) => {
      const response = await axios.get<APIResponse<TenantListResponse>>(askBIApiUrls.xengine.tenant.list, {
        params,
      })

      if (!response.data.data) {
        return { list: [], total: 0 }
      }
      setTotal(response.data.data.total)
      return response.data.data
    },
    {
      onError: (error) => {
        antdMessage.error('获取租户信息失败')
        console.error('Load tenant list with error', error)
      },
    },
  )

  const pageNumberChange = (page: number, pageSize: number) => {
    setCurrent(page)
    loadTenantList({ current: page, pageSize })
  }

  useEffect(() => {
    loadTenantList()
  }, [loadTenantList])

  const { run: handleDelete, loading: isDeleteLoading } = useRequest(
    (name: string) => {
      return axios.post(askBIApiUrls.xengine.tenant.delete, {
        tenantName: name,
        ifExists: true,
        needDropCatalog: false,
        dropDirectly: false,
        dropFromRecycling: false,
      })
    },
    {
      manual: true,
      onSuccess: () => {
        antdMessage.success('删除成功')
        loadTenantList({ current, pageSize: 10 })
      },
      onError: (error) => {
        antdMessage.error('删除租户失败：' + error.message || '未知原因')
        console.error('deleteRole error =', error)
      },
    },
  )

  const { run: handlePermanentDelete, loading: isPermanentDeleteLoading } = useRequest(
    (name: string) => {
      return axios.post(askBIApiUrls.xengine.tenant.delete, {
        tenantName: name,
        ifExists: true,
        needDropCatalog: true,
        dropDirectly: false,
        dropFromRecycling: true,
      })
    },
    {
      manual: true,
      onSuccess: () => {
        antdMessage.success('彻底删除成功')
        loadTenantList({ current, pageSize: 10 })
      },
      onError: (error) => {
        antdMessage.error('彻底删除租户失败：' + error.message || '未知原因')
        console.error('permanent delete error =', error)
      },
    },
  )

  const { run: handleRestore, loading: isRestoreLoading } = useRequest(
    (name: string) => {
      return axios.post(askBIApiUrls.xengine.tenant.restore, {
        tenantName: name,
      })
    },
    {
      manual: true,
      onSuccess: () => {
        antdMessage.success('恢复成功')
        loadTenantList({ current, pageSize: 10 })
      },
      onError: (error) => {
        antdMessage.error('恢复租户失败：' + error.message || '未知原因')
        console.error('restore error =', error)
      },
    },
  )

  const { run: handleCreate, loading: isCreateLoading } = useRequest(
    async (params: CreateTenantRequest) => {
      return axios.post<APIResponse<any>>(askBIApiUrls.xengine.tenant.create, params)
    },
    {
      manual: true,
      onSuccess: () => {
        antdMessage.success('创建成功')
        setShowCreateForm(false)
        form.resetFields()
        loadTenantList({ current, pageSize: 10 })
      },
      onError: (error: any) => {
        // 提取具体的错误信息
        const errorMessage = error.response?.data?.message || error.message || '未知错误'
        if (errorMessage.includes('Catalog') && errorMessage.includes('exists')) {
          antdMessage.error('创建租户失败：目录名称已存在')
        } else if (errorMessage.includes('tenant') && errorMessage.includes('exists')) {
          antdMessage.error('创建租户失败：租户名称已存在')
        } else {
          antdMessage.error('创建租户失败：' + errorMessage)
        }
        console.error('Create tenant error =', error)
      },
    },
  )

  const { run: handleUpdate } = useRequest(
    async (params: CreateTenantRequest) => {
      return axios.post<APIResponse<any>>(askBIApiUrls.xengine.tenant.update, params)
    },
    {
      manual: true,
      onSuccess: () => {
        antdMessage.success('更新成功')
        setEditTenantName(undefined)
        setActiveTab('tenant')
        form.resetFields()
        loadTenantList({ current, pageSize: 10 })
      },
      onError: (error: any) => {
        antdMessage.error('更新失败：' + error.message || '未知原因')
        console.error('Update tenant error =', error)
      },
    },
  )

  const { run: handleProjectCreate, loading: isProjectCreateLoading } = useRequest(
    async (params: ProjectCreateRequest) => {
      await axios.post<APIResponse<any>>(askBIApiUrls.xengine.tenant.addProject, params)
      if (params.users) {
        return axios.post<APIResponse<any>>(askBIApiUrls.xengine.tenant.addUserToProject, {
          userName: params.users[0],
          projectName: params.projectName,
          tenantName: editTenantName,
        })
      }
    },
    {
      manual: true,
      onSuccess: () => {
        antdMessage.success('创建子租户成功')
        setShowProjectForm(false)
        projectForm.resetFields()
        if (editTenantName) {
          handleSelectAllProjects(editTenantName)
        }
      },
      onError: (error: any) => {
        const errorMessage = error.response?.data?.message || error.message || '未知错误'
        antdMessage.error('创建子租户失败：' + errorMessage)
        console.error('Create project error =', error)
      },
    },
  )

  const { run: handleSelectAllProjects, data: allProjects } = useRequest(
    async (tenantName: string) => {
      const response = await axios.get(askBIApiUrls.xengine.tenant.getProjects, { params: { tenantName } })
      return response.data.data.projects
    },
    {
      manual: true,
      onError: (error) => {
        antdMessage.error('获取子租户列表失败')
        console.error('Select all projects error =', error)
      },
    },
  )

  const { run: handleProjectDelete } = useRequest(
    async (tenantName: string, projectName: string) => {
      return axios.post(askBIApiUrls.xengine.tenant.deleteProject, { tenantName, projectName })
    },
    {
      manual: true,
      onSuccess: () => {
        antdMessage.success('删除子租户成功')
        loadTenantList()
        if (editTenantName) {
          handleSelectAllProjects(editTenantName)
        }
      },
      onError: (error) => {
        antdMessage.error('删除子租户失败')
        console.error('Delete project error =', error)
      },
    },
  )

  const { run: handleProjectEdit, loading: isProjectEditLoading } = useRequest(
    async (params: ProjectCreateRequest) => {
      return axios.post<APIResponse<any>>(askBIApiUrls.xengine.tenant.updateProject, params)
    },
    {
      manual: true,
      onSuccess: () => {
        antdMessage.success('编辑子租户成功')
        setShowProjectForm(false)
        projectForm.resetFields()
        setMode('create')
        setCurrentProject(null)
        if (editTenantName) {
          handleSelectAllProjects(editTenantName)
        }
      },
      onError: (error: any) => {
        const errorMessage = error.response?.data?.message || error.message || '未知错误'
        antdMessage.error('编辑子租户失败：' + errorMessage)
        console.error('Edit project error =', error)
      },
    },
  )

  const statusMap = {
    NORMAL: '正常',
    DISCARDED: '废弃',
  }

  const tenantsColumn: ColumnsType<Tenant & Id> = [
    {
      title: '租户名称',
      dataIndex: 'tenantName',
      width: 228,
      render(value) {
        return <TextHighlight text={value} highlight={searchValue} />
      },
    },
    // 暂时先注释，后期需求会加
    // {
    //   title: 'CPU',
    //   dataIndex: 'cpuCoreCount',
    //   width: 228,
    // },
    // {
    //   title: '内存',
    //   dataIndex: 'memorySize',
    //   width: 228,
    // },
    // {
    //   title: '存储',
    //   dataIndex: 'diskCapacity',
    //   width: 228,
    //   render: (value: string[]) => (
    //     <div className="grid gap-1">
    //       {value?.map?.((text, index) => (
    //         <span key={index}>
    //           <Tag bordered={false} color="default">
    //             {text}
    //           </Tag>
    //         </span>
    //       ))}
    //     </div>
    //   ),
    // },
    {
      title: '用户数',
      width: 228,
      render: (_, record) => {
        return <span>{record.users?.length}</span>
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTimestamp',
      width: 228,
      render(value) {
        return dayjs(value).format('YYYY-MM-DD HH:mm:ss')
      },
    },
    {
      title: '状态',
      dataIndex: 'state',
      width: 228,
      render(value: keyof typeof statusMap) {
        return <Tag color={value === 'NORMAL' ? 'green' : 'default'}>{statusMap[value]}</Tag>
      },
    },
    {
      title: '操作',
      dataIndex: 'operation',
      width: 60,
      fixed: 'right',
      render: (_, record) => {
        if (record.state === 'DISCARDED') {
          return (
            <Popover
              placement="top"
              content={
                <div className="w-[120px]">
                  <Button
                    type="text"
                    block
                    onClick={() => {
                      setDeletingTenant(record)
                      setShowDeleteModal(true)
                    }}
                  >
                    彻底删除租户资源
                  </Button>
                  <Button
                    type="text"
                    block
                    onClick={() => {
                      setDeletingTenant(record)
                      setShowRestoreModal(true)
                    }}
                  >
                    恢复该租户
                  </Button>
                </div>
              }
            >
              <MoreOutlined className="cursor-pointer" />
            </Popover>
          )
        }

        return (
          <Popover
            placement="top"
            content={
              <div className="w-[74px]">
                <Button
                  type="text"
                  block
                  onClick={() => {
                    setActiveTab('tenant')
                    setEditTenantName(record.tenantName)
                    handleGetAllUsers()
                    form.setFieldsValue({
                      tenantName: record.tenantName,
                      cpuCoreCount: record.cpuCoreCount,
                      memorySize: record.memorySize,
                      diskCapacity: record.diskCapacity,
                      comment: record.comment,
                      users: record.users,
                    })
                    setShowTenantForm(true)
                  }}
                >
                  编辑租户
                </Button>
                <Button
                  type="text"
                  block
                  onClick={() => {
                    setActiveTab('tenant')
                    setDeletingTenant(record)
                    setShowDeleteModal(true)
                  }}
                >
                  删除租户
                </Button>
              </div>
            }
          >
            <MoreOutlined className="cursor-pointer" />
          </Popover>
        )
      },
    },
  ]

  const handleSearch = (value: string) => {
    setSearchValue(value)
  }

  const filteredTenants = React.useMemo(() => {
    return (tenantList?.list ?? []).filter((tenant: Tenant & Id) => {
      return (
        tenant.tenantName?.toLowerCase().includes(searchValue.toLowerCase()) ||
        tenant.comment?.toLowerCase().includes(searchValue.toLowerCase())
      )
    })
  }, [tenantList, searchValue])

  const { run: handleGetAllUsers, data: allUsers } = useRequest(
    async () => {
      const response = await axios.get(askBIApiUrls.xengine.tenant.getAllUsers)
      return response.data.data
    },
    {
      manual: true,
    },
  )

  const handleTabChange = (key: string) => {
    setActiveTab(key as 'tenant' | 'project')
    if (key === 'project' && editTenantName) {
      handleSelectAllProjects(editTenantName)
    }
  }

  const getModalTitle = () => {
    switch (mode) {
      case 'create':
        return '创建子租户'
      case 'edit':
        return '编辑子租户'
      case 'view':
        return '查看子租户'
      default:
        return '子租户'
    }
  }

  const handleEditClick = (record: Project) => {
    setMode('edit')
    setCurrentProject(record)
    projectForm.setFieldsValue(record)
    setShowProjectForm(true)
  }

  const handleViewClick = (record: Project) => {
    setMode('view')
    setCurrentProject(record)
    projectForm.setFieldsValue({
      projectName: record.projectName,
      comment: record.comment,
      users: record.users || [],
    })
    setShowProjectForm(true)
  }

  // 重置表单状态，防止修改一个表单之后，其他表单状态混乱
  const resetTenantForm = () => {
    setShowTenantForm(false)
    setShowCreateForm(false)
    setEditTenantName(undefined)
    setActiveTab('tenant')
    form.resetFields()
  }

  const resetDeleteModalState = () => {
    setShowDeleteModal(false)
    setDeletingTenant(undefined)
    setIsCheckBox(false)
  }

  return (
    <AdminPage title="" className={className}>
      <AdminCard
        title="租户管理"
        actions={
          <Space>
            <Search placeholder="请输入关键字" allowClear onSearch={handleSearch} />
            <Button
              type="primary"
              icon={<PlusOutlined />}
              size="middle"
              onClick={() => {
                setShowCreateForm(true)
                handleGetAllUsers()
              }}
            >
              创建租户
            </Button>
          </Space>
        }
      >
        {tenantListError != null ? (
          <Result status="error" title="获取租户列表失败" subTitle="请稍后再试" />
        ) : (
          <Table
            rowKey={(record) => record.id || record.tenantName}
            columns={tenantsColumn}
            loading={isTenantListLoading}
            dataSource={filteredTenants}
            pagination={{
              hideOnSinglePage: false,
              showSizeChanger: true,
              current,
              total,
              showTotal: (total) => `共 ${total} 个租户`,
              onChange: (page, pageSize) => {
                pageNumberChange(page, pageSize)
              },
            }}
          />
        )}
        <Modal
          title={editTenantName ? '编辑租户' : '创建租户'}
          open={showTenantForm || showCreateForm}
          onCancel={() => {
            resetTenantForm()
          }}
          footer={[
            <Button
              key="cancel"
              className="w-[72px]"
              onClick={() => {
                resetTenantForm()
              }}
            >
              取消
            </Button>,
            <Button
              key="submit"
              type="primary"
              className="w-[72px]"
              loading={isCreateLoading}
              onClick={() => form.submit()}
            >
              确定
            </Button>,
          ]}
          width={517}
          className="tenant-form-modal"
        >
          <Tabs
            activeKey={activeTab}
            onChange={handleTabChange}
            items={[
              {
                key: 'tenant',
                label: '租户管理',
                children: (
                  <Form
                    form={form}
                    layout="horizontal"
                    initialValues={{
                      cpuCoreCount: 50,
                      memorySize: 50,
                      diskCapacity: 50,
                      ifNotExists: false,
                      needCreateCatalog: true,
                    }}
                    className="pl-0 pr-6"
                    onFinish={async (values) => {
                      const params = {
                        tenantName: values.tenantName,
                        catalogName: `${values.tenantName}_catalog`,
                        ifNotExists: true,
                        needCreateCatalog: true,
                        memorySize: '',
                        cpuCoreCount: '',
                        diskCapacity: '',
                        userNames: values.users,
                        comment: values.comment,
                      }

                      if (editTenantName) {
                        try {
                          await handleUpdate(params)
                          setShowTenantForm(false)
                          setEditTenantName(undefined)
                          form.resetFields()
                          loadTenantList()
                        } catch (error: any) {
                          const errorMessage = error.response?.data?.message || error.message || '未知错误'
                          antdMessage.error('编辑租户失败：' + errorMessage)
                        }
                      } else {
                        try {
                          await handleCreate(params)
                        } catch (error) {
                          console.error('Validation failed:', error)
                        }
                      }
                    }}
                  >
                    <div className="space-y-6">
                      {!editTenantName && (
                        <div className="flex items-center gap-2 rounded-md bg-[#F5F5F5] p-4 text-sm text-[#666666]">
                          <InfoCircleOutlined className="text-[#1677FF]" />
                          <span>创建租户前，请先完成节点配置，以确保功能正常使用</span>
                        </div>
                      )}
                      <Form.Item
                        label={<div className="text-[14px] font-normal">租户名称</div>}
                        name="tenantName"
                        rules={[
                          { required: true, message: '请输入租户名称' },
                          {
                            // eslint-disable-next-line no-control-regex
                            pattern: /^[\x00-\x7F]*$/,
                            message: '租户名称不能包含中文字符',
                          },
                        ]}
                        labelCol={{ span: 5 }}
                      >
                        <Input disabled={!!editTenantName} placeholder="请输入租户名称" className="h-[32px]" />
                      </Form.Item>
                      {/* 租户资源配置,后期要添加 */}
                      {/* <Form.Item
                        label={<div className="text-[14px] font-normal">CPU</div>}
                        name="cpuCoreCount"
                        rules={[{ required: true, message: '请输入CPU核心数' }]}
                      >
                        <div className="flex items-center">
                          <span
                            className="flex h-[32px] w-[32px] min-w-[32px] cursor-pointer items-center justify-center rounded-l-sm bg-[#f2f3f5] p-0"
                            onClick={() =>
                              form.setFieldValue('cpuCoreCount', (form.getFieldValue('cpuCoreCount') || 0) - 1)
                            }
                          >
                            -
                          </span>
                          <Form.Item name="cpuCoreCount" noStyle>
                            <InputNumber controls={false} className="rounded-none text-center" min={1} max={150} />
                          </Form.Item>
                          <span
                            className="flex h-[32px] w-[32px] min-w-[32px] cursor-pointer items-center justify-center rounded-r-sm bg-[#f2f3f5] p-0"
                            onClick={() =>
                              form.setFieldValue('cpuCoreCount', (form.getFieldValue('cpuCoreCount') || 0) + 1)
                            }
                          >
                            +
                          </span>
                          <span className="ml-3 text-[14px] text-[#999]">总计 150 C</span>
                        </div>
                      </Form.Item>

                      <Form.Item
                        label={<div className="text-[14px] font-normal">内存</div>}
                        name="memorySize"
                        rules={[{ required: true, message: '请输入内存大小' }]}
                      >
                        <div className="flex items-center">
                          <span
                            className="flex h-[32px] w-[32px] min-w-[32px] cursor-pointer items-center justify-center rounded-l-sm bg-[#f2f3f5] p-0"
                            onClick={() =>
                              form.setFieldValue('memorySize', (form.getFieldValue('memorySize') || 0) - 1)
                            }
                          >
                            -
                          </span>
                          <Form.Item name="memorySize" noStyle>
                            <InputNumber controls={false} className="rounded-none text-center" min={1} max={200} />
                          </Form.Item>
                          <span
                            className="flex h-[32px] w-[32px] min-w-[32px] cursor-pointer items-center justify-center rounded-r-sm bg-[#f2f3f5] p-0"
                            onClick={() =>
                              form.setFieldValue('memorySize', (form.getFieldValue('memorySize') || 0) + 1)
                            }
                          >
                            +
                          </span>
                          <span className="ml-3 text-[14px] text-[#999]">总计 200 G</span>
                        </div>
                      </Form.Item>

                      <Form.Item
                        label={<div className="text-[14px] font-normal">存储</div>}
                        name="diskCapacity"
                        rules={[{ required: true, message: '请输入存储大小' }]}
                      >
                        <div className="flex items-center">
                          <span
                            className="flex h-[32px] w-[32px] min-w-[32px] cursor-pointer items-center justify-center rounded-l-sm bg-[#f2f3f5] p-0"
                            onClick={() =>
                              form.setFieldValue('diskCapacity', (form.getFieldValue('diskCapacity') || 0) - 1)
                            }
                          >
                            -
                          </span>
                          <Form.Item name="diskCapacity" noStyle>
                            <InputNumber controls={false} className="rounded-none text-center" min={1} max={1000} />
                          </Form.Item>
                          <span
                            className="flex h-[32px] w-[32px] min-w-[32px] cursor-pointer items-center justify-center rounded-r-sm bg-[#f2f3f5] p-0"
                            onClick={() =>
                              form.setFieldValue('diskCapacity', (form.getFieldValue('diskCapacity') || 0) + 1)
                            }
                          >
                            +
                          </span>
                          <span className="ml-3 text-[14px] text-[#999]">剩余 1000 G</span>
                        </div>
                      </Form.Item> */}

                      <Form.Item
                        label={<div className="text-[14px] font-normal">租户描述</div>}
                        name="comment"
                        labelCol={{ span: 5 }}
                        initialValue=""
                      >
                        <TextArea placeholder="请输入租户描述" className="min-h-[32px]" />
                      </Form.Item>

                      <Form.Item
                        label={<div className="text-[14px] font-normal">用户管理</div>}
                        name="users"
                        labelCol={{ span: 5 }}
                      >
                        <UserManagement allUsers={allUsers} disabled={mode === 'view'} />
                      </Form.Item>
                    </div>
                  </Form>
                ),
              },
              ...(editTenantName
                ? [
                    {
                      key: 'project',
                      label: '子租户管理',
                      children: (
                        <div className="flex flex-col py-4">
                          {!allProjects?.length ? (
                            <div className="flex flex-col items-center justify-center py-8">
                              <p className="text-center text-gray-600">
                                租户下必须创建一个子租户才能被启用（用户界面中才能被选择到）
                              </p>
                              <Button type="primary" className="mt-4" onClick={() => setShowProjectForm(true)}>
                                创建子租户
                              </Button>
                            </div>
                          ) : (
                            <>
                              <div className="mb-4 flex items-center justify-between px-4">
                                <h3 className="text-base font-medium">子租户列表</h3>
                                <Button type="primary" onClick={() => setShowProjectForm(true)}>
                                  创建子租户
                                </Button>
                              </div>
                              <div className="px-2">
                                <Table
                                  dataSource={allProjects}
                                  rowKey="projectName"
                                  pagination={{
                                    hideOnSinglePage: false,
                                    showSizeChanger: true,
                                    showTotal: (total) => `共 ${total} 个子租户`,
                                  }}
                                  columns={[
                                    {
                                      title: '子租户名称',
                                      dataIndex: 'projectName',
                                      key: 'projectName',
                                      align: 'center',
                                    },
                                    {
                                      title: '子租户描述',
                                      dataIndex: 'comment',
                                      key: 'comment',
                                      align: 'center',
                                      width: 200,
                                      render: (_, record: Project) => (
                                        <div className="max-w-[200px] overflow-hidden">
                                          <span className="block truncate" title={record.comment}>
                                            {record.comment}
                                          </span>
                                        </div>
                                      ),
                                    },
                                    {
                                      title: '操作',
                                      key: 'operation',
                                      align: 'center',
                                      render: (_, record: Project) => (
                                        <div className="flex">
                                          <Button
                                            type="link"
                                            onClick={() => handleViewClick(record)}
                                            className="mx-1 p-0"
                                          >
                                            查看
                                          </Button>
                                          <Button
                                            type="link"
                                            onClick={() => handleEditClick(record)}
                                            className="mx-1 p-0"
                                          >
                                            编辑
                                          </Button>
                                          <Button
                                            type="link"
                                            danger
                                            onClick={() => {
                                              Modal.confirm({
                                                title: '确认删除',
                                                content: `确定要删除子租户 "${record.projectName}" 吗？`,
                                                okText: '确认',
                                                cancelText: '取消',
                                                onOk: () => {
                                                  if (editTenantName) {
                                                    handleProjectDelete(editTenantName, record.projectName)
                                                  } else {
                                                    antdMessage.error('删除失败，请刷新页面重试')
                                                  }
                                                },
                                              })
                                            }}
                                            className="mx-1 p-0"
                                          >
                                            删除
                                          </Button>
                                        </div>
                                      ),
                                    },
                                  ]}
                                />
                              </div>
                            </>
                          )}
                        </div>
                      ),
                    },
                  ]
                : []),
            ]}
          />
        </Modal>
        <Modal
          title="删除确认"
          width={310}
          open={showDeleteModal}
          onCancel={() => {
            resetDeleteModalState()
          }}
          footer={[
            <Button
              key="cancel"
              onClick={() => {
                resetDeleteModalState()
              }}
            >
              取消
            </Button>,
            <Button
              key="submit"
              type="primary"
              loading={deletingTenant?.state === 'DISCARDED' ? isPermanentDeleteLoading : isDeleteLoading}
              onClick={() => {
                if (deletingTenant) {
                  if (deletingTenant.state === 'DISCARDED') {
                    handlePermanentDelete(deletingTenant.tenantName)
                  } else {
                    handleDelete(deletingTenant.tenantName)
                  }
                  resetDeleteModalState()
                }
              }}
              disabled={!isCheckBox && deletingTenant?.state === 'DISCARDED'}
            >
              确定
            </Button>,
          ]}
        >
          <div className="py-2">
            <div className="flex flex-col p-1">
              <div className="flex items-center">
                <SvgIcon icon={tenantDelWarn} className="mr-1" />
                {deletingTenant?.state === 'DISCARDED' ? (
                  <span>彻底删除租户后，租户下的资源将被彻底删除且无法恢复</span>
                ) : (
                  <span>删除租户后，租户下的资源会被冻结</span>
                )}
              </div>
              {deletingTenant?.state === 'DISCARDED' && (
                <Form.Item className="mt-4">
                  <Checkbox checked={isCheckBox} onChange={() => setIsCheckBox(!isCheckBox)}>
                    知道了
                  </Checkbox>
                </Form.Item>
              )}
            </div>
          </div>
        </Modal>
        <Modal
          title="恢复确认"
          width={310}
          open={showRestoreModal}
          onCancel={() => {
            setShowRestoreModal(false)
            setDeletingTenant(undefined)
          }}
          footer={[
            <Button
              key="cancel"
              onClick={() => {
                setShowRestoreModal(false)
                setDeletingTenant(undefined)
              }}
            >
              取消
            </Button>,
            <Button
              key="submit"
              type="primary"
              loading={isRestoreLoading}
              onClick={() => {
                if (deletingTenant) {
                  handleRestore(deletingTenant.tenantName)
                  setShowRestoreModal(false)
                  setDeletingTenant(undefined)
                }
              }}
            >
              确定
            </Button>,
          ]}
        >
          <div className="py-4">
            <div className="flex items-center">
              <SvgIcon icon={tenantRecover} className="mr-1" />
              恢复租户后，租户内用户的资源和租户权限将重新被启用
            </div>
          </div>
        </Modal>
        <Modal
          title={getModalTitle()}
          open={showProjectForm}
          onCancel={() => {
            setShowProjectForm(false)
            setMode('create')
            setCurrentProject(null)
            projectForm.resetFields()
          }}
          footer={
            mode === 'view'
              ? null
              : [
                  <Button
                    key="cancel"
                    onClick={() => {
                      setShowProjectForm(false)
                      setMode('create')
                      setCurrentProject(null)
                      projectForm.resetFields()
                    }}
                  >
                    取消
                  </Button>,
                  <Button
                    key="submit"
                    type="primary"
                    loading={mode === 'create' ? isProjectCreateLoading : isProjectEditLoading}
                    onClick={() => projectForm.submit()}
                  >
                    确定
                  </Button>,
                ]
          }
          width={517}
        >
          <Form
            form={projectForm}
            layout="horizontal"
            onFinish={(values) => {
              if (mode === 'create') {
                handleProjectCreate({ ...values, tenantName: editTenantName })
              } else if (mode === 'edit') {
                handleProjectEdit({
                  ...values,
                  userNames: values.users,
                  tenantName: editTenantName,
                  projectName: currentProject?.projectName,
                })
              }
            }}
          >
            <Form.Item
              label="子租户名称"
              name="projectName"
              rules={[
                { required: true, message: '请输入子租户名称' },
                {
                  // eslint-disable-next-line no-control-regex
                  pattern: /^[\x00-\x7F]*$/,
                  message: '子租户名称不能包含中文字符',
                },
              ]}
              labelCol={{ span: 5 }}
            >
              <Input placeholder="请输入子租户名称" disabled={mode === 'view' || mode === 'edit'} />
            </Form.Item>

            <Form.Item
              label="子租户描述"
              name="comment"
              rules={[{ required: true, message: '请输入子租户描述' }]}
              labelCol={{ span: 5 }}
            >
              <Input placeholder="请输入子租户描述" disabled={mode === 'view'} />
            </Form.Item>

            {/* <Form.Item label="用户管理" name="users" rules={[{ required: true, message: '请选择用户' }]}> */}
            <Form.Item label="用户管理" name="users" labelCol={{ span: 5 }}>
              <UserManagement allUsers={allUsers} disabled={mode === 'view'} />
            </Form.Item>
          </Form>
        </Modal>
      </AdminCard>
    </AdminPage>
  )
}

const UserManagement = ({
  value,
  onChange,
  allUsers,
  disabled,
}: {
  value?: string[]
  onChange?: (values: string[]) => void
  allUsers?: any[]
  disabled?: boolean
}) => {
  const [selectedUsers, setSelectedUsers] = useState<string[]>(value || [])
  const [searchText, setSearchText] = useState('')

  useEffect(() => {
    if (value) {
      setSelectedUsers(value)
    }
  }, [value])

  const handleSelect = (value: string) => {
    if (disabled) return
    const newSelected = [...selectedUsers, value]
    setSelectedUsers(newSelected)
    onChange?.(newSelected)
  }

  const handleDeselect = (value: string) => {
    if (disabled) return
    const newSelected = selectedUsers.filter((user) => user !== value)
    setSelectedUsers(newSelected)
    onChange?.(newSelected)
  }

  const isUserSelected = (user: any) => selectedUsers.includes(user)

  const filteredUsers = useMemo(() => {
    if (!searchText) {
      return allUsers || []
    }
    return (allUsers || []).filter((user) => user.toLowerCase().includes(searchText.toLowerCase()))
  }, [allUsers, searchText])

  return (
    <Select
      mode="multiple"
      placeholder={disabled ? '暂无用户' : '搜索用户添加'}
      className="min-h-[32px] w-full"
      value={selectedUsers}
      onChange={(values) => {
        if (disabled) return
        setSelectedUsers(values)
        onChange?.(values)
      }}
      disabled={disabled}
      showSearch
      searchValue={searchText}
      onSearch={(value) => !disabled && setSearchText(value)}
      dropdownRender={() => (
        <div className="h-[300px] overflow-y-auto rounded-md bg-white p-2 shadow-lg">
          {filteredUsers?.map((user) => (
            <div
              key={user}
              className={`flex cursor-pointer items-center justify-between rounded-md p-2 ${
                disabled ? 'cursor-not-allowed opacity-50' : 'hover:bg-gray-100'
              }`}
              onClick={() => {
                if (!disabled) {
                  if (isUserSelected(user)) {
                    handleDeselect(user)
                  } else {
                    handleSelect(user)
                  }
                }
              }}
            >
              <div className="flex items-center">
                <span>{user}</span>
              </div>
              {isUserSelected(user) ? (
                <div className="flex items-center">
                  <span className="mr-2">已在子租户内 </span>
                  <span className="text-lg text-purple-400">−</span>
                </div>
              ) : (
                <span className="text-lg text-purple-400">+</span>
              )}
            </div>
          ))}
        </div>
      )}
    />
  )
}
