# 执行语法格式检查和单元测试
.package-cache-dirs: &package-cache-dirs
  # Put package cache directories here.
  # Should not include any directories with binaries built from our source code.
- .cache/pip
- .cache/npm
- .bun_cache
- .cache/apk
- .cache/uv

.build-cache-dirs:
  # Put build cache directories here.
  # Should not include any directories with binaries downloaded from the Internet.
- node_modules

.job-defaults: &job-defaults
  dependencies: []
  needs: []

stages:
  - check
  - review
  - test
  - package
  - e2e
  - deploy

variables:
  PIP_INDEX_URL: https://pypi.tuna.tsinghua.edu.cn/simple
  PIP_CACHE_DIR: "$CI_PROJECT_DIR/.cache/pip"
  npm_config_cache: '$CI_PROJECT_DIR/.cache/npm'
  # Global NPM registry config for `npm install -g`.
  npm_config_registry: 'https://registry.npmmirror.com/'
  BUN_INSTALL_CACHE_DIR: "$CI_PROJECT_DIR/.bun_cache"
  APK_CACHE_DIR: "$CI_PROJECT_DIR/.cache/apk"
  APK_REPOSITORY: "https://mirrors.ustc.edu.cn/alpine"
  UV_CACHE_DIR: "$CI_PROJECT_DIR/.cache/uv"
  UV_INDEX_URL: "https://pypi.mirrors.ustc.edu.cn/simple"
  CACHE_FALLBACK_KEY: $CI_DEFAULT_BRANCH-$CI_JOB_NAME

default:
  cache: &global_cache
    # By default, all jobs can cache and reuse packages from the package cache.
    # Using the build cache is only used for jobs of the build stage.
    paths: *package-cache-dirs
    key: "$CI_COMMIT_REF_SLUG-$CI_JOB_NAME"
    policy: pull-push

gitleaks:
  <<: *job-defaults
  stage: check
  image:
    name: "registry.gitlab.dipeak.com/dipeak/generic-repository/zricethezav/gitleaks:keep-v8.27.2"
    entrypoint: [""]
  before_script:
    - if ! git cat-file -t "${CI_MERGE_REQUEST_DIFF_BASE_SHA}"; then git fetch --depth=1 origin "${CI_MERGE_REQUEST_DIFF_BASE_SHA}"; fi
  script:
    # Generate diff with
    # - no context lines
    # - ignore all whitespace changes
    # - filter only added content lines
    # - remove `+` prefix
    # and scan for secrets
    - git diff -U0 --ignore-all-space ${CI_MERGE_REQUEST_DIFF_BASE_SHA}..${CI_COMMIT_SHA} | (grep '^+[^+]' || true) | sed 's/^+//' | gitleaks -v stdin --redact=50 --no-banner
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
      when: always
    - when: never

check-merge-request:
  <<: *job-defaults
  stage: check
  image: registry.gitlab.dipeak.com/dipeak/generic-repository/ubuntu-web:latest
  script:
    - dev/lint/check-merge-request.sh
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
      when: always
    - when: never

.docker-base:
  # from https://gitlab.dipeak.com/dipeak/docker
  image: registry.gitlab.dipeak.com/dipeak/generic-repository/docker:keep-28.2.1-buildx-0.24.0-main-feac3e9-20250530124736
  variables:
    DOCKER_HOST: tcp://docker:2375
    DOCKER_TLS_CERTDIR: ""
  services:
    - name: registry.gitlab.dipeak.com/dipeak/generic-repository/docker:keep-28.2.1-buildx-0.24.0-main-feac3e9-20250530124736
      command: ["--tls=false"]
      alias: docker
  before_script:
    - echo "$GENERIC_REPOSITORY_DEPLOY_PASSWORD" | docker login -u generic-repository-deploy --password-stdin registry.gitlab.dipeak.com
    - mkdir -p "$APK_CACHE_DIR"
    - echo "$APK_REPOSITORY/v$(cat /etc/alpine-release | cut -d'.' -f1-2)/main" > /etc/apk/repositories
    - echo "$APK_REPOSITORY/v$(cat /etc/alpine-release | cut -d'.' -f1-2)/community" >> /etc/apk/repositories
    - apk update
    - apk add --cache-dir "$APK_CACHE_DIR" bash

.node-base:
  image: registry.gitlab.dipeak.com/dipeak/generic-repository/node:20-alpine
  before_script:
    - export http_proxy="http://**************:7890"
    - export https_proxy="http://**************:7890"
    - npm install --prefer-offline -g npm@10.5.0

gpt-review:
  <<: *job-defaults
  stage: review
  image: "registry.gitlab.dipeak.com/dipeak/generic-repository/ubuntu-web:latest"
  variables:
    GITLAB_TOKEN: "$CODE_REVIEW_GPT_GITLAB_TOKEN"
    GITLAB_HOST: "https://gitlab.dipeak.com"
    HTTP_PROXY: "http://**************:7893"
    HTTPS_PROXY: "http://**************:7893"
    NO_PROXY: ".dipeak.com,127.0.0.1,localhost,*********/8,***********/16"
  before_script:
    - if ! git cat-file -t "${CI_MERGE_REQUEST_DIFF_BASE_SHA}"; then git fetch --depth=1
      origin "${CI_MERGE_REQUEST_DIFF_BASE_SHA}"; fi
  script:
    - curl -fsSL https://bun.sh/install | bash
    - export PATH=~/.bun/bin:"$PATH"
    - git clone
      https://gitlab-ci-token:${CI_JOB_TOKEN}@gitlab.dipeak.com/third-party/code-review-gpt.git
    - cd code-review-gpt && bun i && bun run build && npm install -g . && cd ..
    - code-review-gpt review --ci=gitlab
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $CI_DEFAULT_BRANCH'
      when: always
    - when: never
  allow_failure: true

node_test:
  <<: *job-defaults
  extends: .node-base
  stage: test
  script:
    - npm ci --prefer-offline
    - echo "Linting TypeScript files."
    - npm run check-all
    - echo "Finish linting TypeScript files."
    - npm run prisma-generate
    - DISABLE_SQL_GENERATE_BYPASS=false npm run test:once

python_test:
  <<: *job-defaults
  stage: test
  image: registry.gitlab.dipeak.com/dipeak/generic-repository/python:keep-3.11.13-slim-bookworm
  script:
    - sed -i "<EMAIL>@mirrors.ustc.edu.cn@g" /etc/apt/sources.list.d/debian.sources
    - apt-get update && apt-get install -y --no-install-recommends make
    - echo "Linting Python files."
    - dev/lint/format-python.sh --check
    - echo "Finish linting Python files."
    # - pip install -r python/nl2metric/requirements.txt
    # - mkdir -p /resources
    # - curl -fsSL --user "user:MzvkQb8ns_DHcG29KaR2" "https://gitlab.dipeak.com/api/v4/projects/33/packages/generic/askbot/20250210/cached_server-bge-20250210.zip" > python/nl2metric/cached_server.zip
    # - cd python/nl2metric && unzip cached_server.zip && cd -
    # - curl -fsSL --user "user:8sMDSKRKKbsNgk_A9hwu" "https://gitlab.dipeak.com/api/v4/projects/255/packages/generic/ask_bi_nltk/0.0.1/nltk_dir.tar.gz" > python/nl2metric/nltk_dir.tar.gz
    # - cd python/nl2metric && tar -zxvf nltk_dir.tar.gz -C /resources/punkt_dir  && cd -
    # - echo "Set environment variables"
    # - export DATABASE_URL=mysql://root:AE3~sByGLG-.Prhwdpgb@**************:60155/askbi_mix?allowPublicKeyRetrieval=true
    # - export AI_DATA_OPERATOR_DATABASE_URL=mysql://root:AE3~sByGLG-.Prhwdpgb@**************:60155/ai_data_operator?allowPublicKeyRetrieval=true
    # - export ASK_BI_HOST="http://***************:8000"
    # - export ENABLE_NL2DOCUMENT="False"
    # - export ENABLE_NL2DOCUMENT_BUILDER="False"
    # - export ENABLE_REPORT_GENERATE='False'
    # - export embedding_service_model=BGE
    # - export NLTK_DATA=/resources/punkt_dir
    # - export embedding_model=embedding_service_api
    # - echo "Starting main_embedding_service.py in the background."
    # - python python/nl2metric/main_embedding_service.py &
    # - sleep 5
    # - echo "Running pytest to execute Python unit tests."
   # - pytest python/nl2metric/tests/mock_tests/

ansible_test:
  <<: *job-defaults
  stage: test
  image: registry.gitlab.dipeak.com/dipeak/generic-repository/python:keep-3.11.12-slim-bookworm
  script:
    - cd deploy/ansible
    # We need this step due to `FF_DISABLE_UMASK_FOR_DOCKER_EXECUTOR`.
    # See https://github.com/stackhpc/kayobe-automation/issues/2
    # and https://gitlab.com/gitlab-org/gitlab-runner/-/blob/d1ad54103350cacf52f2e1134edbfb836914dc83/docs/configuration/feature-flags.md
    - chmod -R o-w . && umask 0022
    - ./test-ci.sh

package_all_image:
  <<: *job-defaults
  extends: .docker-base
  stage: package
  script:
    - echo "CI_REGISTRY is $CI_REGISTRY"
    # Install curl and yq for YAML manipulation in build script
    - apk add --cache-dir "$APK_CACHE_DIR" curl
    - curl --user "user:MzvkQb8ns_DHcG29KaR2" -fsSL https://gitlab.dipeak.com/api/v4/projects/33/packages/generic/yq/4.44.3/yq_linux_amd64 -o /usr/local/bin/yq
    - chmod +x /usr/local/bin/yq
    - deploy/script/build_and_push.sh
  artifacts:
    when: always
    expire_in: 1 day
    paths:
      - logs/
      - image_tag.txt

.integration-test-base:
  <<: *job-defaults
  extends: .docker-base
  stage: e2e
  dependencies:
    - package_all_image
  needs:
    - package_all_image
  script:
    # Install basic tools and create Python venv for E2E testing
    - apk add --cache-dir "$APK_CACHE_DIR" curl python3 uv
    - uv venv /opt/venv
    - source /opt/venv/bin/activate
    # Install yq for YAML manipulation
    - curl --user "user:MzvkQb8ns_DHcG29KaR2" -fsSL https://gitlab.dipeak.com/api/v4/projects/33/packages/generic/yq/4.44.3/yq_linux_amd64 -o /usr/local/bin/yq
    - chmod +x /usr/local/bin/yq
    # We need this step due to `FF_DISABLE_UMASK_FOR_DOCKER_EXECUTOR`.
    # See https://github.com/stackhpc/kayobe-automation/issues/2
    # and https://gitlab.com/gitlab-org/gitlab-runner/-/blob/d1ad54103350cacf52f2e1134edbfb836914dc83/docs/configuration/feature-flags.md
    - chmod -R o-w deploy/ansible && umask 0022
    - echo "🚀 Starting E2E integration test..."
    # Read image tag from previous job's artifact
    - IMAGE_TAG=$(cat image_tag.txt)
    - ./dev/e2e-test/run.sh "${IMAGE_TAG}" "${TEST_SUITE_NAME}" "${TEST_ENV_NAME}"
  artifacts:
    when: always
    expire_in: 3 day
    paths:
      - deploy/ansible/inventories/e2e-test/
      - dev/e2e-test/deploy-root/**/*log*/**
      - python/algo/evaluation/data/*result*.csv
      - dev/e2e-test/ui_auto_test/allure-results/
      - dev/e2e-test/ui_auto_test/logs/

api-test:
  extends: .integration-test-base
  variables:
    TEST_SUITE_NAME: "api_test"
    TEST_ENV_NAME: "e2e-test"

eval-test:
  extends: .integration-test-base
  variables:
    TEST_SUITE_NAME: "eval_test"
    TEST_ENV_NAME: "eval-test"
    FEISHU_WEBHOOK: "https://open.feishu.cn/open-apis/bot/v2/hook/ec08e440-f286-426d-8a9a-9268804c3f30,https://open.feishu.cn/open-apis/bot/v2/hook/837c04c5-4698-4a7d-94ea-b94d37572efd"
    FEISHU_AT_OPENID: "ou_3af5ee7ba1699079b4825bf67172314a"
  timeout: 5h
  rules:
    - if: '$ENABLE_EVAL_TEST == "1"'
      when: always
    - when: never

#ui-auto-test:
#  extends: .integration-test-base
#  variables:
#    TEST_ENV_NAME: "e2e-test"
#    TEST_SUITE_NAME: "ui_auto_test"

deploy_env:
  <<: *job-defaults
  stage: deploy
  image: registry.gitlab.dipeak.com/dipeak/generic-repository/python:keep-3.11.12-slim-bookworm
  script:
    - cd deploy/ansible
    # We need this step due to `FF_DISABLE_UMASK_FOR_DOCKER_EXECUTOR`.
    # See https://github.com/stackhpc/kayobe-automation/issues/2
    # and https://gitlab.com/gitlab-org/gitlab-runner/-/blob/d1ad54103350cacf52f2e1134edbfb836914dc83/docs/configuration/feature-flags.md
    - chmod -R o-w . && umask 0022
    - echo "Running deploy for DEPLOY_ENV=$DEPLOY_ENV, XENGINE=$XENGINE_TAG, ASKBI=$ASKBI_TAG"
    - ./setup.sh
    - chmod +x set_image.sh
    - ./set_image.sh
    - ./check.sh
    - sed -i "<EMAIL>@mirrors.ustc.edu.cn@g" /etc/apt/sources.list.d/debian.sources
    - apt-get update && apt-get install -y --no-install-recommends openssh-client
    - mkdir -p ~/.ssh && cp "${DEPLOY_PRIVATE_KEY}" ~/.ssh/id_rsa && chmod 600 ~/.ssh/id_rsa
    - ANSIBLE_HOST_KEY_CHECKING=False ./deploy.sh $DEPLOY_ENV
  rules:
    - if: '$ENABLE_DEPLOY_ENV == "1"'
      when: always
    - when: never

